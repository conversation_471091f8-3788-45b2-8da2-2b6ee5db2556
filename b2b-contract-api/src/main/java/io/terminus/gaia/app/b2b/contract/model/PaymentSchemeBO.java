package io.terminus.gaia.app.b2b.contract.model;

import io.terminus.gaia.app.b2b.contract.tmodel.PaymentSchemeNodeTO;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.FieldType;
import io.terminus.trantorframework.api.annotation.Model;
import io.terminus.trantorframework.api.annotation.typemeta.LinkMeta;
import io.terminus.trantorframework.api.annotation.typemeta.TextMeta;
import lombok.Data;

import java.util.List;

/**
 * 付款方案
 *
 * <AUTHOR>
 */
@Model(
    name = "付款方案",
    mainField = "code"
)
@Data
public class PaymentSchemeBO extends BaseModel<Long> {

    @Field(name = "关联协议")
    @LinkMeta
    private AgreementBO agreementBO;

    @Field(name = "编码")
    @TextMeta(rule = "STRING(PS)+TIMES(YYMMdd)+INCRE(1,8,4,1)")
    private String code;

    @Field(name = "名称")
    private String name;

    @Field(name = "付款节点")
    private String title;

    @Field(name = "方案描述")
    @TextMeta(length = 2048)
    private String description;

    @Field(name = "是否被应用", defaultValue = "false")
    private Boolean apply;

    @Field(name = "是否协议外付款方案", defaultValue = "false")
    private Boolean externalPaymentScheme;

    @Field(name = "付款节点", type = FieldType.Json)
    @TextMeta(length = 4096)
    private List<PaymentSchemeNodeTO> schemeNodes;


    @Field(name = "付款方案模版")
    @LinkMeta
    private PaymentSchemeTemplateBO paymentSchemeTemplateBO;
}
