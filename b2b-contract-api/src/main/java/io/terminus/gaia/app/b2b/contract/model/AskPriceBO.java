package io.terminus.gaia.app.b2b.contract.model;

import io.terminus.gaia.app.b2b.contract.dict.AskPriceStatusDict;
import io.terminus.gaia.app.b2b.contract.tmodel.AskSupplierPriceReplyTO;
import io.terminus.gaia.app.b2b.contract.tmodel.RoundInfoTO;
import io.terminus.gaia.md.model.BrandBO;
import io.terminus.gaia.md.model.CategoryBO;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.gaia.organization.model.DepartmentBO;
import io.terminus.gaia.organization.model.ext.ProjectBO;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.*;
import io.terminus.trantorframework.api.annotation.typemeta.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 运营端:询价单
 *
 * <AUTHOR>
 */
@Model(
        name = "运营端-询价单",
        mainField = "name",
        fieldGroups = {
                @FieldGroup(type = FieldGroupType.DEFAULT_SHOW, fieldName = {"code", "name"})
        },
        config = @ModelConfig(
                enableExport = true),
        indexes = {
                @Index(columns = "code", unique = true),
                @Index(columns = "name")
        }
)
@Data
@EqualsAndHashCode(callSuper = true)
public class AskPriceBO extends BaseModel<Long> {
    @Field(name = "需求池", desc = "需求池")
    @LinkMeta
    private RequestPurchaseBO requestPurchaseBO;

    @Field(name = "询价单编号", desc = "询价单编号")
    @TextMeta(rule = "STRING(XJ)+TIMES(yyyyMMdd)+INCRE(1,4,4,1)")
    private String code;

    @Field(name = "询价单名称", desc = "询价单名称")
    private String name;

    @Field(name = "状态", desc = "状态")
    @DictionaryMeta(value = AskPriceStatusDict.class)
    private String status;

    @Field(name = "分类", desc = "分类id")
    @LinkMeta
    private CategoryBO categoryBO;

    @Field(name = "类目名称", desc = "类目名称")
    private String categoryName;

    @Field(name = "项目", desc = "项目id")
    @LinkMeta
    private ProjectBO projectBO;

    @Field(name = "项目名称", desc = "项目名称")
    private String projectName;

    /**
     * 项目所属部门
     */
    @Field(name = "项目所属组织", desc = "项目所属组织")
    @LinkMeta
    private DepartmentBO departmentBO;

    /**
     * 项目部门所属子企业单位名称
     */
    @Field(name = "项目部门所属子企业单位名称")
    private String enterpriseName;

    /**
     * 来源：采购方提的需求->需求池->询价
     */
    @Field(name = "铜基价（含税）", desc = "铜基价（含税）")
    private BigDecimal basePriceWithTax;

//    @Field(name = "需求品牌/供应商", desc = "需求品牌/供应商")
//    @LookupMeta(linkField = AskPriceBrandRelBO.askPriceBO_field)
//    @JunctionMeta(model = AskPriceBrandRelBO.class, lookupField = AskPriceBrandRelBO.brandBO_field)
//    private List<AskPriceBrandRelBO> askPriceBrandRelBOList;

    @Field(name = "需求品牌", desc = "需求品牌")
    @LookupMeta(linkField = AskPriceBrandRelBO.askPriceBO_field)
    @JunctionMeta(model = AskPriceBrandRelBO.class, lookupField = AskPriceBrandRelBO.brandBO_field)
    private List<BrandBO> brandBOList;

    @Field(name = "需求供应商", desc = "需求供应商")
    @LookupMeta(linkField = AskPriceBrandRelBO.askPriceBO_field)
    @JunctionMeta(model = AskPriceBrandRelBO.class, lookupField = AskPriceBrandRelBO.entityBO_field)
    private List<EntityBO> entityBOList;

    @Field(name = "询价单清单行", desc = "询价单清单行")
    @LookupMeta(linkField = AskPriceLineBO.id_field)
    private List<AskPriceLineBO> askPriceLineBOList;

    /**
     * 供应商报价信息
     * 作用：详情底部展示询价回执结果（state、rejectReason）
     */
    @Field(name = "供应商报价单", desc = "供应商报价单")
    @LookupMeta(linkField = AskSupplierPriceBO.id_field)
    private List<AskSupplierPriceBO> askSupplierPriceBOList;

    @Field(name = "备注")
    private String remark;

    @Field(name = "当前轮数", desc = "当前轮数")
    private Integer currentRound;

    @Field(name = "询价结果回执", desc = "询价结果回执", type = FieldType.Json)
    private List<AskSupplierPriceReplyTO> replyTOList;

    /**
     * @param id id
     * @return AskPriceBO
     */
    public static AskPriceBO of(Long id) {
        if (id == null) {
            return null;
        }
        AskPriceBO askPriceBO = new AskPriceBO();
        askPriceBO.setId(id);
        return askPriceBO;
    }

}
