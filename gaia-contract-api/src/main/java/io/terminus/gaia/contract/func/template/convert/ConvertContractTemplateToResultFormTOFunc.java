package io.terminus.gaia.contract.func.template.convert;

import io.terminus.gaia.contract.tmodel.template.ContractTemplateTO;
import io.terminus.gaia.partner.tmodel.ResultFormTO;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * 将合同模版信息转化为ResultFormTO对象
 * <AUTHOR>
 * @Date 2021/4/30
 */
@Function(name = "convert contract template to ResultFormTO")
public interface ConvertContractTemplateToResultFormTOFunc {

    /**
     * 将合同模版信息转化为ResultFormTO对象
     * @param contractTemplateTO
     * @return ResultFormTO
     */
    ResultFormTO execute(ContractTemplateTO contractTemplateTO);


}
