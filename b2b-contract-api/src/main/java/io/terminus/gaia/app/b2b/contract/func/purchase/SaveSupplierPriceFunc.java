package io.terminus.gaia.app.b2b.contract.func.purchase;

import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseLineBO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.Function;

import java.util.List;

/**
 * @interface_name: SaveSupplierPriceFunc
 * @desc:
 * @date: 2025/7/7 : 17:45
 * @author: Chonor
 **/
@Function
public interface SaveSupplierPriceFunc {

    BooleanResult execute(List<RequestPurchaseLineBO> requestPurchaseLineBOList);
}
