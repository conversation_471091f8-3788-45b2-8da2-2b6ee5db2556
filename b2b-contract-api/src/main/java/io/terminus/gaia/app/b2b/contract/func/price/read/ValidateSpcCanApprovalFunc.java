package io.terminus.gaia.app.b2b.contract.func.price.read;

import io.terminus.gaia.app.b2b.contract.model.price.SalePriceCalBO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.Function;
import io.terminus.trantorframework.api.annotation.FunctionImpl;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/3/25 15:24
 */
@Function
public interface ValidateSpcCanApprovalFunc {

    BooleanResult execute(List<SalePriceCalBO> req);

}
