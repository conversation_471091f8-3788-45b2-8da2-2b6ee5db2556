package io.terminus.gaia.app.b2b.contract.tmodel;

import io.terminus.gaia.partner.model.form.FormGroupBO;
import io.terminus.gaia.partner.model.form.FormLineBO;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.FieldType;
import io.terminus.trantorframework.api.annotation.TransientModel;
import lombok.*;

import java.util.List;

/**
 * 查询模板信息
 *
 * <AUTHOR> 韩俊文(柚屿)
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TransientModel(name = "contract template info TO")
public class ContractTemplateInfoTO extends BaseModel<Long> {

    private static final long serialVersionUID = -3212803556337545725L;

    /**
     * 分组
     */
    @Field(name = "Group detail", type = FieldType.LinkMany)
    private List<FormGroupBO> formGroupList;

    /**
     * 表单项
     */
    @Field(name = "Form detail", type = FieldType.LinkMany)
    private List<FormLineBO> formLineList;
}
