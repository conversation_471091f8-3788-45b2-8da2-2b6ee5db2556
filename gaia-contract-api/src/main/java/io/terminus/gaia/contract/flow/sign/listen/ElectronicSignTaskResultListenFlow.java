package io.terminus.gaia.contract.flow.sign.listen;

import io.terminus.gaia.contract.tmodel.sign.ElectronicSignResultTO;
import io.terminus.trantorframework.api.annotation.AnonymousAccess;
import io.terminus.trantorframework.api.annotation.Flow;

/**
 * 乙方签署成功后电子签署结果回调
 *
 * <AUTHOR>
 */
@Flow(name = "electronic sign task result listen flow")
@AnonymousAccess
public interface ElectronicSignTaskResultListenFlow {

    void execute (ElectronicSignResultTO electronicSignResultTO);
}
