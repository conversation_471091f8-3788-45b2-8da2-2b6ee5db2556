<View title="批量新建清单价格方案" forModel="b2b_contract_AgreementScopePriceTO" type="Form" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://trantor-docs-dev.app.terminus.io/static/v0.17.x/schema/base.xsd">

    <Steps current="2">
        <Step title="选择清单" key="1" />
        <Step title="设置价格" key="2" />
    </Steps>

    <Form key="base" model="b2b_contract_AgreementScopePriceTO" title="价格方案配置" dataFunc="b2b_contract_TransferAgreementDetailFunc"
          dataParams="{ids: env.ids}" onDataLoaded="#{onBaseFormLoaded}">

        <Fields>
            <Field name="supplier" label="使用单位">
                <Validations>
                    <Validation required="#{true}" message="使用单位不能为空"/>
                </Validations>
                <RenderType>
                    <CascadeModelSelect isLeafField="isLeaf" depthLimit="6" parentField="department"
                                        columnTitles="#{['一级部门', '二级部门', '三级部门', '四级部门','五级部门','六级部门']}"
                                        dataSource="organization_DepartmentBO_DepartmentTreeProvider"
                                        dataParams="#{{departmentStatusDict:'ENABLED'}}"
                                        model="organization_DepartmentBO" valueField="id"
                                        labelField="departmentName" modalTitle="选择部门" modalWidth="1000"
                                        searchInputWidth="600"/>
                </RenderType>
            </Field>

            <Field name="pricingBasis" label="计价标准">
                <Validations>
                    <Validation
                            required="#{true}"
                            message="计价标准不能为空"/>
                </Validations>
            </Field>
            <Field name="calculatePoint" label="价格计算节点">
                <Validations>
                    <Validation
                            required="#{true}"
                            message="价格计算节点不能为空"/>
                </Validations>
                <RenderType>
                    <Select allowValues="#{values => values.filter(item => ['SIGN_UP','PLACE_ORDER','TAKE_DELIVERY'].includes(item))}"/>
                </RenderType>
            </Field>
            <Field name="schemeTemplate" label="方案模板" initValue="AREA_PRICE" readonly="true">
                <Validations>
                    <Validation
                            required="#{true}"
                            message="方案模板不能为空"/>
                </Validations>
            </Field>
            <Field label="区域定价方式" name="ladderPriceType">
                <Validations>
                    <Validation required="#{true}" message="区域定价方式不能为空"/>
                </Validations>
                <RenderType>
                    <Radio direction="horizontal"  allowValues="#{values => values.filter(item => ['MULTIPLICATION_PRICE','SUBTRACTION_PRICE', 'CUSTOMIZE_PRICE'].includes(item))}"/>
                </RenderType>
            </Field>
            <Field label="付款方案" name="paymentScheme" show="#{env.type == 'match_making'}">
                <Validations>
                    <Validation required="#{env.type == 'match_making'}" message="撮合协议付款方案不能为空"/>
                </Validations>
                <RenderType>
                    <ModelSelect modalTitle="关联付款方案" model="b2b_contract_PaymentSchemeBO"  dataCondition="agreementBO = ?"
                                 dataParams="[env.agreementId]">
                        <Fields>
                            <Field name="id" show="false"/>
                            <Field name="code" label="付款方案编码"/>
                            <Field name="title" label="付款节点"/>
                            <Field name="description" label="付款方案描述"/>
                        </Fields>
                    </ModelSelect>
                </RenderType>
            </Field>

        </Fields>


        <Actions>
            <Action layout="Footer" type="Cancel" action="#{goBackToTheList}"/>
            <Action layout="Footer" label="上一步"  action="GoBack"/>
            <Action type="Submit" label="确定生成价格方案" layout="Footer" logicFunc="b2b_contract_BatchCreateAgreementDetailPriceSchemeFunc" after="#{goBackToTheList}"/>
        </Actions>
    </Form>

    <frontend_PriceAreaSchemeBatchCreate title="设置价格系数N" model="b2b_contract_AgreementDetailPriceLine" lookupFrom="base.lines">
        <Fields>
            <Field name="agreementDetail.outerAgreementName"/>
            <Field name="agreementDetail.spuBO"/>
            <Field name="agreementDetail.thingMaterial"/>
            <Field name="agreementDetail.thingSize"/>
            <Field name="agreementDetail.attribute"/>
            <Field name="details.logic"/>
            <Field name="details.purchasingAreas"/>
        </Fields>
    </frontend_PriceAreaSchemeBatchCreate>

</View>