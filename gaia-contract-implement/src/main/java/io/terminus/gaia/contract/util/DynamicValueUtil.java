package io.terminus.gaia.contract.util;

import com.google.common.collect.Maps;
import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.gaia.contract.msg.template.ContractTemplateExMsg;
import io.terminus.gaia.contract.tmodel.trade.TradeOrderLineTO;
import io.terminus.gaia.md.model.*;
import io.terminus.gaia.organization.model.BusinessGroupBO;
import io.terminus.gaia.organization.model.DepartmentBO;
import io.terminus.gaia.organization.model.EmployeeBO;
import io.terminus.gaia.partner.utils.DateUtils;
import io.terminus.gaia.partner.utils.DynamicDataUtil;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.Transient;
import io.terminus.trantorframework.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/5/6
 */
@Slf4j
public class DynamicValueUtil {

    public final static Map<String, String> fieldNameMap = Maps.newHashMap();

    static {
        // 合同主体
        fieldNameMap.put("ContractBO.partyA", EntityBO.entityName_field);
        // 签约人
        fieldNameMap.put("ContractBO.signatory", EmployeeBO.employeeName_field);
        // 签约日期
        fieldNameMap.put("ContractBO.signDate", ContractBO.signDate_field);
        // 货币 ContractBO.currency
        fieldNameMap.put("ContractBO.currency", CurrencyBO.currencyName_field);
        // 含税签约金额 ContractBO.contractTaxAmt
        fieldNameMap.put("ContractBO.contractTaxAmt", ContractBO.contractTaxAmt_field);
        // 未含税签约金额 ContractBO.contractAmt
        fieldNameMap.put("ContractBO.contractAmt", ContractBO.contractAmt_field);
        // 生效日期 ContractBO.startDate
        fieldNameMap.put("ContractBO.startDate", ContractBO.startDate_field);
        // 终止日期 ContractBO.endDate
        fieldNameMap.put("ContractBO.endDate", ContractBO.endDate_field);
        // 合同乙方 ContractBO.partyB
        fieldNameMap.put("ContractBO.partyB", EntityBO.entityName_field);
        // 其他合同类型 ContractBO.partyCList
        fieldNameMap.put("ContractBO.partyCList", EntityBO.entityName_field);
        // 业务类型 ContractBO.businessTypeDict
        fieldNameMap.put("ContractBO.businessTypeDict", ContractBO.businessTypeDict_field);
        // 所属公司 ContractBO.company
        fieldNameMap.put("ContractBO.company", EntityBO.company_field);
        // 所属组织 ContractBO.purchaserOrganization
        fieldNameMap.put("ContractBO.purchaserOrganization", BusinessGroupBO.bizGroupName_field);
        // 所属实体
        fieldNameMap.put("ContractBO.site", SiteBO.siteName_field);
        // 是否多方签署
        fieldNameMap.put("ContractBO.isMultiParty", ContractBO.isMultiParty_field);
        //协议品牌
        fieldNameMap.put("ContractBO.agreementBrandNameStr", ContractBO.agreementBrandNameStr_field);
        //订单品牌
        fieldNameMap.put("ContractBO.orderBrandNameStr", ContractBO.orderBrandNameStr_field);
        //分类
        fieldNameMap.put("ContractBO.categoryBOList", CategoryBO.categoryName_field);
        //价格浮动系数
        fieldNameMap.put("ContractBO.priceRatio", ContractBO.priceRatio_field);
        //适用范围
        fieldNameMap.put("ContractBO.serviceAreaList", DepartmentBO.departmentName_field);
        //是否有保证金/保函
        fieldNameMap.put("ContractBO.isHaveGuarantee", ContractBO.isHaveGuarantee_field);
        //保函类型
        fieldNameMap.put("ContractBO.guaranteeTypeDict", ContractBO.guaranteeTypeDict_field);
        //保证金/保函比例
        fieldNameMap.put("ContractBO.guaranteeRatio", ContractBO.guaranteeRatio_field);
        //商票期限
        fieldNameMap.put("ContractBO.commercialTicketPeriod", ContractBO.commercialTicketPeriod_field);
        //商票贴息比例
        fieldNameMap.put("ContractBO.commercialTicketDiscount", ContractBO.commercialTicketDiscount_field);
        //商票支付比例
        fieldNameMap.put("ContractBO.commercialBillPayment", ContractBO.commercialBillPayment_field);
        //商票经济条款
        fieldNameMap.put("ContractBO.economicClause", ContractBO.economicClause_field);
        //甲供支付方式
        fieldNameMap.put("ContractBO.partyAPayWay", ContractBO.partyAPayWay_field);
        //甲指乙供三方支付方式
        fieldNameMap.put("ContractBO.partyCPayWay", ContractBO.partyCPayWay_field);
        //安装支付方式
        fieldNameMap.put("ContractBO.installPayWay", ContractBO.installPayWay_field);
        //合同名称
        fieldNameMap.put("ContractBO.contractName", ContractBO.contractName_field);
        //项目名称
        fieldNameMap.put("ContractBO.projectName", ContractBO.projectName_field);
        //项目分期名称
        fieldNameMap.put("ContractBO.projectStageName", ContractBO.projectStageName_field);
        //订单名称
        fieldNameMap.put("ContractBO.orderName", ContractBO.orderName_field);
        //协议名称
        fieldNameMap.put("ContractBO.agreementName", ContractBO.agreementName_field);
        //协议编码
        fieldNameMap.put("ContractBO.agreementCode", ContractBO.agreementCode_field);
        //订单含税金额
        fieldNameMap.put("ContractBO.orderNeedPay", ContractBO.orderNeedPay_field);
        //订单不含税金额
        fieldNameMap.put("ContractBO.orderNeedPayWithOutTax", ContractBO.orderNeedPayWithOutTax_field);
        //订单税额
        fieldNameMap.put("ContractBO.orderTax", ContractBO.orderTax_field);
        //发票税率
        fieldNameMap.put("ContractBO.billTax", ContractBO.billTax_field);
        //货币单位
        fieldNameMap.put("ContractBO.currencyUnit", ContractBO.currency_field);
        //合同类型
        fieldNameMap.put("ContractBO.contractTypeName", ContractBO.contractTypeName_field);
        //合同分类
        fieldNameMap.put("ContractBO.contractCategoryName", ContractBO.contractCategoryName_field);
        //计价模式
        fieldNameMap.put("ContractBO.feeTypeName", ContractBO.feeTypeName_field);
        //支付方式
        fieldNameMap.put("ContractBO.payTypeName", ContractBO.payTypeName_field);
        //商票比例
        fieldNameMap.put("ContractBO.commercialDiscount", ContractBO.commercialDiscount_field);
        //甲方名称
        fieldNameMap.put("ContractBO.partyAName", ContractBO.partyAName_field);
        //乙方名称
        fieldNameMap.put("ContractBO.partyBName", ContractBO.partyBName_field);
        //开票方名称
        fieldNameMap.put("ContractBO.billPartyName", ContractBO.billPartyName_field);
        //发票类型
        fieldNameMap.put("ContractBO.invoiceTypeName", ContractBO.invoiceTypeName_field);
        //收货地点
        fieldNameMap.put("ContractBO.receivingAddress", ContractBO.receivingAddress_field);
        //供货周期
        fieldNameMap.put("ContractBO.supplyCycle", ContractBO.supplyCycle_field);
        //经济条款
        fieldNameMap.put("ContractBO.orderEconomicClause", ContractBO.orderEconomicClause_field);
        //备注
        fieldNameMap.put("ContractBO.orderRemark", ContractBO.orderRemark_field);
        //供应品类信息
        fieldNameMap.put("ContractBO.categoryInfo", ContractBO.categoryInfo_field);
        //序号
        fieldNameMap.put("ContractBO.seqNo", ContractBO.seqNo_field);
        //物料名称
        fieldNameMap.put("ContractBO.thingName", ContractBO.thingName_field);
        //规格
        fieldNameMap.put("ContractBO.thingSize", ContractBO.thingSize_field);
        //型号
        fieldNameMap.put("ContractBO.thingUnit", ContractBO.thingUnit_field);
        //材料描述
        fieldNameMap.put("ContractBO.thingDesc", ContractBO.thingDesc_field);
        //材料类型
        fieldNameMap.put("ContractBO.thingCategory", ContractBO.thingCategory_field);
        //适用范围
        fieldNameMap.put("ContractBO.scopeArea", ContractBO.scopeArea_field);
        //单位
        fieldNameMap.put("ContractBO.unit", ContractBO.unit_field);
        //单价
        fieldNameMap.put("ContractBO.price", ContractBO.price_field);
        //数量
        fieldNameMap.put("ContractBO.thingQuantity", ContractBO.thingQuantity_field);
        //金额(含税)
        fieldNameMap.put("ContractBO.thingAmounts", ContractBO.thingAmounts_field);
        //协议名称
        fieldNameMap.put("ContractBO.contractName", ContractBO.contractName_field);
        //订单含税金额(大写)
        fieldNameMap.put("ContractBO.orderNeedPayUp", ContractBO.orderNeedPayUp_field);
        //订单不含税金额(大写)
        fieldNameMap.put("ContractBO.orderNeedPayWithOutTaxUp", ContractBO.orderNeedPayWithOutTaxUp_field);
        //支付条款
        fieldNameMap.put("ContractBO.payClause", ContractBO.payClause_field);
        //协议乙方
        fieldNameMap.put("ContractBO.agreementPartyB", ContractBO.agreementPartyB_field);
        //档次
        fieldNameMap.put("ContractBO.grade", ContractBO.grade_field);
        //集采协议起始日期
        fieldNameMap.put("ContractBO.agreementStartDate", ContractBO.agreementStartDate_field);
        //集采协议终止日期
        fieldNameMap.put("ContractBO.agreementEndDate", ContractBO.agreementEndDate_field);
        //合同金额小写
        fieldNameMap.put("ContractBO.orderNeedPayDown", ContractBO.orderNeedPayDown_field);
        //合同金额不含税小写
        fieldNameMap.put("ContractBO.orderNeedPayWithOutTaxDown", ContractBO.orderNeedPayWithOutTaxDown_field);
        //税额大写
        fieldNameMap.put("ContractBO.orderTaxUp", ContractBO.orderTaxUp_field);
        //税额小写
        fieldNameMap.put("ContractBO.orderTaxDown", ContractBO.orderTaxDown_field);
        //保修年限
        fieldNameMap.put("ContractBO.warrantyPeriod", ContractBO.warrantyPeriod_field);
        //安装保修年限
        fieldNameMap.put("ContractBO.installationWarrantyPeriod", ContractBO.installationWarrantyPeriod_field);
        //城市名称
        fieldNameMap.put("ContractBO.cityName", ContractBO.cityName_field);
        //订单创建日期（年）
        fieldNameMap.put("ContractBO.orderCreateYear", ContractBO.orderCreateYear_field);
        //订单创建日期（月）
        fieldNameMap.put("ContractBO.orderCreateMonth", ContractBO.orderCreateMonth_field);
        //订单创建日期（日）
        fieldNameMap.put("ContractBO.orderCreateDay", ContractBO.orderCreateDay_field);
        //甲方地址
        fieldNameMap.put("ContractBO.partyALocation", ContractBO.partyALocation_field);
        //乙方地址
        fieldNameMap.put("ContractBO.partyBLocation", ContractBO.partyBLocation_field);
        //乙方开户银行
        fieldNameMap.put("ContractBO.partyBBankName", ContractBO.partyBBankName_field);
        //乙方户名
        fieldNameMap.put("ContractBO.partyBBankUserName", ContractBO.partyBBankUserName_field);
        //乙方开户账号
        fieldNameMap.put("ContractBO.partyBBankAccount", ContractBO.partyBBankAccount_field);

        //协议甲方地址
        fieldNameMap.put("ContractBO.agreementPartyALocation", ContractBO.agreementPartyALocation_field);
        //协议乙方地址
        fieldNameMap.put("ContractBO.agreementPartyBLocation", ContractBO.agreementPartyBLocation_field);
        //协议生效年份
        fieldNameMap.put("ContractBO.agreementStartYear", ContractBO.agreementStartYear_field);
        //协议终止年份
        fieldNameMap.put("ContractBO.agreementEndYear", ContractBO.agreementEndYear_field);
        //协议产品税率
        fieldNameMap.put("ContractBO.agreementTax", ContractBO.agreementTax_field);
        //协议保修期
        fieldNameMap.put("ContractBO.agreementGuaranteeLimit", ContractBO.agreementGuaranteeLimit_field);
        //协议保修期(月)
        fieldNameMap.put("ContractBO.guaranteeLimit", ContractBO.guaranteeLimit_field);
        //增值税率
        fieldNameMap.put("ContractBO.taxRateStr", ContractBO.taxRateStr_field);
        //订单编码
        fieldNameMap.put("ContractBO.tradeContractOrderCode", ContractBO.tradeContractOrderCode_field);
        //协议保函类型
        fieldNameMap.put("ContractBO.guaranteeTypeStrForTemplate", ContractBO.guaranteeTypeStrForTemplate_field);

    }

    /**
     * 获取值来源value
     *
     * @param key        动态数据源的key
     * @param contractBO 合同对象
     * @return 返回对应属性，如果为null，则返回空字符串
     * @throws BusinessException Contract.dynamicValueReadFail 动态表单value读取失败
     */
    public static String getValue(String key, ContractBO contractBO, TradeOrderLineTO orderLineTO) {
        String fieldName = fieldNameMap.get(key);
        if (fieldName == null) {
            // 未知的值来源
            log.error("未知的值来源，key:{}", key);
            throw new BusinessException(ContractTemplateExMsg.DYNAMIC_VALUE_READ_FAIL);
        }
        // 从合同对象拿出对应属性
        Object o = DynamicDataUtil.resolveFormLineDynamicData(key, contractBO);
        String value = "";
        if (o == null) {
            // 对应属性为null
            value = "";
        } else if (o instanceof List) {
            List<Object> list = (List<Object>) o;
            StringBuilder stringBuilder = new StringBuilder("");
            list.forEach(o1 -> {
                stringBuilder.append(getFieldValue(o1, fieldName) + "、");
            });
            value = stringBuilder.deleteCharAt(stringBuilder.length() - 1).toString();
        } else if (o instanceof BaseModel) {
            value = getFieldValue(o, fieldName);
        } else if (o instanceof Date) {
            //日期类型处理有效期开始和有效期结束
            value = DateUtils.getDateToString((Date) o, "yyyy-MM-dd");
        } else if (o instanceof Boolean) {
            //布尔型处理
            value = (Boolean)o?"是":"否";
        } else {
            value = String.valueOf(o);
        }
        //订单行数据特殊处理
        if(orderLineTO!=null){
            // 从合同对象拿出对应属性
            Object orderLine = DynamicDataUtil.resolveFormLineDynamicData(key, orderLineTO);
            value = orderLine==null?"":String.valueOf(orderLine);
        }
        return value;
    }

    /**
     * 获取对象制定属性的value
     *
     * @param o         对象
     * @param fieldName 字段名
     * @return
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     */
    private static String getFieldValue(Object o, String fieldName) {
        String value = "";
        try {
            Field field = o.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            Object o1 = field.get(o);
            if (o1 == null) {
                value = "";
            } else {
                value = String.valueOf(o1);
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.error("值来源数据解析获取失败，filedName:{},e:{}", fieldName, e);
            throw new BusinessException(ContractTemplateExMsg.DYNAMIC_VALUE_READ_FAIL);
        }

        return value;
    }


//    public static void main(String[] args){
//
//        ContractBO contractBO = new ContractBO();
//        EmployeeBO employeeBO = new EmployeeBO();
//        employeeBO.setEmployeeName("张三会");
//        employeeBO.setEmployeeCode("zsh123");
//        contractBO.setSignatory(employeeBO);
//        contractBO.setBusinessTypeDict(BusinessTypeDict.QUALIFICATION_FORM);
//        contractBO.setPartyCList(Lists.newArrayList(
//                EntityBO.builder().entityName("张三").build(),
//                EntityBO.builder().entityName("李翠花").build()));
//
//        String result1 = DynamicValueUtil.getValue("ContractBO.signatory",contractBO);
//        String result2 = DynamicValueUtil.getValue("ContractBO.businessTypeDict",contractBO);
//        String result3 = DynamicValueUtil.getValue("ContractBO.partyCList",contractBO);
//        System.out.println("result1: " + result1 + ",result2: " + result2 + ",result3: " + result3);
//    }
}
