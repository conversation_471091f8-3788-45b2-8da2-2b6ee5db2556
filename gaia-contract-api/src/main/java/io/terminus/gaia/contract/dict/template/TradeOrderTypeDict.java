package io.terminus.gaia.contract.dict.template;

import io.terminus.trantorframework.api.annotation.Dictionary;
import io.terminus.trantorframework.api.annotation.DictionaryItem;

/**
 * 订单模板类型
 * @Author: fengbo
 * @Date: 2021/9/26
 */
@Dictionary(name = "template type dict")
public interface TradeOrderTypeDict {

    /**
     * 采购
     */
    @DictionaryItem(value = "purchase")
    String PURCHASE = "PURCHASE";

    /**
     * 销售
     */
    @DictionaryItem(value = "sell")
    String SELL = "SELL";
}
