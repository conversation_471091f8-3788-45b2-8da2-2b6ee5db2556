package io.terminus.gaia.contract.dict.sign;

import io.terminus.trantorframework.api.annotation.Dictionary;
import io.terminus.trantorframework.api.annotation.DictionaryItem;

/**
 * 签署方式
 *
 * <AUTHOR>
 */
@Dictionary(
        name = "sign task way dict"
)
public interface SignTaskWayDict {

    /**
     * 印章
     */
    @DictionaryItem(value = "seal")
    String SEAL = "SEAL";

    /**
     * 证书
     */
    @DictionaryItem(value = "certificate")
    String CERTIFICATE = "CERTIFICATE";

    /**
     * 线下
     */
    @DictionaryItem(value = "offline")
    String OFFLINE = "OFFLINE";


}
