package io.terminus.gaia.contract.func.md.read;

import io.terminus.gaia.md.model.BrandBO;
import io.terminus.gaia.md.model.query.QBrandBO;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * @Author: fengbo
 * @Date: 2021/8/25
 */
@Function
public interface GetBrandByStatusFunc {

    /**
     * 根据状态，查询品牌信息
     *
     * @param qBrandBO 品牌信息
     * @return 主体列表
     */
    Paging<BrandBO> execute(QBrandBO qBrandBO);

}
