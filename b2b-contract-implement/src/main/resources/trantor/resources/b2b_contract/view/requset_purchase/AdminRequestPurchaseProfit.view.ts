import { Controller, Toast, utils, showMessage } from 'nusi-sdk'

const { triggerLogicFlow, triggerLogicFunction } = utils

export default class extends Controller {

    // 导出销售价格清单
    exportProfit = async () => {
        try {
            utils.openGlobalLoading()
            
            const result = await this.triggerLogicFunction('b2b_contract_ExportRequestPurchaseProfitFunc', this.pageRecord);
            console.log('导出结果:', result)
            
            if (result && result.value) {
                window.open(result.value, '_blank');
                showMessage({
                    level: "Weak",
                    message: "导出成功",
                    type: "Success"
                });
            } else {
                Toast.error("导出失败，未获取到文件地址");
            }
        } catch (e) {
            console.log('导出失败:', e)
            Toast.error(e.message || "导出失败");
        } finally {
            utils.closeGlobalLoading()
        }
    }

    // 获取导入销售价格的回调函数
    getUploadProfitPayloadCallback = (containerKey) => {
        return async (payload) => {
            console.log('导入销售价格回调:', payload)
            
            if (!payload || !payload.profitFile) {
                Toast.error("请选择要上传的文件");
                return;
            }

            try {
                // 设置需求单ID
                payload.requestPurchaseId = this.pageRecord.id;
                
                await this.triggerLogicFunction('b2b_contract_ImportRequestPurchaseProfitFunc', payload);
                
                showMessage({
                    level: "Weak",
                    message: "导入成功",
                    type: "Success"
                });
                
                // 刷新表格数据
                const container = this.getContainerByKey(containerKey);
                // if (container && container.refresh) {
                //     container.refresh();
                // }
                container.instance.refreshCurrPage()
            } catch (e) {
                console.log('导入失败:', e);
                Toast.error(e.message || "导入失败");
            }
        }
    }
}