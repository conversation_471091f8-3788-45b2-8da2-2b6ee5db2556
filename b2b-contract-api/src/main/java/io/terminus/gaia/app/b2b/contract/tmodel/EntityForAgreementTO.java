package io.terminus.gaia.app.b2b.contract.tmodel;

import io.terminus.trantorframework.api.RootModel;
import io.terminus.trantorframework.api.annotation.TransientModel;
import lombok.Data;

@Data
@TransientModel
public class EntityForAgreementTO extends RootModel<Long> {

    /**
     * 供应商编码
     */
    private String entityCode;

    /**
     * 供应商名称
     */
    private String entityName;

    /**
     * 供应商简称
     */
    private String entityShortName;

    /**
     * 社会统一信用代码
     */
    private String socialCreditCode;

    /**
     * 是否主供应商
     */
    private Boolean isMain;

}
