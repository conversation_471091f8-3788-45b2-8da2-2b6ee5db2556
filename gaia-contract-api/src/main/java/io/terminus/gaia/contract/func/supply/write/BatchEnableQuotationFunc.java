package io.terminus.gaia.contract.func.supply.write;

import io.terminus.gaia.contract.model.supply.QuotationBO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.Function;

import java.util.List;

/**
 * 批量 启用价格套
 * <AUTHOR>
 * @date 2021-04-20
 */
@Function(name = "enable quotation")
public interface BatchEnableQuotationFunc {
    /**
     * 启用价格套
     * @param quotationList 价格套
     * @return BooleanResult
     */
    BooleanResult execute(List<QuotationBO> quotationList);
}
