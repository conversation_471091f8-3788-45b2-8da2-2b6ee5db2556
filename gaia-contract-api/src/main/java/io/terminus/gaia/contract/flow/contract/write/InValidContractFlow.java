package io.terminus.gaia.contract.flow.contract.write;

import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.trantorframework.api.annotation.Flow;

/**
 * 作废合同逻辑流
 *
 * <AUTHOR>
 * @date 2021-04-21
 */
@Flow(name = "invalid contract flow")
public interface InValidContractFlow {
    /**
     * 作废合同
     *
     * @param contract 框架合同
     */
    void execute(ContractBO contract);
}
