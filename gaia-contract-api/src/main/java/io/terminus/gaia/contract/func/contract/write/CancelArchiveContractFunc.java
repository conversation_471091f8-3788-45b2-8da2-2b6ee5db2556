package io.terminus.gaia.contract.func.contract.write;

import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * 取消归档合同
 * <AUTHOR>
 * @date 2021-04-16
 */
@Function(name = "cancel archive contract")
public interface CancelArchiveContractFunc {
    /**
     * 取消归档合同
     * @param contract 框架合同
     * @return 框架合同
     */
    ContractBO execute(ContractBO contract);
}
