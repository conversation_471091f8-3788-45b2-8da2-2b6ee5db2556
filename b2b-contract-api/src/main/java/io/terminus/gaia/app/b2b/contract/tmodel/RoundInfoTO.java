package io.terminus.gaia.app.b2b.contract.tmodel;

import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.FieldType;
import io.terminus.trantorframework.api.annotation.TransientModel;
import io.terminus.trantorframework.api.annotation.typemeta.LookupMeta;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2025/7/8 11:08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TransientModel(name = "RoundInfoTO")
public class RoundInfoTO extends BaseModel<Long> {

    @Field(name = "询价单id")
    private Long askPriceId;

    @Field(name = "第round轮报价")
    private Integer round;

    @Field(name = "轮次名称")
    private String roundName;
}
