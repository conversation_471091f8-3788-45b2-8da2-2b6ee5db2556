package io.terminus.gaia.app.b2b.contract.func.askprice.write;

import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceLineBO;
import io.terminus.gaia.app.b2b.contract.tmodel.AskSupplierPriceReplyTO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * <AUTHOR>
 * @time 2025/7/8 17:24
 */
@Function
public interface UpdateAskSupplierPriceLineFunc {

    AskSupplierPriceLineBO execute(AskSupplierPriceLineBO req);

}
