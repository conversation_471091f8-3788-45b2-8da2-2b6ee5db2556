package io.terminus.gaia.contract.func.sign.read;

import io.terminus.gaia.contract.BaseService;
import io.terminus.gaia.contract.dict.sign.SignBillTypeDict;
import io.terminus.gaia.contract.dict.sign.SignTaskStatusDict;
import io.terminus.gaia.contract.model.sign.SignTaskBO;
import io.terminus.gaia.contract.model.sign.SignatoriesBO;
import io.terminus.gaia.md.dict.EntityCategoryDict;
import io.terminus.gaia.md.dict.EntityStatusDict;
import io.terminus.gaia.md.dict.EntityTypeDict;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.trantorframework.api.type.Attachment;
import io.terminus.trantorframework.sdk.sql.DS;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

/**
 * 根据ID查询SignTaskBO对象 单测
 *
 * <AUTHOR>
 */
public class QuerySignTaskByIdFuncTest extends BaseService {

    private QuerySignTaskByIdFunc querySignTaskByIdFunc;
    private SignTaskBO signTaskBO;

    @Before
    public void setUp() throws Exception {
        this.signTaskBO = new SignTaskBO();
        signTaskBO.setTitle("测试");
        signTaskBO.setSignStatusDict(SignTaskStatusDict.SIGNING);
        signTaskBO.setSignBillTypeDict(SignBillTypeDict.CONTRACT);
        signTaskBO.setSignBillCode("111000");

        Attachment attachment = new Attachment();
        List<Attachment.File> files = new ArrayList<>();
        Attachment.File file = new Attachment.File();
        file.setUrl("//terminus-trantor.oss-cn-hangzhou.aliyuncs.com/trantor/attachments/33e45698-fddb-43bb-ab96-fc2bf38a8f22.docx");
        file.setName("模板文件 2的副本 2.docx");
        file.setType("docx");
        files.add(file);
        attachment.setFiles(files);

//        signTaskBO.setSignAttachment(attachment);
        signTaskBO.setCompany(EntityBO.builder()
                .entityName("测试公司")
                .entityType(EntityTypeDict.COMPANY)
                .entityStatus(EntityStatusDict.ENABLED)
                .entityCategory(EntityCategoryDict.MANUFACTURE)
                .build());
        Long id = DS.create(signTaskBO).getValue().longValue();
        signTaskBO = DS.findById(SignTaskBO.class, id);
    }

    @Test
    public void execute(){
        Assert.assertNotNull(querySignTaskByIdFunc.execute(signTaskBO));
    }
}