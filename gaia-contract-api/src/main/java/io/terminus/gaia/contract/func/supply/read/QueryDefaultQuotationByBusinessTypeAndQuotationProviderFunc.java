package io.terminus.gaia.contract.func.supply.read;

import io.terminus.gaia.contract.model.supply.QuotationBO;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * 根据业务类型以及价格提供方，获取默认的价格套
 * <AUTHOR>
 * @date 2021-04-25
 */
@Function(name = "Get Default Quotation By Business Type And Quotation Provider")
public interface QueryDefaultQuotationByBusinessTypeAndQuotationProviderFunc {
    /**
     * 根据业务类型以及价格提供方，获取默认的价格套
     * @param quotation 价格套
     * @return Quotation
     */
    QuotationBO execute(QuotationBO quotation);
}
