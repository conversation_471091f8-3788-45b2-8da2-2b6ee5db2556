package io.terminus.gaia.app.b2b.contract.func.agreement;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.google.common.collect.ImmutableList;
import io.terminus.gaia.app.b2b.contract.tmodel.AgreementAreaSchemeRequestTO;
import io.terminus.gaia.app.b2b.contract.tmodel.AgreementDetailPriceLine;
import io.terminus.gaia.app.b2b.contract.tmodel.AgreementDetailScopePrice;
import io.terminus.gaia.item.dict.LogicDict;
import io.terminus.gaia.md.model.DistrictBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.api.type.Currency;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.flow.LF;
import io.terminus.trantorframework.sdk.upload.OSSClient;
import lombok.AllArgsConstructor;

import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@FunctionImpl
@AllArgsConstructor
public class AgreementAreaSchemeImportFuncImpl implements AgreementAreaSchemeImportFunc {

    private final OSSClient ossClient;

    private static final List<String> setPriceTemplateFixHead = ImmutableList.of("清单项（不可更改）", "关联标品（不可更改）", "材质（不可更改）","规格型号（不可更改）", "属性（不可更改）");
    private static final Function<AgreementDetailPriceLine, List<String>> setPriceTemplateContent = line -> ImmutableList.of(
            line.getAgreementDetail().getOuterAgreementName(),
            line.getAgreementDetail().getSpuBO().getName(),
            ObjectUtil.isNotNull(line.getAgreementDetail().getThingMaterial()) ? line.getAgreementDetail().getThingMaterial() : " ",
            ObjectUtil.isNotNull(line.getAgreementDetail().getThingSize()) ? line.getAgreementDetail().getThingSize() : " ",
            ObjectUtil.isNotNull(line.getAgreementDetail().getAttribute()) ? line.getAgreementDetail().getAttribute() : " "
    );

    @Override
    public List<AgreementDetailPriceLine> execute(AgreementAreaSchemeRequestTO request) {
        if (StrUtil.isBlank(request.getImportFile())) {
            throw new BusinessException("上传文件地址为空");
        }
        List<AgreementDetailPriceLine> lineList = request.getAgreementScopePriceTO().getLines();
        if (lineList.isEmpty()) {
            throw new BusinessException("协议清单为空");
        }

        String fileName = StrUtil.subAfter(request.getImportFile(), "/", true);
        if (request.getImportFile().contains("trantor/attachments/")) {
            fileName = "trantor/attachments/" + fileName;
        }
        Optional<InputStream> download = ossClient.download(fileName);
        if (!download.isPresent()) {
            throw new BusinessException("未找到上传的文件");
        }

        List<String> head = new ArrayList<>();
        head.addAll(setPriceTemplateFixHead);
        head.addAll(LF.map(lineList.get(0).getDetails(), detail -> (LogicDict.CONTAIN.equals(detail.getLogic())?"包含":"排除")  + " " + detail.getPurchasingAreas().stream().map(DistrictBO::getDistrictName).collect(Collectors.joining(","))));

        EasyExcel.read(download.get(), new AnalysisEventListener<Map<Integer, String>>() {
            private int index = 0;

            @Override
            public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                for (int i = 0; i < head.size(); i++) {
                    if (!head.get(i).equals(headMap.get(i))) {
                        throw new BusinessException("模板格式不正确，请重新导出模板");
                    }
                }
            }

            @Override
            public void invoke(Map<Integer, String> rowMap, AnalysisContext analysisContext) {
                if (lineList.size() > index) {
                    AgreementDetailPriceLine line = lineList.get(index++);
                    int i = 0;
                    for (String content : setPriceTemplateContent.apply(line)) {
                        i++;
//                        String rowString = ObjectUtil.isNotNull(rowMap.get(i++)) ? rowMap.get(i++).replace("\n","") : "";
//                        String newContent = ObjectUtil.isNotNull(content) ? content.replace("n","") : "";
//                        if (!Objects.equals(newContent, rowString)) {
//                            throw new BusinessException("模板格式不正确，请重新导出模板");
//                        }
                    }
                    String value;
                    for (AgreementDetailScopePrice detail : line.getDetails()) {
                        detail.setAreaValue(Objects.nonNull(value = rowMap.get(i++)) ? new Currency(NumberUtil.toBigDecimal(value)) : null);
                    }
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }
        }).sheet().doRead();

        return lineList;
    }

}
