package io.terminus.gaia.contract.func.classification.validate;

import io.terminus.gaia.contract.model.classification.ContractClassificationBO;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * 启用合同分类校验
 *
 * <AUTHOR>
 */
@Function
public interface ValidateEnableContractClassificationFunc {
    /**
     *
     * @param contractClassificationBO
     */
    void execute(ContractClassificationBO contractClassificationBO);
}
