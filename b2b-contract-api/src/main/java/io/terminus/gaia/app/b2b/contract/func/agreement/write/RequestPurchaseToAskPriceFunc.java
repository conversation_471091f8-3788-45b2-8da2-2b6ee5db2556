package io.terminus.gaia.app.b2b.contract.func.agreement.write;

import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseBO;
import io.terminus.gaia.organization.tmodel.IdResult;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * @interface_name: RequestPurchaseToAskPriceFunc
 * @desc:
 * @date: 2025/6/27 : 16:15
 * @author: Chonor
 **/
@Function
public interface RequestPurchaseToAskPriceFunc {

    BooleanResult execute(RequestPurchaseBO requestPurchaseBO);
}
