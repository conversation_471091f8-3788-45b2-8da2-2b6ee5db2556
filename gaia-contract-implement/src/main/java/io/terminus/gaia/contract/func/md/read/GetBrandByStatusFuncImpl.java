package io.terminus.gaia.contract.func.md.read;

import io.terminus.gaia.md.dict.BrandStatusDict;
import io.terminus.gaia.md.model.BrandBO;
import io.terminus.gaia.md.model.query.QBrandBO;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: fengbo
 * @Date: 2021/8/25
 */
@Slf4j
@FunctionImpl(name = "get brand by status func impl")
public class GetBrandByStatusFuncImpl implements GetBrandByStatusFunc {

    @Override
    public Paging<BrandBO> execute(QBrandBO qBrandBO) {
        String fuzzyValue = qBrandBO.getQueryParams().getFuzzyValue();
        String whereSql = StringUtils.isBlank(fuzzyValue) ? "brandStatus = ?" :
                String.format("brandStatus = ? AND (brandName like '%s' OR brandCode like '%s')", "%" + fuzzyValue + "%", "%" + fuzzyValue + "%");
        String defaultOrderBy = " order by updatedAt desc";
        Paging<BrandBO> brandPaging = DS.paging(BrandBO.class,
                "*",
                whereSql + defaultOrderBy,
                qBrandBO.getQueryParams().getPage(), BrandStatusDict.ENABLED);
        return brandPaging;
    }
}
