package io.terminus.gaia.contract.func.contract.write;

import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.gaia.contract.model.sign.SignTaskBO;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * 提交签署后 更新合同相关信息
 * <AUTHOR>
 */
@Function(name = "update contract after submit sign func")
public interface UpdateContractBOAfterSubmitSignFunc {

    ContractBO execute (SignTaskBO signTaskBO);
}
