package io.terminus.gaia.app.b2b.contract.func.agreement.read;

import io.terminus.gaia.item.tmodel.AgreementItemTO;
import io.terminus.gaia.app.b2b.contract.model.query.QAgreementBO;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.api.annotation.Function;

@Function
public interface PagingAgreementForItemFunc {

    Paging<AgreementItemTO> execute(QAgreementBO qAgreementBO);

}
