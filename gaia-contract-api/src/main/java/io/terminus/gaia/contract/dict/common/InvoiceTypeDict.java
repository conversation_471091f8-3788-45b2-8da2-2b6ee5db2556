package io.terminus.gaia.contract.dict.common;

import io.terminus.datastore.dsl.tools.StringUtils;
import io.terminus.trantorframework.api.annotation.Dictionary;
import io.terminus.trantorframework.api.annotation.DictionaryItem;

/**
 * 发票类型
 * <p>
 * S  增值税专用发票
 * C  增值税普通发票
 * O  其它原始凭证
 * CE  增值税电子普通发票
 * CB  区块链电子普通发票
 * SE  增值税电子专用发票
 *
 * <AUTHOR>
 * @date 2021/11/9 下午4:32
 */
@Dictionary(name = "invoice type dict")
public interface InvoiceTypeDict {
    /**
     * 增值税普通发票
     */
    @DictionaryItem(value = "增值税普通发票")
    String C = "C";

    /**
     * 增值税专用发票
     */
    @DictionaryItem(value = "增值税专用发票")
    String S = "S";

    /**
     * 增值税电子专用发票
     */
    @DictionaryItem(value = "增值税电子专用发票")
    String SE = "SE";

    /**
     * 增值税电子普通发票
     */
    @DictionaryItem(value = "增值税电子普通发票")
    String CE = "CE";

    static String exchange(String type) {
        String result = StringUtils.EMPTY;
        if (StringUtils.isEmpty(type)) {
            return result;
        }
        switch (type) {
            case C:
                result = "增值税普通发票";
                break;
            case S:
                result = "增值税专用发票";
                break;
            case SE:
                result = "增值税电子专用发票";
                break;
            case CE:
                result = "增值税电子普通发票";
                break;
            default:
                result = StringUtils.EMPTY;
        }
        return result;
    }
}
