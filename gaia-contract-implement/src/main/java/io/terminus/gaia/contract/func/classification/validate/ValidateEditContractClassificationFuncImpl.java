package io.terminus.gaia.contract.func.classification.validate;

import io.terminus.gaia.contract.dict.classification.ContractClassificationStatusDict;
import io.terminus.gaia.contract.ext.ValidateContractClassificationExistExt;
import io.terminus.gaia.contract.model.classification.ContractClassificationBO;
import io.terminus.gaia.contract.msg.classification.ClassificationExMsg;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@FunctionImpl
@RequiredArgsConstructor
public class ValidateEditContractClassificationFuncImpl implements ValidateEditContractClassificationFunc {
    private final ValidateContractClassificationExistExt validateContractClassificationExistExt;

    @Override
    public void execute(ContractClassificationBO contractClassificationBO) {

        ContractClassificationBO DB = validateContractClassificationExistExt.execute(contractClassificationBO);

        if (!ContractClassificationStatusDict.DRAFT.equals(DB.getClassificationStatus())) {
            throw new BusinessException(ClassificationExMsg.CLASSIFICATION_STATUS_MUST_BE_DRAFT_STATUS);
        }

        if (StringUtils.isBlank(contractClassificationBO.getClassificationName())) {
            throw new BusinessException(ClassificationExMsg.CONTRACT_CLASSIFICATION_NAME_IS_NULL);
        }

        contractClassificationBO.setPreviousClassification(DB);
    }
}
