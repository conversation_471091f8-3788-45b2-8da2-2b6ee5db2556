package io.terminus.gaia.contract.func.sign.write;

import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.gaia.contract.model.sign.SignTaskBO;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * @Author: fengbo
 * @Date: 2021/10/21
 */
@Function
public interface UpdateContractSignTaskAndSignFileSignFunc {
    SignTaskBO execute(ContractBO contractBO, SignTaskBO signTaskBO);
}
