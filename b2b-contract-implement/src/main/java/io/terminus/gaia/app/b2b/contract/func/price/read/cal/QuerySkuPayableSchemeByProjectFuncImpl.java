package io.terminus.gaia.app.b2b.contract.func.price.read.cal;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import io.terminus.gaia.app.b2b.contract.model.PayableSchemeTemplateBO;
import io.terminus.gaia.app.b2b.contract.model.price.SalePriceCalLineBO;
import io.terminus.gaia.app.b2b.contract.tmodel.price.SkuPayableSchemeReqTO;
import io.terminus.gaia.app.b2b.contract.tmodel.price.SkuPayableSchemeRespTO;
import io.terminus.gaia.contract.utils.ExceptionUtil;
import io.terminus.gaia.item.model.sku.OneMouthfulLinePriceBO;
import io.terminus.gaia.item.model.sku.SkuBO;
import io.terminus.gaia.md.model.DistrictBO;
import io.terminus.gaia.organization.model.ext.ProjectBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@FunctionImpl
public class QuerySkuPayableSchemeByProjectFuncImpl implements QuerySkuPayableSchemeByProjectFunc {
    @Override
    public List<SkuPayableSchemeRespTO> execute(List<SkuPayableSchemeReqTO> skuPayableSchemeReqTOList) {
        // 初始化出参
        List<SkuPayableSchemeRespTO> skuPayableSchemeRespTOList = new ArrayList<>();
        // 参数校验
        if (CollUtil.isEmpty(skuPayableSchemeReqTOList)) {
            throw new BusinessException("入参不能为空");
        }

        Set<String> skuCodeList = skuPayableSchemeReqTOList.stream().map(SkuPayableSchemeReqTO::getSkuNo).filter(StrUtil::isNotEmpty).collect(Collectors.toSet());
        Assert.notEmpty(skuCodeList, ExceptionUtil.create("商品sku不存在"));
        List<SkuBO> skuBOList = DS.findAll(SkuBO.class, "*,spu.*", "skuCode in (?)", skuCodeList);
        Map<String, SkuBO> skuBOMap = skuBOList.stream().collect(Collectors.toMap(SkuBO::getSkuCode, Function.identity()));


        SkuPayableSchemeRespTO result;
        for (SkuPayableSchemeReqTO skuPayableSchemeReqTO : skuPayableSchemeReqTOList) {
            if (StrUtil.isBlank(skuPayableSchemeReqTO.getSkuNo())
                    || ObjectUtil.isNull(skuPayableSchemeReqTO.getProjectId())) {
                throw new BusinessException("入参采购商品编码，项目id不能为空");
            }

            SkuBO skuBO = skuBOMap.get(skuPayableSchemeReqTO.getSkuNo());
            if (ObjectUtil.isNull(skuBO)) {
                throw new BusinessException("商品sku不存在");
            }

            ProjectBO projectBO = DS.findById(ProjectBO.class, skuPayableSchemeReqTO.getProjectId(), "*");
            if (ObjectUtil.isNull(projectBO)) {
                throw new BusinessException("项目不存在");
            }
            if (ObjectUtil.isNull(projectBO.getAreaCode())) {
                throw new BusinessException("项目地址为空");
            }
            DistrictBO districtBO = DS.findOne(DistrictBO.class, "*", "districtCode = ? ", projectBO.getAreaCode());
            if (ObjectUtil.isNull(districtBO)) {
                throw new BusinessException("项目地址不存在");
            }

            result = new SkuPayableSchemeRespTO();
            result.setSkuNo(skuPayableSchemeReqTO.getSkuNo());
            result.setProjectId(skuPayableSchemeReqTO.getProjectId());
            List<PayableSchemeTemplateBO> payableSchemeTemplateBOList = new ArrayList<>();
            List<String> districtIdList = StrUtil.split(districtBO.getIdPath(), "/");
            if (CollUtil.isNotEmpty(districtIdList)) {

                for (int i = districtIdList.size() - 1; i >= 0; i--) {
                    List<SalePriceCalLineBO> salePriceCalLineBOList = DS.findAll(SalePriceCalLineBO.class, "*, payableSchemeTemplateBO.*", "status = 'ENABLED' and spu = ? and districtBO = ?", skuBO.getSpu().getId(), districtIdList.get(i));

                    // 电线电缆
                    if (Objects.nonNull(skuBO.getSpu().getRawMaterialContent())) {
                        // 电线电缆品牌和测算必须相等品牌
                        Assert.notNull(skuBO.getBrandBO().getId(), ExceptionUtil.create("电线电缆品牌不能为空"));
                        salePriceCalLineBOList = salePriceCalLineBOList.stream()
                                .filter(it -> Objects.nonNull(it.getBrandBO()) && Objects.equals(it.getBrandBO().getId(), skuBO.getBrandBO().getId()))
                                .collect(Collectors.toList());
                    } else if (Objects.nonNull(skuBO.getBrandBO()) && Objects.nonNull(skuBO.getBrandBO().getId())) {
                        // 非电线电缆如果过滤品牌不为空，那么使用过滤品牌的
                        List<SalePriceCalLineBO> salePriceCalLineBOListFilterBrand = salePriceCalLineBOList.stream()
                                .filter(it -> Objects.nonNull(it.getBrandBO()) && Objects.equals(it.getBrandBO().getId(), skuBO.getBrandBO().getId()))
                                .collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(salePriceCalLineBOListFilterBrand)) {
                            salePriceCalLineBOList = salePriceCalLineBOListFilterBrand;
                        }
                    }

                    if (CollUtil.isNotEmpty(salePriceCalLineBOList)) {
                        salePriceCalLineBOList.forEach(salePriceCalLineBO -> {
                            // 给应收账期方案赋值测算id
                            salePriceCalLineBO.getPayableSchemeTemplateBO().setSalePriceCalLineId(salePriceCalLineBO.getId());
                            // 一口价信息
                            List<OneMouthfulLinePriceBO> oneMouthfulLinePriceBOList = DS.findAll(OneMouthfulLinePriceBO.class, "*", "sku = ? and districtBO = ? and payableSchemeTemplateId = ?", skuBO.getId(), salePriceCalLineBO.getDistrictBO().getId(), salePriceCalLineBO.getPayableSchemeTemplateBO().getId());
                            if (CollUtil.isNotEmpty(oneMouthfulLinePriceBOList)) {
                                salePriceCalLineBO.getPayableSchemeTemplateBO().setOneMouthPriceWithTax(oneMouthfulLinePriceBOList.get(0).getAclSalePriceWithTax());
                                salePriceCalLineBO.getPayableSchemeTemplateBO().setOneMouthPriceWithoutTax(oneMouthfulLinePriceBOList.get(0).getAclSalePriceWithoutTax());
                            }
                            payableSchemeTemplateBOList.add(salePriceCalLineBO.getPayableSchemeTemplateBO());
                        });
                        break;
                    }
                }
            }
            payableSchemeTemplateBOList.sort(Comparator.comparing(it -> ObjectUtil.defaultIfNull(it.getSortIndex(), Long.MAX_VALUE)));
            result.setPayableSchemeTemplateBOList(payableSchemeTemplateBOList);
            skuPayableSchemeRespTOList.add(result);
        }
        return skuPayableSchemeRespTOList;
    }
}
