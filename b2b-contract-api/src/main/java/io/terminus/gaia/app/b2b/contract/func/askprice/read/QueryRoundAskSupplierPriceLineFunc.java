package io.terminus.gaia.app.b2b.contract.func.askprice.read;

import io.terminus.gaia.app.b2b.contract.model.AskPriceLineBO;
import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceLineBO;
import io.terminus.gaia.app.b2b.contract.model.query.QAskPriceBO;
import io.terminus.gaia.app.b2b.contract.model.query.QAskPriceLineBO;
import io.terminus.gaia.app.b2b.contract.model.query.QAskSupplierPriceBO;
import io.terminus.gaia.app.b2b.contract.model.query.QAskSupplierPriceLineBO;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * 根据轮次和询价单id，查询询价单明细和供应商报价单价
 *
 * @author: huangjunwei
 **/
@Function
public interface QueryRoundAskSupplierPriceLineFunc {

    Paging<AskSupplierPriceLineBO> execute(QAskSupplierPriceLineBO req);
}
