package io.terminus.gaia.contract.flow.sign.validate;

import io.terminus.gaia.contract.BaseService;

/**
 * 校验是否是当前签署方签署 单测
 *
 * <AUTHOR>
 */
public class ValidateSignFlowTest extends BaseService {

//    private ValidateSignFlow validateSignFlow;
//    private CreateSignatureFunc createSignatureFunc;
//    private CreateSignTaskFunc createSignTaskFunc;
//    private CreateSignFileFunc createSignFileFunc;
//    private BatchCreateSignatoriesFunc batchCreateSignatoriesFunc;
//    private SignTaskBO signTaskBO;
//    private List<SignatoriesBO> signatoriesBOList;
//    private SignatureBO signatureBO;
//    private SignatoriesSignTO signatoriesSignTO;
//
//    @Before
//    public void setUp() throws Exception {
//
//        // 组装数据 SignTask
//        this.signTaskBO = new SignTaskBO();
//        signTaskBO.setTitle("测试");
//        signTaskBO.setSignStatusDict(SignTaskStatusDict.SIGNING);
//        signTaskBO.setSignBillTypeDict(SignBillTypeDict.CONTRACT);
//        signTaskBO.setSignBillCode("111000");
//        signTaskBO.setSignWayDict(SignTaskWayDict.CERTIFICATE);
//
//        signTaskBO.setCompany(EntityBO.builder()
//                .entityName("测试公司")
//                .entityType(EntityTypeDict.COMPANY)
//                .entityStatus(EntityStatusDict.ENABLED)
//                .entityCategory(EntityCategoryDict.MANUFACTURE)
//                .build());
////        Long id = DS.create(signTaskBO).getValue().longValue();
////        signTaskBO = DS.findById(SignTaskBO.class, id);
//        // 组装list
//        signatoriesBOList = new ArrayList<>();
//        SignatoriesBO signatoriesBO = SignatoriesBO.builder().entity(EntityBO.builder()
//                .entityName("ceshi1")
//                .entityType(EntityTypeDict.COMPANY)
//                .entityStatus(EntityStatusDict.ENABLED)
//                .entityCategory(EntityCategoryDict.MANUFACTURE)
//                .build()).sortNum(1).build();
//        signatoriesBOList.add(signatoriesBO);
//        signatoriesBOList.add(SignatoriesBO.builder().entity(EntityBO.builder()
//                .entityName("ceshi2")
//                .entityType(EntityTypeDict.COMPANY)
//                .entityStatus(EntityStatusDict.ENABLED)
//                .entityCategory(EntityCategoryDict.MANUFACTURE)
//                .build()).sortNum(2).build());
//
//        signTaskBO = createSignTaskFunc.execute(signTaskBO);
//        List<SignatoriesBO> res = batchCreateSignatoriesFunc.execute(signatoriesBOList, signTaskBO);
//
//        signTaskBO.setSignatoriesList(signatoriesBOList);
//
//        EntityBO entityBO = new EntityBO();
//        entityBO.setId(1234L);
//        EntityRoleBO entityRoleBO = new EntityRoleBO();
//        entityRoleBO.setEntity(entityBO);
//        entityRoleBO.setRoleCode("TEST_CODE");
//        entityRoleBO.setRoleType(EntityRoleTypeDict.MEMBERS);
//        entityRoleBO.setRoleStatus(EntityRoleStatusDict.ENABLED);
//        Long entityRoleId = DS.create(entityRoleBO).getValue().longValue();
//        entityRoleBO = DS.findById(EntityRoleBO.class,entityRoleId);
//
//        signatureBO = new SignatureBO();
//        signatureBO.setName("测试名称");
//        signatureBO.setEntityBO(entityBO);
//        signatureBO.setSignatureTypeDict(SignatureTypeDict.SIGNATURE);
//        signatureBO.setRemarks("测试备注");
//
//        signatureBO = createSignatureFunc.execute(signatureBO);
//
//        signatoriesSignTO = new SignatoriesSignTO();
//        signatoriesSignTO.setSignatoriesBO(signatoriesBO);
//        signatoriesSignTO.setSignatureBO(signatureBO);
//    }
//
//    @Test
//    public void execute() {
//        validateSignFlow.execute(signatoriesSignTO);
//    }
}