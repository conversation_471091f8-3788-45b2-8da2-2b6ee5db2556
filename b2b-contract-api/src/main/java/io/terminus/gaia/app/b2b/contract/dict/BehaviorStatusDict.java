package io.terminus.gaia.app.b2b.contract.dict;

import io.terminus.trantorframework.api.annotation.Dictionary;
import io.terminus.trantorframework.api.annotation.DictionaryItem;

/**
 * <AUTHOR>
 */
@Dictionary
public interface BehaviorStatusDict {

    @DictionaryItem(value = "待提交")
    String CREATED = "CREATED";

    @DictionaryItem(value = "审核中")
    String AUDITING = "AUDITING";

    @DictionaryItem(value = "已生效")
    String ACTIVE = "ACTIVE";

    @DictionaryItem(value = "已解除")
    String RELIEVE = "RELIEVE";

}
