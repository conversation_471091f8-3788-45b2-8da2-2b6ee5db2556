package io.terminus.gaia.contract.func.contract.fill;

import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;

/**
 * <AUTHOR>
 */
@FunctionImpl
public class FillContractIdWhenCreateFuncImpl implements FillContractIdWhenCreateFunc {
    @Override
    public void execute(ContractBO contractBO) {
        contractBO.setId(DS.nextId(ContractBO.class));
    }
}
