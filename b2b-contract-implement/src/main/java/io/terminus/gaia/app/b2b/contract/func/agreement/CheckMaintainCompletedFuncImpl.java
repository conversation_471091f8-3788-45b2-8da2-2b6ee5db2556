package io.terminus.gaia.app.b2b.contract.func.agreement;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import io.terminus.gaia.app.b2b.contract.dict.AgreementCoverAreaTypeDict;
import io.terminus.gaia.app.b2b.contract.dict.AgreementDetailStatusDict;
import io.terminus.gaia.app.b2b.contract.dict.AgreementStatusDict;
import io.terminus.gaia.app.b2b.contract.dict.AgreementTypeDict;
import io.terminus.gaia.app.b2b.contract.model.AgreementBO;
import io.terminus.gaia.app.b2b.contract.model.AgreementDetailBO;
import io.terminus.gaia.app.b2b.contract.model.PaymentRelatePriceSchemeBO;
import io.terminus.gaia.app.b2b.contract.spring.validator.AgreementValidator;
import io.terminus.gaia.app.b2b.item.dict.category.LinkPriceSchemeDict;
import io.terminus.gaia.app.b2b.item.model.CategoryAgreementConfigBO;
import io.terminus.gaia.md.model.CategoryBO;
import io.terminus.trantorframework.Page;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-08-08
 * @descrition
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class CheckMaintainCompletedFuncImpl implements CheckMaintainCompletedFunc {

    private final AgreementValidator agreementValidator;

    @Override
    public void execute() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("Check Agreement Maintain Completed");

        // 控制全量 or 增量
//        String obj = redisTemplate.opsForValue().get("entire:history:agreement");

        Date end = new Date();
        Date start = DateUtil.offset(end, DateField.YEAR, -20);
//        if (obj == null) {
//            start = DateUtil.offsetMinute(end, -5);
//        } else {

//        }
        log.info("CheckMaintainCompletedFlow execute, start-{},end-{}", start, end);

        int pageNo = 1;
        Page page = new Page(pageNo, 20);


        while (true) {

            // 增量的协议
            Paging<AgreementBO> paging = DS.paging(AgreementBO.class, "*,category.*,departments.*,coverAreas.*,paymentSchemes.*",
                    " `status` != ?",
                    page, AgreementStatusDict.DROP);

            if (paging.getData().isEmpty()) {
                break;
            }

            page.setNo(++pageNo);

            // 协议的分类配置map
            Map<Long, CategoryAgreementConfigBO> configMap = getCategoryAgreementConfigBOMap(paging.getData());

            for (AgreementBO agreement : paging.getData()) {

                boolean basicInfoSatisfy = agreementValidator.validateRequired(agreement);

                if (!basicInfoSatisfy) {
                    agreement.setMaintainCompleted(false);
                    DS.update(agreement);
                } else {
                    // 基础信息&&价格方案配置
                    validateConfig(configMap, agreement);
                }
            }
        }

        stopWatch.stop();
        log.info("{}", stopWatch.prettyPrint());
    }

    private Map<Long, CategoryAgreementConfigBO> getCategoryAgreementConfigBOMap(List<AgreementBO> data) {
        List<Long> categoryIds = data.stream().map(AgreementBO::getCategory).filter(Objects::nonNull)
                .map(CategoryBO::getId).distinct().collect(Collectors.toList());
        Map<Long, CategoryAgreementConfigBO> configMap = DS.findAll(CategoryAgreementConfigBO.class,
                        "*", "categoryBO in (?) ", categoryIds).stream()
                .collect(Collectors.toMap(configBO -> configBO.getCategoryBO().getId(), Function.identity(), (v1, v2) -> v2));
        return configMap;
    }

    private void validateConfig(Map<Long, CategoryAgreementConfigBO> configMap, AgreementBO agreement) {

        CategoryAgreementConfigBO config = configMap.get(agreement.getCategory().getId());
        String isLinkPriceScheme = Optional.ofNullable(config).map(CategoryAgreementConfigBO::getIsLinkPriceScheme).orElse(LinkPriceSchemeDict.NO);

        if (!isLinkPriceScheme.equals(LinkPriceSchemeDict.YES)) {
            agreement.setMaintainCompleted(true);
        } else {
            boolean complete = true;
            List<AgreementDetailBO> details = DS.findAll(AgreementDetailBO.class, "*", "agreementBO = ? and statusDict = ?",
                    agreement.getId(), AgreementDetailStatusDict.START_USING);

            if (Objects.equals(agreement.getType(), AgreementTypeDict.MATCH_MAKING)) {
                List<PaymentRelatePriceSchemeBO> paymentRelatePriceSchemeBOS = DS.findAll(PaymentRelatePriceSchemeBO.class, "*", "agreementBO=?", agreement.getId());
                Map<Long, List<PaymentRelatePriceSchemeBO>> paymentRelatePriceSchemeGroup = paymentRelatePriceSchemeBOS.stream().collect(Collectors.groupingBy(it -> it.getAgreementDetail().getId()));

                complete = details.stream().allMatch(it -> Objects.nonNull(it.getPriceSchemeBO()) || paymentRelatePriceSchemeGroup.containsKey(it.getId()));
            } else {
                complete = details.stream().allMatch(it -> Objects.nonNull(it.getPriceSchemeBO()));
            }

            agreement.setMaintainCompleted(complete);
        }
        DS.update(agreement);
    }
}
