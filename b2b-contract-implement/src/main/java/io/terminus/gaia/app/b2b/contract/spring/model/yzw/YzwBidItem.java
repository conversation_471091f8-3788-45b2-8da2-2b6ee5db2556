package io.terminus.gaia.app.b2b.contract.spring.model.yzw;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class YzwBidItem implements Serializable {

    @JsonProperty("TenderListTableSysNo")
    private Long tenderListTableSysNo;

    @JsonProperty("ListName")
    private String listName;

    @JsonProperty("ListType")
    private Integer listType;

    @JsonProperty("ListTemplateSysNo")
    private Long listTemplateSysNo;

    @JsonProperty("BiddingItemList")
    private List<List<YzwBiddingListItem>> biddingItemList;

    @JsonProperty("OldBiddingItemList")
    private List<List<YzwBiddingListItem>> oldBiddingItemList;
}