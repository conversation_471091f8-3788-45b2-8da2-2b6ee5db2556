package io.terminus.gaia.app.b2b.contract.func.agreement.write;

import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseLineBO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * @class_name: UpdateRequestPurchaseLineFuncImpl
 * @desc:
 * @date: 2025/6/21 : 17:58
 * @author: Chonor
 **/
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class UpdateRequestPurchaseLineFuncImpl implements UpdateRequestPurchaseLineFunc{
    @Override
    public BooleanResult execute(RequestPurchaseLineBO requestPurchaseLineBO) {
        
        // 检查输入参数
        if (Objects.isNull(requestPurchaseLineBO)) {
            throw new BusinessException("请求参数不能为空");
        }
        
        if (Objects.isNull(requestPurchaseLineBO.getId())) {
            throw new BusinessException("需求明细ID不能为空");
        }

        final RequestPurchaseLineBO db = DS.findById(RequestPurchaseLineBO.class, requestPurchaseLineBO.getId());
        if (Objects.isNull(db)) {
            throw new BusinessException("未找到对应的需求明细记录");
        }

        RequestPurchaseLineBO update = new RequestPurchaseLineBO();
        update.setId(requestPurchaseLineBO.getId());

        // 销售辅材及其他价格，处理空值
        final BigDecimal saleOtherCosts = Objects.nonNull(requestPurchaseLineBO.getSaleOtherCosts()) ? 
                requestPurchaseLineBO.getSaleOtherCosts() : 
                (Objects.nonNull(db.getSaleOtherCosts()) ? db.getSaleOtherCosts() : BigDecimal.ZERO);

        update.setSaleOtherCosts(saleOtherCosts);

        // 销售折扣系数，处理空值
        BigDecimal saleDiscountFactor = Objects.nonNull(requestPurchaseLineBO.getSaleDiscountFactor()) ? 
                requestPurchaseLineBO.getSaleDiscountFactor() : 
                (Objects.nonNull(db.getSaleDiscountFactor()) ? db.getSaleDiscountFactor() : BigDecimal.ONE);

        update.setSaleDiscountFactor(saleDiscountFactor);

        // 采购协议铜基价，处理空值
        final BigDecimal purCopperBasicPrice = Objects.nonNull(db.getPurCopperBasicPrice()) ? 
                db.getPurCopperBasicPrice() : BigDecimal.ZERO;

        // 标品含铜量，处理空值
        final BigDecimal purRawMaterialContent = Objects.nonNull(db.getPurRawMaterialContent()) ? 
                db.getPurRawMaterialContent() : BigDecimal.ZERO;

        // 采购含税单价，处理空值
        final BigDecimal purTaxPrice = Objects.nonNull(db.getPurTaxPrice()) ? 
                db.getPurTaxPrice() : BigDecimal.ZERO;

        // 销售含税单价 = （采购协议铜基价*标品含铜量/1000+销售辅材及其他价格）*销售折扣系数
        // 修正公式：原公式中的除法操作有问题，应该是除以1000而不是(1000+saleOtherCosts)
        BigDecimal saleTaxPrice = BigDecimal.ZERO;
        try {
            // 计算: (purCopperBasicPrice * purRawMaterialContent / 1000 + saleOtherCosts) * saleDiscountFactor
            BigDecimal copperPrice = purCopperBasicPrice.multiply(purRawMaterialContent)
                    .divide(new BigDecimal("1000"), 6, RoundingMode.HALF_UP);
            saleTaxPrice = copperPrice.add(saleOtherCosts).multiply(saleDiscountFactor);
        } catch (ArithmeticException e) {
            log.warn("计算销售含税单价时出现算术异常: {}", e.getMessage());
            saleTaxPrice = BigDecimal.ZERO;
        }
        update.setSaleTaxPrice(saleTaxPrice);

        // 销售利润率 = （销售含税单价-采购含税单价）/采购含税单价*100%
        BigDecimal saleProfitRate = BigDecimal.ZERO;
        try {
            if (purTaxPrice.compareTo(BigDecimal.ZERO) > 0) {
                saleProfitRate = (saleTaxPrice.subtract(purTaxPrice))
                        .divide(purTaxPrice, 6, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
            } else {
                log.warn("采购含税单价为0或负数，无法计算利润率");
            }
        } catch (ArithmeticException e) {
            log.warn("计算销售利润率时出现算术异常: {}", e.getMessage());
            saleProfitRate = BigDecimal.ZERO;
        }
        update.setProfitRate(saleProfitRate);

        log.info("更新需求明细ID: {}, 销售含税单价: {}, 销售利润率: {}%", 
                requestPurchaseLineBO.getId(), saleTaxPrice, saleProfitRate);

        return DS.update(update);
    }
}
