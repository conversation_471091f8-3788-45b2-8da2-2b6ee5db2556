package io.terminus.gaia.contract.flow.contract.write;

import io.terminus.gaia.contract.flow.template.read.PreviewContractTemplateFlow;
import io.terminus.gaia.contract.func.contract.fill.FillContractDataWhenCreateFunc;
import io.terminus.gaia.contract.func.contract.fill.FillContractSaveDataFunc;
import io.terminus.gaia.contract.func.contract.fill.FillPreviousContractFunc;
import io.terminus.gaia.contract.func.contract.validate.ValidateSaveContractParamFunc;
import io.terminus.gaia.contract.func.contract.write.*;
import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FlowImpl;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@FlowImpl
@RequiredArgsConstructor
public class EditContractFlowImpl implements EditContractFlow {

    private final ValidateSaveContractParamFunc validateSaveContractParamFunc;
    private final PreviewContractTemplateFlow previewContractTemplateFlow;
    private final FillContractSaveDataFunc fillContractSaveDataFunc;
    private final FillPreviousContractFunc fillPreviousContractFunc;
    private final EditContractFunc editContractFunc;
    private final EditContractMultiPartyFunc editContractMultiPartyFunc;
    private final SetUpContractDealerFlow setUpContractDealerFlow;
    private final CreateContractCategoryFunc createContractCategoryFunc;
    private final CreateContractBrandFunc createContractBrandFunc;
    private final CreateContractDepartmentFunc createContractDepartmentFunc;
    private final UpdatePrcWhileUpdateContractFlow updatePrcWhileUpdateContractFlow;
    private final FillContractDataWhenCreateFunc fillContractDataWhenCreateFunc;

    @Override
    public BooleanResult execute(ContractBO contractBO) {
        // 参数校验
        validateSaveContractParamFunc.execute(contractBO);
        // 参数转化，防止重新选择字典类型所带过来的脏数据
        fillContractSaveDataFunc.execute(contractBO);
        // 填充原始合同数据
        fillPreviousContractFunc.execute(contractBO);
        // 判断是否使用模板
        if (contractBO.getIsUseTemplate()) {
            // 预览合同，将预览的文件，保存在合同的附件中
            fillContractDataWhenCreateFunc.execute(contractBO);
            contractBO = previewContractTemplateFlow.execute(contractBO);
        }
        // 编辑时多方签署关系的处理
        editContractMultiPartyFunc.execute(contractBO);
        // 合同数据入库
        editContractFunc.execute(contractBO);
        //合同签约供应商信息入库
        setUpContractDealerFlow.execute(contractBO);
        //合同品牌入库
        createContractBrandFunc.execute(contractBO);
        //合同分类入库
        createContractCategoryFunc.execute(contractBO);
        //适用范围入库
        createContractDepartmentFunc.execute(contractBO);
        //更新商品价格
        updatePrcWhileUpdateContractFlow.execute(contractBO);
        return BooleanResult.TRUE;
    }
}
