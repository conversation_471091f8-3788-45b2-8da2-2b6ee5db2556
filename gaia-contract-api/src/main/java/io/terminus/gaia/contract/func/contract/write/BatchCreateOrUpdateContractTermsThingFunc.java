package io.terminus.gaia.contract.func.contract.write;

import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.gaia.contract.model.contract.ContractTermBO;
import io.terminus.trantorframework.api.annotation.Function;

import java.util.List;

/**
 * 批量添加合同数据
 * <AUTHOR>
 * @date 2021-04-14
 */
@Function(name = "batch create or update contract terms thing")
public interface BatchCreateOrUpdateContractTermsThingFunc {
    /**
     * 批量添加合同数据
     * @param contract 框架合同
     * @param contractTermsList 合同条款列表
     * @return 合同条款列表
     */
    List<ContractTermBO> execute(ContractBO contract, List<ContractTermBO> contractTermsList);
}
