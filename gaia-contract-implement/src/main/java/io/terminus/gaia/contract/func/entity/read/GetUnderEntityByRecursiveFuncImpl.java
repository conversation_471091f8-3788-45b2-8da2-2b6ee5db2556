package io.terminus.gaia.contract.func.entity.read;

import com.google.common.collect.Lists;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.trantorframework.api.RootModel;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.flow.LF;
import io.terminus.trantorframework.sdk.sql.DS;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 获取当前主体下的所有下级主体
 *
 * <AUTHOR>
 * @date 2021-04-19
 */
@FunctionImpl(name = "get under entity by recursive func")
public class GetUnderEntityByRecursiveFuncImpl implements GetUnderEntityByRecursiveFunc {
    /**
     * 获取当前主体下的所有下级主体
     *
     * @param entityList 主体列表
     */
    @Override
    public List<EntityBO> execute(List<EntityBO> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return Collections.emptyList();
        }
        List<EntityBO> resultList = Lists.newArrayList();
        getEntityBOs(entityList, resultList);
        resultList.addAll(entityList);
        return resultList;
    }

    /**
     * 循环遍历查询获取供应商下所有下级主体
     *
     * @param entityBOS
     * @param resultList
     * @return 供应商下所有下级主体
     */
    public List<EntityBO> getEntityBOs(List<EntityBO> entityBOS, List<EntityBO> resultList) {
        //根据主体id条件查询 主体id为将要查询到的主体的上级主体id
        List<Long> ids = LF.map(entityBOS, RootModel::getId);
        List<EntityBO> childEntities = DS.findAll(EntityBO.class,
                "*", "parentEntity.id in (?)", ids);
        //如果查询到下级供应商不为空 继续往下查询对应下级供应商
        if (CollectionUtils.isNotEmpty(childEntities)) {
            resultList.addAll(childEntities);
            getEntityBOs(childEntities, resultList);
        }
        return resultList;
    }
}
