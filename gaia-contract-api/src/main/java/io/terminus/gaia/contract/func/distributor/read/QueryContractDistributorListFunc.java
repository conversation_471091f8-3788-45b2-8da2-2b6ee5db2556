package io.terminus.gaia.contract.func.distributor.read;

import io.terminus.gaia.contract.model.contract.ContractDistributorBO;
import io.terminus.trantorframework.api.annotation.Function;

import java.util.List;

/**
 * 查询经销商服务信息列表
 * @Author: fengbo
 * @Date: 2021/8/11
 */
@Function(name = "get contract distributor func")
public interface QueryContractDistributorListFunc {

    /**
     * 查询经销商服务信息列表
     *
     * @param contractDistributor
     */
    List<ContractDistributorBO> execute(ContractDistributorBO contractDistributor);
}
