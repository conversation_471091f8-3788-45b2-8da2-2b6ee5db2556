package io.terminus.gaia.app.b2b.contract.func.agreement;

import com.google.common.collect.Maps;
import io.terminus.gaia.common.open.bpm.client.BpmClient;
import io.terminus.gaia.md.dict.SubmitZJBPMCodeDict;
import io.terminus.gaia.md.tmodel.request.BpmRequestTO;
import io.terminus.gaia.md.tmodel.response.BpmResponseTO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.json.Json;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 测试提交bpm审批
 *
 * <AUTHOR>
 */
@Slf4j
@FunctionImpl
public class SubmitBpmFuncImpl implements SubmitBpmFunc{

    @Override
    public BpmResponseTO execute() {

        BpmRequestTO bpmRequestTO = new BpmRequestTO();
        Map<String, Object> data = new HashMap<>();
        Map<String, Object> formson_1349 = Maps.newHashMap();
        Map<String, Object> formson_1350 = Maps.newHashMap();
        Map<String, Object> formmain_1347 = Maps.newHashMap();
        Map<String, Object> formson_1348 = Maps.newHashMap();
        formson_1349.put("名称","G-test-02黄铜230331");
        formson_1349.put("编码","245300020001");
        formson_1349.put("分类","G-test");
        formson_1349.put("品牌", "品牌");
        formson_1349.put("材质", "黄铜");
        formson_1349.put("规格型号", "230331");
        formson_1349.put("计量单位", "桶");
        formson_1349.put("报价依据", "报价依据\\t1-2；报价依据\\t1-2；报价依据\\t1-2；报价依据\\t1-2");
        formson_1349.put("采购数量", "1402");
        formson_1349.put("竞价备注", "竞价备注\\t1-3；竞价备注\\t1-3；竞价备注\\t1-3；竞价备注\\t1-3");
        formson_1349.put("属性", "[{\"unit\": {\"id\": \"8001\", \"measured\": \"NUMBER\", \"unitCode\": \"8001\", \"unitName\": \"G-test-单位\", \"unitStatus\": \"ENABLED\", \"unitNickname\": \"G\", \"unitPrecision\": 2, \"unitShortCode\": \"G\"}, \"remark\": null, \"status\": null, \"sourceDict\": null, \"confirmStage\": \"contact\", \"attributeCode\": null, \"attributeName\": \"G-test002\", \"attributePresets\": null, \"attributeValueTO\": null, \"attributeValueType\": \"NUMBER\"}, {\"unit\": {\"id\": \"6001\", \"measured\": \"LENGTH\", \"unitCode\": \"6001\", \"unitName\": \"毫米\", \"unitStatus\": \"ENABLED\", \"unitNickname\": \"mm\", \"unitPrecision\": 0, \"unitShortCode\": \"mm\"}, \"remark\": null, \"status\": null, \"sourceDict\": null, \"confirmStage\": \"item\", \"attributeCode\": null, \"attributeName\": \"长\", \"attributePresets\": null, \"attributeValueTO\": {\"textValue\": null, \"numberValue\": 100}, \"attributeValueType\": \"NUMBER\"}, {\"unit\": {\"id\": \"6001\", \"measured\": \"LENGTH\", \"unitCode\": \"6001\", \"unitName\": \"毫米\", \"unitStatus\": \"ENABLED\", \"unitNickname\": \"mm\", \"unitPrecision\": 0, \"unitShortCode\": \"mm\"}, \"remark\": null, \"status\": null, \"sourceDict\": null, \"confirmStage\": \"item\", \"attributeCode\": null, \"attributeName\": \"宽\", \"attributePresets\": null, \"attributeValueTO\": {\"textValue\": null, \"numberValue\": 10}, \"attributeValueType\": \"NUMBER\"}, {\"unit\": {\"id\": \"6001\", \"measured\": \"LENGTH\", \"unitCode\": \"6001\", \"unitName\": \"毫米\", \"unitStatus\": \"ENABLED\", \"unitNickname\": \"mm\", \"unitPrecision\": 0, \"unitShortCode\": \"mm\"}, \"remark\": null, \"status\": null, \"sourceDict\": null, \"confirmStage\": \"item\", \"attributeCode\": null, \"attributeName\": \"厚度\", \"attributePresets\": null, \"attributeValueTO\": {\"textValue\": null, \"numberValue\": 2}, \"attributeValueType\": \"NUMBER\"}]");
        formson_1350.put("供应商名称","河北尚煦城商贸有限公司");
        formson_1350.put("汇总除税合价","10531");
        formson_1350.put("供应商联系人","供应商联系人");
        formson_1350.put("供应商联系电话","13111112222");
        formson_1350.put("备注","供应商联系人供应商联系人供应商联系人供应商联系人供应商联系人供应商联系人");
        formmain_1347.put("单号", "BO12666277452000");
        formmain_1347.put("竞价品类", "草本");
        formmain_1347.put("竞价单标题", "基于招标任务竞价21");
        formmain_1347.put("竞价区域", "北京市、海南省");
        formmain_1347.put("竞价单位","中建一局");
        formmain_1347.put("竞价单状态","60");
        formmain_1347.put("经办人","cszh_top");
        formmain_1347.put("经办人联系电话","13394538999");
        formmain_1347.put("竞价类型","1");
        formmain_1347.put("竞价开始时间","2023-03-31 15:00:00");
        formmain_1347.put("竞价截止时间","2023-03-31 11:27:48");
        formmain_1347.put("竞价发布时间","2023-03-31 14:34:00");
        formmain_1347.put("付款方案-付款节点",null);
        formmain_1347.put("付款方案-方案描述",null);
        formmain_1347.put("竞价单备注","竞价备注");
        formmain_1347.put("特殊说明","特殊说明");
        formmain_1347.put("附件",null);
        formson_1348.put("项目名称","英伦花园项目");
        formson_1348.put("概算金额",null);
        formson_1348.put("项目开工时间",null);
        formson_1348.put("计划竣工时间",null);
        formson_1348.put("币种","CNY");
        formson_1348.put("施工单位","一公司");
        formson_1348.put("所属分公司事业部大项目部","未纳入大项目部管理/未纳入大项目部管理");
        formson_1348.put("项目所属地区",null);
        formson_1348.put("工程地点省","北京市");
        formson_1348.put("工程地点市",null);
        formson_1348.put("工程地点区县",null);
        formson_1348.put("建筑面积","2.85");
        formson_1348.put("产品线",null);
        formson_1348.put("结构形式",null);
        data.put("formson_1349", Collections.singletonList(formson_1349));
        data.put("formson_1350",Collections.singletonList(formson_1350));
        data.put("formmain_1347",formmain_1347);
        data.put("formson_1348",Collections.singletonList(formson_1348));
        bpmRequestTO.setData(data);
        bpmRequestTO.setSubject("确认竞价审批");
        bpmRequestTO.setTemplateCode(SubmitZJBPMCodeDict.BID_CREATE);
        BpmResponseTO call = BpmClient.call(bpmRequestTO);
        log.error("bpm返回信息:{}", Json.toJson(call));
        return call;
    }
}
