package io.terminus.gaia.contract.func.supply.write;

import io.terminus.gaia.contract.BaseService;
import io.terminus.gaia.contract.model.supply.QuotationBO;
import io.terminus.gaia.contract.util.QuotationFlowHolder;
import io.terminus.trantorframework.sdk.sql.DS;
import org.junit.Before;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2021-05-10
 */
public class SetDefaultQuotationFuncTest extends BaseService {
    SetDefaultQuotationFunc setDefaultQuotationFunc;
    QuotationBO quotation;
    @Before
    public void setUp() throws Exception {
        quotation = QuotationFlowHolder.quotation;
        DS.save(quotation);
    }

    @Test
    public void execute() {
        setDefaultQuotationFunc.execute(quotation);
    }
}