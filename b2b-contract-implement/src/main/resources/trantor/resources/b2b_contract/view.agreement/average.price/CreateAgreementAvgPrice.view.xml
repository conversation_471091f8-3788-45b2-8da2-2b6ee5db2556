<View title="设置协议采购平均系数" forModel="b2b_contract_AgreementBO" type="Form" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://trantor-docs-dev.app.terminus.io/static/v0.17.x/schema/base.xsd">

    <Steps current="1">
        <Step title="选择协议" key="1" />
        <Step title="确认清单" key="2" />
        <Step title="设置价格" key="3" />
    </Steps>

    <TableForm key="agreementList" model="b2b_contract_AgreementBO" title="框架协议" showAdd="false" dataSource="#{()=>pageRecord.data || []}">
        <Fields>
            <Field name="code" readonly="true"/>
            <Field name="name" readonly="true">
                <RenderType>
                    <Action targetView="b2b_contract_AgreementBO_AgreementInfo" env="#{{agreementId: this.record.id}}" openViewType="Dialog"/>
                </RenderType>
            </Field>
            <Field name="jfCompanyBO" readonly="true"/>
            <Field name="departmentStr" readonly="true"/>
            <Field name="yfCompanyBO" readonly="true"/>
            <Field name="category" label="分类" readonly="true"/>
            <Field name="bidTaskCode" readonly="true"/>
        </Fields>

        <Actions>
            <Action layout="Footer" type="Cancel" action="#{goBackToTheList}"/>
            <Action type="Submit" action="#{nextSteps}" label="下一步" layout="Footer" />
            <Action openViewType="Dialog" targetView="b2b_contract_AgreementBO_ChooseSelfConstructionAgreement" label="添加协议" layout="Middle" />
        </Actions>
    </TableForm>
</View>