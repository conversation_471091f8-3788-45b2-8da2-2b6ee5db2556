package io.terminus.gaia.contract.func.contract.write;

import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.gaia.contract.model.contract.ContractTermBO;
import io.terminus.trantorframework.api.annotation.Function;

import java.util.List;

/**
 * 批量删除合同条款
 * <AUTHOR>
 * @date 2021-04-15
 */
@Function(name = "delete contract terms")
public interface BatchDeleteContractTermsFunc {
    /**
     * 批量删除合同条款
     * @param contract 框架合同
     * @return 合同条款
     */
    List<ContractTermBO> execute(ContractBO contract);
}
