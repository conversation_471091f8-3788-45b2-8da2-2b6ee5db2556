package io.terminus.gaia.contract.trigger;

import io.terminus.gaia.contract.func.contract.trigger.ContractAttachmentCreateFunc;
import io.terminus.gaia.contract.func.contract.trigger.ContractSapGuidCreateFunc;
import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.trantorframework.api.annotation.Trigger;
import io.terminus.trantorframework.sdk.trigger.AddEvent;
import io.terminus.trantorframework.sdk.trigger.AddEventTriggerListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 合同更新完成后，监听异步生成PDF文件
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Trigger(modelClass = ContractBO.class, listenFields = {ContractBO.contractAttachment_field, ContractBO.sapGuid_field})
public class ContractCreateHandledTrigger implements AddEventTriggerListener<ContractBO> {

    private final ContractAttachmentCreateFunc contractAttachmentCreateFunc;

    private final ContractSapGuidCreateFunc contractSapGuidCreateFunc;

    @Override
    public void execute(AddEvent<ContractBO> addEvent) {
        ContractBO contract = addEvent.getBody();
        log.info("进入合同创建监听...contractCode为{}",contract.getContractCode());
        //合同文件创建function
        contractAttachmentCreateFunc.execute(contract);
        //同步合同创建function
        contractSapGuidCreateFunc.execute(contract);
    }
}
