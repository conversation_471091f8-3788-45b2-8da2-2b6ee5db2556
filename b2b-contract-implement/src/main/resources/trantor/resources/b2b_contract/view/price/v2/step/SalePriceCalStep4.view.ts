import {Controller,Toast} from 'nusi-sdk'

export default class extends Controller {


    calAll = async (ctx) => {
        const container = this.getContainerByKey('salePriceCal');
        const sumbitData = await container.instance.submitCalc(true) // 如果是全部测算，调submitCalc(true)
        const { finish, result } = sumbitData; // finish === false 时为用户关闭确认弹窗，不提交。result是提交返回
        if (finish) {
            console.log(result)

            const data = {
                ...this.pageRecord,
                spcUid: result.value,
            }

            this.openView('b2b_contract_SalePriceCalBO_SalePriceCalStep5', {
                openViewType: 'Self',
                record: data,
            })
        }
    }

    calSel = async (ctx) => {
        const container = this.getContainerByKey('salePriceCal');
        const selectedData = container.instance.getSelectedData() // 如果是所选测算，调submitCalc(true)
        if (!selectedData.length) {
            Toast.warning('未选中标品行')
            return
        }
        const { finish, result } = await container.instance.submitCalc(); // finish === false 时为用户关闭确认弹窗，不提交。result是提交返回
        if (finish) {
            console.log(result)

            const data = {
                ...this.pageRecord,
                spcUid: result.value,
            }

            this.openView('b2b_contract_SalePriceCalBO_SalePriceCalStep5', {
                openViewType: 'Self',
                record: data,
            })
        }
    }
}