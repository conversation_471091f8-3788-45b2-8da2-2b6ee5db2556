package io.terminus.gaia.app.b2b.contract.func.contract.read;

import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.trantorframework.api.annotation.AnonymousAccess;
import io.terminus.trantorframework.api.annotation.Function;

import java.util.List;

/**
 * @Author: fengbo
 * @Date: 2021/10/14
 */
@Function(name = "get sale contract list func")
@AnonymousAccess
public interface GetSaleContractListFunc {


    /***
     * 根据框架合同查询销售合同集合
     * @param contract
     * @return
     */
    List<ContractBO> execute(ContractBO contract);
}
