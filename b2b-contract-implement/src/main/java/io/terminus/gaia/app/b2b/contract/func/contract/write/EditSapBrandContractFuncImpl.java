package io.terminus.gaia.app.b2b.contract.func.contract.write;

import io.terminus.gaia.app.b2b.contract.model.ContractExtBO;
import io.terminus.gaia.app.b2b.item.model.OutBrandBO;
import io.terminus.trantorframework.api.annotation.FlowImpl;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Author: fengbo
 * @Date: 2021/11/9
 */
@FunctionImpl
public class EditSapBrandContractFuncImpl implements EditSapBrandContractFunc {
    @Override
    public void execute(ContractExtBO contractExtBO) {
        //处理sap品牌信息
        if (StringUtils.isEmpty(contractExtBO.getBrandCode())) {
            return;
        }
        //查询sap品牌信息
        List<OutBrandBO> outBrandBOS = DS.findAll(OutBrandBO.class, "*", "brandCode=?", contractExtBO.getBrandCode());
        if (!CollectionUtils.isEmpty(outBrandBOS)) {
            ContractExtBO updateContract=new ContractExtBO();
            updateContract.setSapBrand(outBrandBOS.get(0));
            updateContract.setId(contractExtBO.getId());
            DS.update(updateContract);
        }
    }
}
