<?xml version="1.0" encoding="UTF-8"?>
<View title="CreateContract" type="List" forModel="contract_ContractBO" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://trantor-docs-dev.app.terminus.io/static/v0.16.x/schema/base.xsd">
  <Form key="contract_Contract" model="contract_ContractBO">
    <Fields>
      <Field name="isUseTemplate" initValue="#{true}">
        <RenderType>
          <Radio/>
        </RenderType>
      </Field>
      <Field name="contractAttachment" show="#{!this.data.isUseTemplate}">
        <Validations>
          <Validation required="#{true}" message="please select contract file"/>
        </Validations>
      </Field>
      <Field name="contractTemplate" show="#{!!this.data.isUseTemplate}">
        <RenderType>
          <ModelSelect dataCondition="formTemplate.status='ENABLE'"/>
        </RenderType>
        <Validations>
          <Validation required="#{true}" message="please select contract template"/>
        </Validations>
      </Field>
    </Fields>
    <Actions>
      <Action type="Submit" layout="Footer"
              env="#{{isUseTemplate: this.data.isUseTemplate,
                    contractAttachment: this.data.contractAttachment, contractTemplate: this.data.contractTemplate}}"
              targetView="contract_ContractBO_ContractCreate"/>
    </Actions>
  </Form>
</View>