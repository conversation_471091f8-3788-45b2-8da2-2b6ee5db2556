package io.terminus.gaia.contract.flow.sign.write;

import io.terminus.gaia.contract.func.sign.validate.ValidateSigningSignatoriesFunc;
import io.terminus.gaia.contract.func.sign.write.SignedSignatoriesFunc;
import io.terminus.gaia.contract.model.sign.SignatoriesBO;
import io.terminus.gaia.contract.tmodel.sign.SignatoriesSignTO;
import io.terminus.trantorframework.api.annotation.FlowImpl;
import lombok.RequiredArgsConstructor;

/**
 * 签署方签署
 * @Date 2021/4/14
 * <AUTHOR>
 */
@RequiredArgsConstructor
@FlowImpl(name = "signing signatories")
public class SigningSignatoriesFlowImpl implements SigningSignatoriesFlow {
    private final ValidateSigningSignatoriesFunc validateSigningSignatoriesFunc;
    private final SignedSignatoriesFunc signedSignatoriesFunc;

    /**
     * 签署
     *
     * @param signatoriesBO 签署方
     * @return 签署后的签署方信息
     */
    @Override
    public void execute(SignatoriesBO signatoriesBO) {
        // 签署完成的防御性校验
        SignatoriesSignTO signTO = validateSigningSignatoriesFunc.execute(signatoriesBO);

        // 提交签署
        signedSignatoriesFunc.execute(signTO);
    }
}
