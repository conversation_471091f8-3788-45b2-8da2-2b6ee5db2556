package io.terminus.gaia.app.b2b.contract.tmodel.price;

import io.terminus.gaia.app.b2b.contract.model.PayableSchemeTemplateBO;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.FieldType;
import io.terminus.trantorframework.api.annotation.TransientModel;
import lombok.*;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TransientModel
public class SkuPayableSchemeRespTO extends BaseModel<Long> {

    /**
     * sku编码
     */
    @Field(name = "sku编码")
    private String skuNo;

    /**
     * 项目id
     */
    @Field(name = "项目id")
    private Long projectId;

    /**
     * 项目id
     */
    @Field(name = "应收账款方案", type = FieldType.Json)
    private List<PayableSchemeTemplateBO> payableSchemeTemplateBOList;

}
