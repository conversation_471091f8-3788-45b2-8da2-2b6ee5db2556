package io.terminus.gaia.app.b2b.contract.func.contract.convert;

import io.terminus.gaia.app.b2b.contract.func.contract.read.GetMaterialFinalContractFunc;
import io.terminus.gaia.app.b2b.contract.tmodel.sync.*;
import io.terminus.gaia.app.b2b.item.dict.sku.MaterialsListStatusDict;
import io.terminus.gaia.app.b2b.item.model.SkuBOExt;
import io.terminus.gaia.contract.dict.contract.ContractStatusDict;
import io.terminus.gaia.contract.dict.contract.guarantee.GuaranteeTypeDict;
import io.terminus.gaia.contract.func.contract.convert.ConvertOss2FtpFunc;
import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.gaia.contract.msg.contract.ContractExMsg;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.trantor.module.base.model.result.StringResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.api.type.Attachment;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: fengbo
 * @Date: 2021/9/27
 */
@Slf4j
@RequiredArgsConstructor
@FunctionImpl
public class ConvertSyncAgreementToSapDataFuncImpl implements ConvertSyncAgreementToSapDataFunc {

    private final GetMaterialFinalContractFunc getMaterialFinalContractFunc;

    private final ConvertOss2FtpFunc convertOss2FtpFunc;

    public final static Map<String, String> sapContractStatusMap = new HashMap();

    public final static Map<String, String> sapApproveStatusMap = new HashMap();

    public final static Map<String, String> sapSupplierApproveStatusMap = new HashMap();

    private final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");

    static {
        sapContractStatusMap.put("INVITED_BIDDING", "0101");
        sapContractStatusMap.put("PRICE_COMPARISON", "0102");
        sapContractStatusMap.put("DIRECT_ENTRUST", "0103");
        sapContractStatusMap.put("PUBLIC_BID", "0105");
        sapApproveStatusMap.put("APPROVED_PENDING", "01");
        sapApproveStatusMap.put("APPROVING", "02");
        sapApproveStatusMap.put("APPROVED", "03");
        sapApproveStatusMap.put("APPROVED_REJECT", "04");
        sapSupplierApproveStatusMap.put("APPROVED_PENDING", "01");
        sapSupplierApproveStatusMap.put("APPROVING", "05");
        sapSupplierApproveStatusMap.put("APPROVED", "06");
        sapSupplierApproveStatusMap.put("APPROVED_CANCEL", "12");
        sapSupplierApproveStatusMap.put("APPROVED_REJECT", "13");
    }

    @Override
    public PushToSapAgreementTO execute(ContractBO contractBO) {
        //查询集采协议信息
        contractBO = DS.findById(ContractBO.class, contractBO.getId(), "*,operator.*,partyA.*,partyB.*,partyCList.*,brandBOList.*,categoryBOList.*,contractDistributorList.*,contractDistributorList.performanceEntity.*," +
                "serviceAreaList.*,parent.*");
        if (contractBO == null) {
            throw new BusinessException(ContractExMsg.CONTRACT_IS_NULL);
        }
        PushToSapAgreementTO sapAgreementTO = new PushToSapAgreementTO();
        //fixme 多个供应品类
        if (CollectionUtils.isNotEmpty(contractBO.getCategoryBOList())) {
            sapAgreementTO.setVendorType(contractBO.getCategoryBOList().get(0).get("outCategoryCode"));
        }
        //系统编号
        sapAgreementTO.setContractId(String.valueOf(contractBO.getId()));
        //合同编号
        sapAgreementTO.setContractCode(contractBO.getContractCode());
        //归档编号
        sapAgreementTO.setContArchId(contractBO.getFilingCode());
        //经办人
        sapAgreementTO.setZuser(contractBO.getOperator().getUsername());
        //经办部门
        sapAgreementTO.setDepart(contractBO.getOperatorDepartment().getUserDeptNo());
        //组织类型
        sapAgreementTO.setOrgType("R");
        //合同状态
        if (ContractStatusDict.SIGNED.equals(contractBO.getContractStatusDict())) {
            //已归档
            sapAgreementTO.setStatus("08");
        } else {
            //已作废
            sapAgreementTO.setStatus("12");
        }
        //合同名称
        sapAgreementTO.setContractName(contractBO.getContractName());
        //合同类型
        if (contractBO.getIsSupplementContract()) {
            //补充协议
            sapAgreementTO.setContType3("05");
        } else {
            //集采协议
            sapAgreementTO.setContType3("02");
        }
        //采购模式,默认集团采购
        sapAgreementTO.setProcMode("01");
        //采购方式
        sapAgreementTO.setProcType(sapContractStatusMap.get(contractBO.getBidWay()));
        if (StringUtils.isBlank(sapAgreementTO.getProcType())) {
            sapAgreementTO.setProcType("0103");
        }
        //甲方编码
        //fixme 甲方编码 数据有问题 4054
        sapAgreementTO.setPartyA(contractBO.getPartyA().getEntityCode());
        //乙方编码
        //fixme 乙方 1000005279
        sapAgreementTO.setPartyB(contractBO.getPartyB().getEntityCode());
        //丙方编码
        if (contractBO.getIsMultiParty()) {
            sapAgreementTO.setPartyC(StringUtils.join(contractBO.getPartyCList().stream().map(EntityBO::getEntityCode).toArray(), ","));
        }
        //货币
        sapAgreementTO.setWaers("CNY");
        //归档日期
        //fixme  目前为空
        if (contractBO.getSignDate() != null) {
            sapAgreementTO.setArchiveDate(simpleDateFormat.format(contractBO.getSignDate()));
        }
        //品牌
        if (CollectionUtils.isNotEmpty(contractBO.getBrandBOList())) {
//            sapAgreementTO.setBrand(contractBO.getBrandBOList().get(0).getBrandCode());
            //fixme 品牌先固定
            sapAgreementTO.setBrand("1000000231");
        }
        //生效日期
        sapAgreementTO.setFromDate(simpleDateFormat.format(contractBO.getStartDate()));
        //失效日期
        sapAgreementTO.setToDate(simpleDateFormat.format(contractBO.getEndDate()));
        //合同备注
        sapAgreementTO.setContComment(contractBO.getRemark());
        //非清单类
        if (contractBO.getIsNotBOM()) {
            sapAgreementTO.setMatFlag("X");
        }
        //集团协议编码
        sapAgreementTO.setRelGroupContId(contractBO.getContractCode());
        //分类
//        sapAgreementTO.setVendorType(StringUtils.join(contractBO.getCategoryBOList().stream().map(CategoryBO::getCategoryCode).toArray(), ","));
        //是否有保函
        sapAgreementTO.setGuaranteeFlag("2");
        if (contractBO.getIsHaveGuarantee()) {
            sapAgreementTO.setGuaranteeFlag("1");
        }
        //保函类型
        if (contractBO.getGuaranteeTypeDict() != null) {
            if (GuaranteeTypeDict.QUALITY_GUARANTEE.equals(contractBO.getGuaranteeTypeDict())) {
                sapAgreementTO.setGuaranteeType("1");
            } else {
                sapAgreementTO.setGuaranteeType("2");
            }
        }
        //保证金/保函比列
        sapAgreementTO.setGuaranteeAmt(contractBO.getGuaranteeRatio());
        //商票价格状态
        sapAgreementTO.setInvStatus(sapApproveStatusMap.get(contractBO.getApproveStatus()));
        //采购支付方式
        sapAgreementTO.setPayMethod(contractBO.getPartyAPayWay());
        //安装支付方式
        sapAgreementTO.setFixMethod(contractBO.getInstallPayWay());
        //商票支付比列
        sapAgreementTO.setTpRatio(contractBO.getCommercialBillPayment());
        //商票期限
        sapAgreementTO.setSpLimit(contractBO.getCommercialTicketPeriod());
        //商票经济条款
        sapAgreementTO.setMacTranslation(contractBO.getEconomicClause());
        //盖章状态
        sapAgreementTO.setSignState("06");
        //供应商范围变更状态
        sapAgreementTO.setVedorsChgState("06");
        //记录建立日期
        //fixme 格式 yyyyMMdd
        sapAgreementTO.setDatum(simpleDateFormat.format(contractBO.getCreatedAt()));
        //审批通过日期
        //fixme 格式 yyyyMMdd
        if (contractBO.getAgreementApprovedDate() == null) {
            contractBO.setAgreementApprovedDate(contractBO.getCreatedAt());
        }
        sapAgreementTO.setPassDat(simpleDateFormat.format(contractBO.getAgreementApprovedDate()));
        //立项编号
        sapAgreementTO.setObjectId(contractBO.getProjectEstablishmentCode());
        //引用协议编号
        if (contractBO.getIsSupplementContract()) {
            if (contractBO.getParent() != null) {
                sapAgreementTO.setRelAgreementId(contractBO.getParent().getSapGuid());
            }
        }
        String contractId = String.valueOf(contractBO.getId());
        String approveStatus = contractBO.getApproveStatus();
        String contractStatus = contractBO.getContractStatusDict();
        String contractCode = contractBO.getContractCode();
        String userName = contractBO.getOperator().getUsername();
        Date createDate = contractBO.getCreatedAt();
        Boolean isUseTemplate = contractBO.getIsUseTemplate();
        //获取绑定了材料清单的集采协议
        //ContractBO materialContract= getMaterialFinalContractFunc.execute(contractBO);
        //材料信息
        List<ContractMaterialsTO> materialList = Lists.newArrayList();
        //查询材料清单信息
        //fixme 行项目为空
        List<SkuBOExt> allSkuList =
                DS.findAll(SkuBOExt.class, "*,taxRate.*,thingStockUnit.*,entity.*", SkuBOExt.protocol_field + "=?", contractBO.getId());
        if (CollectionUtils.isNotEmpty(allSkuList)) {
            //商票支付比列
            BigDecimal spRatio = contractBO.getCommercialBillPayment();
            allSkuList.forEach(sku -> {
                ContractMaterialsTO materials = new ContractMaterialsTO();
                materials.setContractId(contractId);
                materials.setItem(String.valueOf(sku.getId()));
                materials.setSkuCode(sku.getSkuCode());
                materials.setTaxRate(sku.getTaxRate().getTaxRate());
                materials.setUnit(sku.getThingStockUnit().getUnitName());
                //含税单价
                materials.setPrice(sku.getDefaultPrc().getValue());
                //不含税单价
                materials.setTaxFreePrice(sku.getDefaultPrcWithoutTax().getValue());
                //商票单价
                materials.setSalePrice(sku.getCommercialPaperPrc().getValue());
                //商票单价(不含税)
                materials.setTaxFreeSalePrice(sku.getCommercialPaperPrcWithoutTax().getValue());
//                if (!sku.getIsContractTax()) {
//                    //含税价 待计算
//                    BigDecimal taxRate = sku.getTaxRate() == null ? BigDecimal.ZERO : sku.getTaxRate().getTaxRate();
//                    String cashPrice = sku.getDefaultPrc().add(sku.getDefaultPrc().multiply(taxRate).divide(new BigDecimal(100))).toString();
//                    String commercialPrice = sku.getCommercialPaperPrc().add(sku.getCommercialPaperPrc().multiply(taxRate).divide(new BigDecimal(100))).toString();
//
//                    //不含税单价
//                    materials.setTaxFreePrice(sku.getDefaultPrc().getValue());
//                    //商票单价
//                    materials.setSalePrice(new BigDecimal(commercialPrice).setScale(2, RoundingMode.UP));
//                    //商票单价(不含税)
//                    materials.setTaxFreeSalePrice(sku.getCommercialPaperPrc().getValue());
//                } else {
//                    //不含税价 待计算
//                    BigDecimal taxRate = sku.getTaxRate() == null ? BigDecimal.ZERO : sku.getTaxRate().getTaxRate();
//                    String cashPrice = sku.getDefaultPrc().add(sku.getDefaultPrc().multiply(taxRate).divide(new BigDecimal(100))).toString();
//                    String commercialPrice = sku.getCommercialPaperPrc().add(sku.getCommercialPaperPrc().multiply(taxRate).divide(new BigDecimal(100))).toString();
//                    //不含税单价
//                    materials.setTaxFreePrice(new BigDecimal(cashPrice).setScale(2, RoundingMode.UP));
//                    //含税单价
//                    materials.setPrice(sku.getDefaultPrc().getValue());
//                    //商票单价(不含税)
//                    materials.setTaxFreeSalePrice(new BigDecimal(commercialPrice).setScale(2, RoundingMode.UP));
//                    //商票单价
//                    materials.setSalePrice(sku.getCommercialPaperPrc().getValue());
//                }
                //物料编码
                materials.setManter(sku.getSkuCode());
                //物料名称
                materials.setManterName(sku.getThingName());
                //材料描述
                //材料类型
                //适用范围
                //规格
                materials.setStandard(sku.getThingSize());
                //型号
                materials.setType(sku.getThingSize());
                //材料要求
                //开票产品名称
                materials.setKpManterName(sku.getInvoiceProductName());
                //开票规格型号
                materials.setKpStandard(sku.getInvoiceSpecification());
                //税收分类编码
                materials.setKpNumber(sku.getTaxCategoryCode());
                //其他费用
                //标准化产品标识
                //商票支付比列
                materials.setSpRatio(spRatio);
                //集采外材料明细标识
                materials.setOutsideFlag(!sku.getIsInsideContract() ? "X" : StringUtils.EMPTY);
                //集采内材料明细审批状态
                //materials.setStatus(sku.getMaterialsStatusDict());
                //记录建立日期
                materials.setDatum(simpleDateFormat.format(sku.getCreatedAt()));
                //是否有效
                materials.setValid(sku.getIsUsable() ? "10" : "20");
                if (MaterialsListStatusDict.DISABLED.equals(sku.getMaterialsStatusDict())) {
                    materials.setValid("20");
                }
                materialList.add(materials);
                materials = null;
            });
        }
        sapAgreementTO.setMaterialList(materialList);
        //协议供应商范围信息
        List<ContractSupplierTO> agreementSupplierList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(contractBO.getContractDistributorList())) {
            contractBO.getContractDistributorList().forEach(distributor -> {
                ContractSupplierTO supplier = new ContractSupplierTO();
                supplier.setContractId(contractId);
                supplier.setLifnr(distributor.getPerformanceEntity().getEntityCode());
                supplier.setStatus(sapSupplierApproveStatusMap.get(approveStatus));
                supplier.setStatus(ContractStatusDict.SIGNED.equals(contractStatus) ? "0" : "1");
                supplier.setDatum(simpleDateFormat.format(distributor.getCreatedAt()));
                supplier.setUname(distributor.getCreatedBy() == null ? "admin" : distributor.getCreatedBy().getUsername());
                agreementSupplierList.add(supplier);
                supplier = null;
            });
        }
        sapAgreementTO.setAgreementSupplierList(agreementSupplierList);
        //协议适用范围
        List<ContractDepartmentTO> agreementRange = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(contractBO.getServiceAreaList())) {
            contractBO.getServiceAreaList().forEach(depart -> {
                ContractDepartmentTO departmentTO = new ContractDepartmentTO();
                departmentTO.setContractId(contractId);
                departmentTO.setRange(depart.getDepartmentCode());
                agreementRange.add(departmentTO);
                departmentTO = null;
            });
        }
        sapAgreementTO.setAgreementRange(agreementRange);
        //附件信息
        List<ContractSapAttachmentTO> attachment = Lists.newArrayList();
        if (contractBO.getContractAttachment() != null) {
            List<Attachment.File> fileList = contractBO.getContractAttachment().getFiles();
            fileList.forEach(file -> {
                StringResult oldUrl = new StringResult(file.getUrl().startsWith("https:") ? file.getUrl() : "https:" + file.getUrl());
                StringResult fileName = new StringResult(file.getName());
                StringResult ftpUrl = convertOss2FtpFunc.execute(oldUrl, fileName);
                ContractSapAttachmentTO attachmentTO = new ContractSapAttachmentTO();
                attachmentTO.setProcGuid(contractCode);
                attachmentTO.setFileName(file.getName());
                attachmentTO.setPath(ftpUrl.getValue() == null ? file.getUrl().startsWith("https:") ? file.getUrl() : "https:" + file.getUrl() : ftpUrl.getValue());
                attachmentTO.setDatum(simpleDateFormat.format(createDate));
                attachmentTO.setUzeit(simpleDateFormat.format(createDate));
                attachmentTO.setUname(userName);
                attachmentTO.setContTemplate(isUseTemplate ? "1" : "0");
                attachment.add(attachmentTO);
                attachmentTO = null;
            });
        }
        sapAgreementTO.setAttachment(attachment);

        sapAgreementTO.setSpRatio(contractBO.getCommercialBillPayment());
        sapAgreementTO.setTpRatio(contractBO.getCommercialTicketDiscount());
        sapAgreementTO.setSpLimit(contractBO.getCommercialTicketPeriod());
        return sapAgreementTO;
    }
}
