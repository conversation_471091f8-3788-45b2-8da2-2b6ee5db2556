package io.terminus.gaia.contract.msg.sign;

import io.terminus.trantorframework.api.annotation.Message;

/**
 * 签署方异常消息定义
 *
 * <AUTHOR>
 */
public interface SignatoriesMsg {
    /**
     * 签署顺序出错
     */
    @Message("signing order fail")
    String SINGING_ORDER_FAIL = "Contract.signingOrderFail";
    /**
     * 签署人状态不符合
     */
    @Message("signatories status mismatch")
    String SIGNATORIES_STATUS_MISMATCH = "Contract.signatoriesStatusMismatch";

}
