package io.terminus.gaia.app.b2b.contract.func.agreement.read;

import io.terminus.gaia.app.b2b.contract.model.AgreementBO;
import io.terminus.gaia.app.b2b.contract.model.query.QAgreementBO;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * <AUTHOR>
 */
@Function
public interface PagingAgreementFunc {

    Paging<AgreementBO> execute(QAgreementBO qAgreementBO);
}
