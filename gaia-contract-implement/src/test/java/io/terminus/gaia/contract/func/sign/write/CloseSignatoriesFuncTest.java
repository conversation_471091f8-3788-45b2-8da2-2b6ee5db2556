package io.terminus.gaia.contract.func.sign.write;

import io.terminus.gaia.contract.BaseService;
import io.terminus.gaia.contract.dict.sign.SignBillTypeDict;
import io.terminus.gaia.contract.dict.sign.SignTaskStatusDict;
import io.terminus.gaia.contract.dict.sign.SignatoriesStatusDict;
import io.terminus.gaia.contract.func.sign.write.BatchCreateSignatoriesFunc;
import io.terminus.gaia.contract.func.sign.write.CloseSignatoriesFunc;
import io.terminus.gaia.contract.model.sign.SignTaskBO;
import io.terminus.gaia.contract.model.sign.SignatoriesBO;
import io.terminus.gaia.md.dict.EntityCategoryDict;
import io.terminus.gaia.md.dict.EntityStatusDict;
import io.terminus.gaia.md.dict.EntityTypeDict;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.trantorframework.api.type.Attachment;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * 测试：关闭签署方单据
 * @Date 2021/4/15
 * <AUTHOR>
 */
@Slf4j
public class CloseSignatoriesFuncTest extends BaseService {

    private CloseSignatoriesFunc closeSignatoriesFunc;

    private BatchCreateSignatoriesFunc batchCreateSignatoriesFunc;

    @Test
    public void execute(){

        // 组装数据
        Attachment attachment = new Attachment();
        List<Attachment.File> files = new ArrayList<>();
        Attachment.File file = new Attachment.File();
        file.setUrl("//terminus-trantor.oss-cn-hangzhou.aliyuncs.com/trantor/attachments/33e45698-fddb-43bb-ab96-fc2bf38a8f22.docx");
        file.setName("模板文件 2的副本 2.docx");
        file.setType("docx");
        files.add(file);
        attachment.setFiles(files);

        SignTaskBO signTaskBO = SignTaskBO.builder()
                .title("测试一下")
                .company(EntityBO.builder()
                        .entityName("测试公司")
                        .entityType(EntityTypeDict.COMPANY)
                        .entityStatus(EntityStatusDict.ENABLED)
                        .entityCategory(EntityCategoryDict.MANUFACTURE)
                        .build())
//                .signAttachment(attachment)
                .signStatusDict(SignTaskStatusDict.SIGNING)
                .signBillTypeDict(SignBillTypeDict.CONTRACT)
                .signBillCode("123")
                .build();
        Long id = DS.create(signTaskBO).getValue().longValue();
        signTaskBO = DS.findById(SignTaskBO.class,id);

        List<SignatoriesBO> signatoriesBOList = new ArrayList<>();
        signatoriesBOList.add(SignatoriesBO.builder().entity(EntityBO.builder()
                .entityName("ceshi1")
                .entityType(EntityTypeDict.COMPANY)
                .entityStatus(EntityStatusDict.ENABLED)
                .entityCategory(EntityCategoryDict.MANUFACTURE)
                .build()).build());
        signatoriesBOList.add(SignatoriesBO.builder().entity(EntityBO.builder()
                .entityName("ceshi2")
                .entityType(EntityTypeDict.COMPANY)
                .entityStatus(EntityStatusDict.ENABLED)
                .entityCategory(EntityCategoryDict.MANUFACTURE)
                .build()).build());
        signatoriesBOList = batchCreateSignatoriesFunc.execute(signatoriesBOList,signTaskBO);
        signTaskBO.setSignatoriesList(signatoriesBOList);
        // 调用
        closeSignatoriesFunc.execute(signTaskBO);

        for (SignatoriesBO s : signatoriesBOList){
            String nowStatus = DS.findById(SignatoriesBO.class,s.getId()).getSignatoriesStatusDict();
            log.info("before status：{},now status:{}",s.getSignatoriesStatusDict(),nowStatus);
            Assert.assertTrue(StringUtils.equals(nowStatus, SignatoriesStatusDict.CLOSED));

        }


    }
}
