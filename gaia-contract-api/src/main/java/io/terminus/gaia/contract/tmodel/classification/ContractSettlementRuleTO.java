package io.terminus.gaia.contract.tmodel.classification;

import io.terminus.gaia.contract.dict.classification.SettlementCycleDict;
import io.terminus.trantorframework.api.RootModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.TransientModel;
import io.terminus.trantorframework.api.annotation.typemeta.DictionaryMeta;
import io.terminus.trantorframework.api.annotation.typemeta.TextMeta;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 结算规则
 * <AUTHOR>
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TransientModel(name = "Contract Settlement Rule TO")
@Data
@Accessors(chain = true)
public class ContractSettlementRuleTO extends RootModel<Long> {

    /**
     * 是否允许修改规则
     */
    @Field(name = "can modify")
    private Boolean canModify;

    /**
     * 结算周期
     */
    @DictionaryMeta(value = SettlementCycleDict.class)
    @Field(name = "settlement cycle")
    private String settlementCycle;

    /**
     * 首个结算日
     */
    @Field(name = "first settlement date")
    private Date fistSettlementDate;

    /**
     * 是否允许修改单价
     */
    @Field(name = "can modify unit price")
    private Boolean canModifyUnitPrice;

    /**
     * 结算规则条款
     */
    @Field(name = "term")
    @TextMeta(length = 1024)
    private String term;
}
