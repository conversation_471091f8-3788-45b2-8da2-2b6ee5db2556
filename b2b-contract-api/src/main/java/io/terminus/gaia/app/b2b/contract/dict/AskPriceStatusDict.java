package io.terminus.gaia.app.b2b.contract.dict;

import io.terminus.trantorframework.api.annotation.Dictionary;
import io.terminus.trantorframework.api.annotation.DictionaryItem;
import io.terminus.trantorframework.api.annotation.typemeta.Icon;

/**
 * 运营端-询价单
 * <AUTHOR>
 */
@Dictionary
public interface AskPriceStatusDict {

    //只要有任何供应商未完成报价，就是报价中
    @DictionaryItem(value = "报价中", icon = Icon.dot, iconColor = Icon.Color.Yellow, displayOrder = 1)
    String PRICE_WRITING = "price_writing";

    //全部供应商都已报价，就是待商务部和市场履约部确认
    @DictionaryItem(value = "待确认", icon = Icon.dot, iconColor = Icon.Color.Blue, displayOrder = 2)
    String PRICE_WAITING_CONFIRM = "price_waiting_confirm";

    //只要商务部和市场履约部确认，就是已完成
    @DictionaryItem(value = "已完成", icon = Icon.dot, iconColor = Icon.Color.Green, displayOrder = 3)
    String FINISH = "finish";

    //业务可能作废询价单
    @DictionaryItem(value = "已作废", icon = Icon.dot, iconColor = Icon.Color.Red, displayOrder = 4)
    String INVALID = "invalid";
}
