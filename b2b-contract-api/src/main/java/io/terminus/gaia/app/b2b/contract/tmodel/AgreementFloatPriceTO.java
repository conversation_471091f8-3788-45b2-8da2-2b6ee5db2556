package io.terminus.gaia.app.b2b.contract.tmodel;

import io.terminus.gaia.app.b2b.contract.tmodel.sync.AgreementPriceLine;
import io.terminus.gaia.item.dict.*;
import io.terminus.gaia.item.model.price.OverlayPriceBO;
import io.terminus.gaia.md.model.CategoryBO;
import io.terminus.gaia.organization.model.DepartmentBO;
import io.terminus.trantorframework.api.RootModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.FieldType;
import io.terminus.trantorframework.api.annotation.TransientModel;
import io.terminus.trantorframework.api.annotation.typemeta.DictionaryMeta;
import io.terminus.trantorframework.api.annotation.typemeta.LinkMeta;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-06-01
 * @descrition
 */
@Data
@TransientModel(name = "协议清单浮动价批量创建入参")
public class AgreementFloatPriceTO extends RootModel<Long> {


    /**
     * 关联供应商
     */
    @Field(name = "使用单位",type = FieldType.Json)
    @LinkMeta
    private DepartmentBO supplier;

    /**
     * 类目id
     */
    @Field(name = "Category",type = FieldType.Json)
    @LinkMeta
    private CategoryBO category;


    /**
     * 计价基准
     */
    @DictionaryMeta(PricingBasisType.class)
    @Field(name = "pricing basis")
    private String pricingBasis;

    /**
     * 方案模板
     */
    @DictionaryMeta(SchemeTemplateDict.class)
    @Field(name = "scheme template")
    private String schemeTemplate;


    /**
     * 价格计算节点
     */
    @DictionaryMeta(CalculatePointDict.class)
    @Field(name = "calculate point", type = FieldType.MultiDictionary)
    private List<String> calculatePoint;


    /**
     * 价格浮动方式
     */
    @Field(name = "fluctuation type")
    @DictionaryMeta(FluctutationType.class)
    private String fluctuationType;

    /**
     * 价格平台
     */
    @DictionaryMeta(PaltformDict.class)
    @Field(name = "platform dict")
    private String pricePlatform;

    /**
     * 取价规则
     */
    @Field(name = "pricing rules")
    @DictionaryMeta(PricingRulesDict.class)
    private String pricingRules;

    @Field(name = "协议价格配置行",type = FieldType.Json)
    private List<AgreementPriceLine> agreementPriceLines;


    @Field(name = "叠加价",type = FieldType.Json)
    private List<OverlayPriceBO> overlaysPrices;



//
//    public static void main(String[] args) {
//        List<AgreementPriceLineDetail> lineDetails = new ArrayList<>();
//        AgreementPriceLineDetail detail1 = new AgreementPriceLineDetail();
//
//        detail1.setFloatingValue(new Currency(1));
//        detail1.setReferenceDict(ReferenceDict.PURCHASING_AREA);
//        detail1.setReferenceDistrict(null);
//        DistrictBO districtBO = new DistrictBO();
//        districtBO.setId(100L);
//        districtBO.setDistrictName("北京市");
//        DistrictBO districtBO1 = new DistrictBO();
//        districtBO1.setId(101L);
//        districtBO1.setDistrictName("天津市");
//        detail1.setPurchasingAreas(ListUtil.of(districtBO,districtBO1));
//
//        lineDetails.add(detail1);
//
//
//        AgreementPriceLineDetail detail2 = new AgreementPriceLineDetail();
//
//        detail2.setFloatingValue(new Currency(2));
//        detail2.setReferenceDict(ReferenceDict.SPECIFIED_AREA);
//        DistrictBO districtBO3 = new DistrictBO();
//        districtBO3.setId(10555L);
//        districtBO3.setDistrictName("青岛市");
//        detail2.setReferenceDistrict(districtBO3);
//        DistrictBO districtBO2 = new DistrictBO();
//        districtBO2.setId(103L);
//        districtBO2.setDistrictName("南京市");
//        detail2.setPurchasingAreas(ListUtil.of(districtBO2));
//
//        lineDetails.add(detail2);
//
//        AgreementPriceLine line = new AgreementPriceLine();
//        AgreementBO agreementBO = new AgreementBO();
//        agreementBO.setId(1111L);
//        line.setAgreementBO(agreementBO);
//        line.setDetails(lineDetails);
//
//        SimplePropertyPreFilter filter = new SimplePropertyPreFilter();
//        filter.getExcludes().add("__trantorExtendFields");
//        filter.getExcludes().add("_fields");
//        System.out.println(JSON.toJSONString(line, filter));
//
//
//        AgreementFloatPriceTO agreementFloatPriceTO = new AgreementFloatPriceTO();
//        agreementFloatPriceTO.setSupplier(new DepartmentBO());
//        agreementFloatPriceTO.setCategory(new CategoryBO());
//        agreementFloatPriceTO.setPricingBasis(PricingBasisType.WITHOUT_CHARGE);
//        agreementFloatPriceTO.setCalculatePoint(Lists.newArrayList(CalculatePointDict.PLACE_ORDER, CalculatePointDict.TAKE_DELIVERY));
//        agreementFloatPriceTO.setFluctuationType(FluctutationType.ADDITION);
//        agreementFloatPriceTO.setPricePlatform("兰格网");
//        agreementFloatPriceTO.setPricingRules(PricingRulesDict.FIRST_PRICE);
//        agreementFloatPriceTO.setAgreementPriceLines(Lists.newArrayList(line));
//
//        System.out.println(JSON.toJSONString(agreementFloatPriceTO, filter));
//
//
//
//
//    }

}
