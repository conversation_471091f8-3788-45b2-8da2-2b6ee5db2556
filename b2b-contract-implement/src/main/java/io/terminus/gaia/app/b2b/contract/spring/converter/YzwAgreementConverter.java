package io.terminus.gaia.app.b2b.contract.spring.converter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import io.terminus.gaia.app.b2b.contract.dict.*;
import io.terminus.gaia.app.b2b.contract.model.AgreementBO;
import io.terminus.gaia.app.b2b.contract.model.AgreementContactBO;
import io.terminus.gaia.app.b2b.contract.model.AgreementDetailBO;
import io.terminus.gaia.app.b2b.contract.model.AgreementDetailMappingBO;
import io.terminus.gaia.app.b2b.contract.spring.model.yzw.YzwBiddingListItem;
import io.terminus.gaia.app.b2b.contract.spring.model.yzw.YzwContract;
import io.terminus.gaia.app.b2b.contract.spring.model.yzw.YzwContractArea;
import io.terminus.gaia.app.b2b.contract.spring.model.yzw.YzwContractMate;
import io.terminus.gaia.app.b2b.contract.tmodel.AgreementDetailSupplyInfoTO;
import io.terminus.gaia.app.b2b.contract.tmodel.AgreementDetailSyncInfoTO;
import io.terminus.gaia.app.b2b.contract.util.B2bContractUtil;
import io.terminus.gaia.app.b2b.item.dict.external.ExternalSystemDict;
import io.terminus.gaia.app.b2b.item.dict.external.IpmMappingBizTypeDict;
import io.terminus.gaia.app.b2b.item.model.ExternalMappingBO;
import io.terminus.gaia.common.constants.GaiaCommonConstants;
import io.terminus.gaia.common.utils.EnhanceDS;
import io.terminus.gaia.item.model.spu.SpuBO;
import io.terminus.gaia.md.dict.*;
import io.terminus.gaia.md.model.CategoryBO;
import io.terminus.gaia.md.model.CompanyBO;
import io.terminus.gaia.md.model.DistrictBO;
import io.terminus.gaia.md.model.TaxRateBO;
import io.terminus.gaia.md.model.query.QTaxRateBO;
import io.terminus.gaia.organization.model.DepartmentBO;
import io.terminus.gaia.organization.model.DepartmentRuleBO;
import io.terminus.trantorframework.api.type.Currency;
import io.terminus.trantorframework.json.Json;
import io.terminus.trantorframework.querymodel.type.support.QBigDecimal;
import io.terminus.trantorframework.querymodel.type.support.QString;
import io.terminus.trantorframework.querymodel.type.support.QType;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class YzwAgreementConverter extends AgreementConverterTemplate {

    /**
     * 模型转化
     *
     * @param yzwContract
     * @return
     */
    public AgreementBO convertAgreement(YzwContract yzwContract, boolean skipExist) {
        AgreementBO saveAgreement = new AgreementBO();

        AgreementBO existAgreement = EnhanceDS.safeFindOne(AgreementBO.class,
                "*", "externalCode IN (?) AND sourceDict = ?", yzwContract.getExternalId(), AgreementSourceDict.SYNC);
        if (existAgreement != null) {
            saveAgreement.setId(existAgreement.getId());
            saveAgreement.setExistAgreement(existAgreement);
        }
        // 判断是否跳过同步
        if (skipSync(yzwContract, existAgreement, skipExist)) {
            return null;
        }
        saveAgreement.setName(yzwContract.getContractName());
        saveAgreement.setExternalCode(yzwContract.getExternalId());
        saveAgreement.setExternalNo(yzwContract.getContractCode());
        // 采购商名称转换处理
        yzwContract.setPurchaserName(convertPurchaseName(yzwContract.getPurchaserName()));
        List<CompanyBO> jfCompanies = DS.findAll(CompanyBO.class, "*", "companyName = ? AND merchantType = ?", yzwContract.getPurchaserName(), MerchantTypeDict.PURCHASER);
        List<String> errors = new ArrayList<>();
        errors.add("付款方案");
        if (CollectionUtils.isNotEmpty(jfCompanies)) {
            if (jfCompanies.size() > 1) {
                log.warn("集采协议同步，根据【{}】查到多个采购商", yzwContract.getPurchaserName());
            }
            saveAgreement.setJfCompanyBO(jfCompanies.get(0).getEntity());
        } else {
            log.warn("集采协议同步，根据【{}】找不到甲方", yzwContract.getPurchaserName());
            errors.add(StrUtil.format("协议甲方【{}】", yzwContract.getPurchaserName()));
        }
        CompanyBO yfCompanyBO = EnhanceDS.safeFindOne(CompanyBO.class, "*", "supJcSysno = ? AND merchantType = ?", yzwContract.getSupplierSysNo(), MerchantTypeDict.SUPPLIER);
        if (yfCompanyBO != null) {
            saveAgreement.setYfCompanyBO(yfCompanyBO.getEntity());
        } else {
            log.warn("集采协议同步，根据【{}-{}】找不到供应商", yzwContract.getSupplierSysNo(), yzwContract.getSupplierName());
            errors.add(StrUtil.format("协议乙方【{}-{}】", yzwContract.getSupplierSysNo(), yzwContract.getSupplierName()));
        }
        DepartmentBO department = EnhanceDS.safeFindOne(DepartmentBO.class, "*", "jcSysno = ?", yzwContract.getUsingOrgSysNo());
        if (department != null) {
            fillUsingDepartment(saveAgreement, existAgreement, department);
        } else {
            log.warn("集采协议同步，根据【{}】找不到使用单位", yzwContract.getUsingOrgSysNo());
            errors.add(StrUtil.format("使用单位【{}】", yzwContract.getUsingOrgSysNo()));
        }

        saveAgreement.setType(B2bContractUtil.calcAgreementType(saveAgreement));
        if (Objects.isNull(saveAgreement.getType())) {
            saveAgreement.setType(AgreementTypeDict.MATCH_MAKING);
        }
        // 从云筑网获取的协议，【适用项目类型】默认为施工；从爱德系统获取的协议，【适用项目类型】默认为地产
        saveAgreement.setProjectTypes(ListUtil.toList(AgreementProjectTypeDict.CONSTRUCTION));
        ExternalMappingBO categoryMapping = EnhanceDS.safeFindOne(ExternalMappingBO.class, "*", "systemDict = ? AND bizType = ? AND externalCode = ?",
                ExternalSystemDict.YZ_CENTER_PURCHASE, IpmMappingBizTypeDict.CATEGORY, yzwContract.getSystemCategorySysNo());
        if (categoryMapping == null) {
            log.warn("集采协议同步，根据类目【{}-{}】找不到映射", yzwContract.getSystemCategorySysNo(), yzwContract.getSystemCategoryName());
            errors.add(StrUtil.format("协议分类【{}-{}】", yzwContract.getSystemCategorySysNo(), yzwContract.getSystemCategoryName()));
        } else {
            CategoryBO categoryBO = new CategoryBO();
            categoryBO.setId(categoryMapping.getBizId());
            saveAgreement.setCategory(categoryBO);
        }


        saveAgreement.setSignAt(yzwContract.getContractDate());
        saveAgreement.setEffectiveAt(yzwContract.getBeginDate() == null ? null : DateUtil.beginOfDay(yzwContract.getBeginDate()));
        saveAgreement.setExpireAt(yzwContract.getEndDate() == null ? null : DateUtil.endOfDay(yzwContract.getEndDate()));
        saveAgreement.setRemark("");
        saveAgreement.setAttachment(convertAttachment(yzwContract.getContractAttachList()));

        saveAgreement.setSourceDict(AgreementSourceDict.SYNC);
        saveAgreement.setBidTaskId(yzwContract.getTenderSysNo());
        saveAgreement.setBidTaskCode(yzwContract.getTenderCode());
        saveAgreement.setBidTaskName(yzwContract.getTenderName());
        saveAgreement.setJcBidInfo(yzwContract.buildBidInfo());
        // 没有补充协议
        if (Long.valueOf(2).equals(yzwContract.getChangeType())) {
            saveAgreement.setIsSupply(true);
            AgreementBO mainAgreement = EnhanceDS.safeFindOne(AgreementBO.class,
                    "*", "externalCode IN (?) AND sourceDict = ?", yzwContract.getOldContractSysNo(), AgreementSourceDict.SYNC);
            saveAgreement.setMainAgreement(mainAgreement);
        } else {
            saveAgreement.setIsSupply(false);
        }
        saveAgreement.setIsSubCompany(false);
        saveAgreement.setIsCenterPay(false);
        saveAgreement.setCanRelateDetail(true);
        saveAgreement.setCreatedAt(yzwContract.getCreateDate());

        handleSyncStatus(errors, yzwContract, saveAgreement, existAgreement);

        // 填充联系人
        fillContacts(saveAgreement, yzwContract);
        // 覆盖范围
        fillCoverArea(saveAgreement, yzwContract);
        // 协议清单
        fillAgreementDetail(saveAgreement, yzwContract);
        // 责任单位
        fillOptDepartments(saveAgreement, yzwContract);

        return saveAgreement;
    }

    /**
     * 转化协议责任单位
     * @param agreementBO
     * @param yzwContract
     */
    private void fillOptDepartments(AgreementBO agreementBO, YzwContract yzwContract) {
        // 协议的合同经办人
        String contractOperator = String.valueOf(yzwContract.getPurchaserUserSysNo());
        if (StrUtil.isNotBlank(contractOperator)) {
            // 匹配组织上维护的信息
            List<DepartmentRuleBO> departmentRuleBOList = DS.findAll(DepartmentRuleBO.class, "*, departmentBO.*", "contractOperator like ?", StrUtil.concat(true,"%", contractOperator, "%"));
            List<DepartmentBO> departmentBOList = new ArrayList<>();
            if (CollUtil.isNotEmpty(departmentRuleBOList)) {
                departmentRuleBOList.forEach(departmentRuleBO -> {
                    if (departmentRuleBO.getContractOperator().contains(",")) {
                        String[] contractOperators = departmentRuleBO.getContractOperator().split(",");
                        List<String> stringList = Arrays.asList(contractOperators);
                        if (stringList.contains(contractOperator)) {
                            departmentBOList.add(departmentRuleBO.getDepartmentBO());
                        }
                    } else {
                        departmentBOList.add(departmentRuleBO.getDepartmentBO());
                    }
                });
            }
            if (CollUtil.isNotEmpty(departmentBOList)) {
                agreementBO.setOptDepartments(departmentBOList);
                List<String> idList = departmentBOList.stream().map(departmentBO -> String.valueOf(departmentBO.getId())).collect(Collectors.toList());
                agreementBO.setOptDepartmentIdList(idList);
            }
        }
    }

    /**
     * 转化协议清单
     *
     * @param agreementBO
     * @return
     */
    private void fillAgreementDetail(AgreementBO agreementBO, YzwContract yzwContract) {
        List<YzwContractMate> yzwContractMates = yzwContract.getContracMateList();
        if (CollectionUtils.isEmpty(yzwContractMates)) {
            return;
        }
        // 补充协议查询主协议清单/非补充协议查本协议清单
        List<AgreementDetailBO> allDetails = null;
        if (Objects.nonNull(agreementBO.getMainAgreement())) {
            allDetails = DS.findAll(AgreementDetailBO.class, "*", "agreementBO = ?", agreementBO.getMainAgreement().getId());

        } else {
            allDetails = agreementBO.getExistAgreement() == null ? new ArrayList<>() :
                    DS.findAll(AgreementDetailBO.class, "*", "agreementBO = ?", agreementBO.getExistAgreement().getId());
        }

        Map<String, AgreementDetailBO> outDetailMap = allDetails.stream().filter(e -> StringUtils.isNotBlank(e.getOuterCode()))
                .collect(Collectors.toMap(AgreementDetailBO::getOuterCode, Function.identity(), (a, b) -> {
                    log.warn("============= 协议清单outerCode【{}】重复 ===============", a.getOuterCode());
                    return a;
                }));

        // key: mateSysNo
        Map<String, List<YzwBiddingListItem>> bidItemMap = yzwContract.getBidItemMap();
        // key: 税率
        Map<String, TaxRateBO> taxRateMap = new HashMap<>();
        List<AgreementDetailBO> newDetails = new ArrayList<>();
        yzwContractMates.forEach(contractMate -> {
            AgreementDetailBO detailBO = outDetailMap.get(contractMate.getExternalId());
            if (detailBO != null) {
                return;
            }
            // 仅处理新增的
            AgreementDetailBO newDetail = new AgreementDetailBO();
            newDetail.setSaleEntityBO(agreementBO.getYfCompanyBO());
            // 补充协议特殊处理
            if (agreementBO.getIsSupply()) {
                newDetail.setAgreementBO(agreementBO.getMainAgreement());
                newDetail.setSupplementAgreementBO(agreementBO);
            } else {
                newDetail.setAgreementBO(agreementBO);
            }
            newDetail.setBidTaskCode(agreementBO.getBidTaskCode());
            newDetail.setBidTaskName(agreementBO.getBidTaskName());
            // 供应信息
            AgreementDetailSupplyInfoTO supplyInfo = new AgreementDetailSupplyInfoTO();
            // 同步信息
            AgreementDetailSyncInfoTO syncInfo = new AgreementDetailSyncInfoTO();
            supplyInfo.setSaleEntityBO(agreementBO.getYfCompanyBO());
            // DataSysNo
            List<YzwBiddingListItem> biddingItemList = bidItemMap.getOrDefault(contractMate.getUk(), new ArrayList<>());
            if (CollectionUtils.isNotEmpty(biddingItemList)) {
                // 不含税价
                Currency price = biddingItemList.stream().filter(e -> "ExcludeTaxUnitPrice".equals(e.getItemName())).findFirst()
                        .map(YzwBiddingListItem::getItemValue)
                        .filter(NumberUtil::isNumber)
                        .map(e -> new Currency(new BigDecimal(e))).orElse(null);
                supplyInfo.setPrice(price);
                // 含税价
                Currency quotedPrice = biddingItemList.stream().filter(e -> "QuotedPrice".equals(e.getItemName()))
                        .map(YzwBiddingListItem::getItemValue)
                        .filter(NumberUtil::isNumber)
                        .findFirst().map(e -> new Currency(new BigDecimal(e))).orElse(null);
                supplyInfo.setTaxPrice(quotedPrice);
                // 报价依据
                String quotedBasis = biddingItemList.stream().filter(e -> "QuotedBasis".equals(e.getItemName())).findFirst()
                        .map(YzwBiddingListItem::getItemValue).orElse(null);
                supplyInfo.setQuotationBasis(quotedBasis);
                // 备注
                String remark = biddingItemList.stream().filter(e -> "Description".equals(e.getItemName())).findFirst()
                        .map(YzwBiddingListItem::getItemValue).orElse(null);
                syncInfo.setRemark(remark);
                // 税率 集采返回的是小数，0.13
                String taxRateStr = biddingItemList.stream().filter(e -> "TaxRate".equals(e.getItemName()))
                        .map(YzwBiddingListItem::getItemValue)
                        .filter(NumberUtil::isNumber)
                        .findFirst().orElse(null);
                if (StringUtils.isNotBlank(taxRateStr) && NumberUtil.isNumber(taxRateStr)) {
                    TaxRateBO taxRateBO = taxRateMap.computeIfAbsent(taxRateStr, (e) -> {
                        // 乘100
                        BigDecimal taxRate = new BigDecimal(taxRateStr).multiply(GaiaCommonConstants.HUNDRED);
                        QTaxRateBO qTaxRateBO = new QTaxRateBO();
                        qTaxRateBO.setBusinessType(new QType(ListUtil.toList(TaxRateBusinessTypeDict.PURCHASE)));
                        qTaxRateBO.setTaxRateType(new QString(TaxRateTypeDict.GENERAL_TAXPAYER));
                        qTaxRateBO.setTaxRate(new QBigDecimal(taxRate));
                        List<TaxRateBO> existTaxRates = DS.findAll(qTaxRateBO);
                        if (CollectionUtils.isEmpty(existTaxRates)) {
                            return initTaxRate(taxRate);
                        } else {
                            return existTaxRates.get(0);
                        }
                    });
                    syncInfo.setTaxRate(taxRateBO.getTaxRateShow());
                    newDetail.setTaxRate(taxRateBO);
                }
            }
            if (Objects.nonNull(contractMate.getMateSysNo())) {
                syncInfo.setMateSysNo(contractMate.getMateSysNo().toString());
            }
            syncInfo.setMateName(contractMate.getMateName());
            syncInfo.setMateSize(contractMate.getMateSpec());
            syncInfo.setMateMaterial("");
            syncInfo.setUnit(contractMate.getMateUnit());
            syncInfo.setSupplier(yzwContract.getSupplierName());
            syncInfo.setCategory(yzwContract.getSystemCategoryName());
            // ------ 同步字段 START -----------
            newDetail.setOuterAgreementName(syncInfo.getMateName());
            newDetail.setThingSize(syncInfo.getMateSize());
            newDetail.setThingMaterial(syncInfo.getMateMaterial());
            // ------- 同步字段 END -------------
            newDetail.setSupplyInfo(supplyInfo);
            newDetail.setSyncInfo(syncInfo);
            newDetail.setSource(AgreementDetailSourceDict.CLOUD_BUILD);
            newDetail.setOuterCode(contractMate.getExternalId());
            // 三方物料id也就是IPM的物资id
            newDetail.setThingMaterialId(contractMate.getMateOtherCode());
            // 价格处理
            compensateDetailPrice(newDetail);

            newDetails.add(newDetail);
        });

        // 处理映射
        handleDetailMapping(newDetails, allDetails);

        newDetails.forEach(detail -> {
            if (agreementBO.isSyncSuccess()) {
                // 包含所有的必填信息
                if (detail.containAllRequired()) {
                    // 成功映射则设为同步成功
                    detail.setInventoryStatus(InventoryStatusDict.IN_STOCK);
                    detail.setStatusDict(AgreementDetailStatusDict.START_USING);
                } else {
                    detail.setInventoryStatus(InventoryStatusDict.OUT_STOCK);
                    detail.setStatusDict(AgreementDetailStatusDict.UNUSUAL);
                }
            } else {
                // 先置为失效，编辑拓展信息时，失效 -> 启用
                detail.setInventoryStatus(InventoryStatusDict.OUT_STOCK);
                detail.setStatusDict(AgreementDetailStatusDict.UNUSUAL);
                detail.setLoseReason(AgreementDetailLoseReasonDict.AGREEMENT_SYNC_FAIL);
            }
        });

        agreementBO.setDetails(newDetails);
    }

    /**
     * 清单价格兼容处理
     *
     * @param detail
     */
    private void compensateDetailPrice(AgreementDetailBO detail) {
        TaxRateBO taxRate = detail.getTaxRate();
        if (taxRate == null || taxRate.getTaxRate() == null) {
            return;
        }
        AgreementDetailSupplyInfoTO supplyInfo = detail.getSupplyInfo();
        // 含税价
        Currency taxPrice = supplyInfo.getTaxPrice();
        // 不含税价
        Currency price = supplyInfo.getPrice();
        if (taxPrice == null && price == null) {
            return;
        }
        // 小数
        BigDecimal rate = taxRate.getTaxRate().divide(GaiaCommonConstants.HUNDRED, 2, RoundingMode.HALF_UP);
        // 含税价为空
        if (taxPrice == null) {
            // 含税 -> 不含税
            supplyInfo.setTaxPrice(new Currency(price.getValue().divide(BigDecimal.ONE.add(rate), 4, RoundingMode.HALF_UP)));
        }
        // 不含税价为空
        if (price == null) {
            // 不含税 -> 含税
            supplyInfo.setPrice(taxPrice.subtract(taxPrice.multiply(rate)));
        }
    }

    /**
     * 处理映射
     *
     * @param newDetails
     */
    private void handleDetailMapping(List<AgreementDetailBO> newDetails, List<AgreementDetailBO> existDetails) {
        if (CollectionUtils.isEmpty(newDetails)) {
            return;
        }

        List<String> mateSysNos = newDetails.stream().map(e -> e.getSyncInfo().getMateSysNo())
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mateSysNos)) {
            return;
        }

        Set<Long> alreadyMapSpuIds = existDetails.stream().filter(e -> e.getSpuBO() != null)
                .map(e -> e.getSpuBO().getId()).collect(Collectors.toSet());

        // 查询映射
        Map<String, AgreementDetailMappingBO> mappingMap = DS.findAll(AgreementDetailMappingBO.class,
                        "*,spuBO.*", "mateSysNo IN (?)", mateSysNos)
                .stream().collect(Collectors.toMap(AgreementDetailMappingBO::getMateSysNo, Function.identity()));
        newDetails.forEach(detail -> {
            AgreementDetailMappingBO mappingBO = mappingMap.get(detail.getSyncInfo().getMateSysNo());
            if (mappingBO == null) {
                return;
            }
            SpuBO spuBO = mappingBO.getSpuBO();
            if (spuBO == null) {
                log.warn("标品已被删除，移除清单映射:{}", Json.toJson(mappingBO));
                DS.delete(mappingBO);
                return;
            }
            if (alreadyMapSpuIds.contains(spuBO.getId())) {
                log.warn("协议清单SPU【{}】重复映射，跳过自动映射", spuBO.getId());
                return;
            }
            // 自动映射
            log.info("集采物资自动映射，物资【{}-{}】==> spu【{}-{}】", mappingBO.getMateSysNo(), mappingBO.getMateName(), spuBO.getId(), spuBO.getName());
            detail.setSpuBO(spuBO);
            detail.setSpuCode(spuBO.getSpuCode());
            detail.setSpuName(spuBO.getName());
            detail.setThingSize(spuBO.getThingSize());
            detail.setThingMaterial(spuBO.getThingMaterial());
            detail.setAttribute(spuBO.getRealAttributeStr());
            detail.setUnit(spuBO.getUnit());
            detail.setCategoryBO(spuBO.getCategory());

            alreadyMapSpuIds.add(spuBO.getId());
        });
    }

    /**
     * 初始化税率
     *
     * @param taxRate
     * @return
     */
    private TaxRateBO initTaxRate(BigDecimal taxRate) {
        log.info("===== 协议同步自动创建税率: {}", taxRate);
        TaxRateBO taxRateBO = new TaxRateBO();
        taxRateBO.setId(DS.nextId(TaxRateBO.class));
        taxRateBO.setTaxRateName(NumberUtil.toStr(taxRate) + "%");
        taxRateBO.setTaxRateShortCode(taxRateBO.getId().toString());
        taxRateBO.setTaxRateType(TaxRateTypeDict.GENERAL_TAXPAYER);
        taxRateBO.setClassification(TaxRateClassificationDict.ENABLED);
        taxRateBO.setTaxRate(taxRate);
        taxRateBO.setTaxRateDscp("");
        taxRateBO.setTaxRateStatus(TaxRateStatusDict.ENABLED);
        taxRateBO.setTaxRateShow(NumberUtil.toStr(taxRate) + "%");
        taxRateBO.setBusinessType(ListUtil.toList(TaxRateBusinessTypeDict.PURCHASE));

        DS.create(taxRateBO);
        return taxRateBO;
    }

    /**
     * 集采联系人
     *
     * @param yzwContract
     * @return
     */
    private void fillContacts(AgreementBO saveAgreement, YzwContract yzwContract) {
        if (skipSyncContact(saveAgreement.getExistAgreement())) {
            return;
        }
        // 仅第一次同步新增
        AgreementContactBO contactBO = new AgreementContactBO();
        contactBO.setPerson(yzwContract.getSupplierContactName());
        contactBO.setPhone(yzwContract.getSupplierContactPhone());
        saveAgreement.setContacts(Lists.newArrayList(contactBO));
    }

    /**
     * 覆盖范围
     *
     * @param saveAgreement
     */
    private void fillCoverArea(AgreementBO saveAgreement, YzwContract yzwContract) {
        if (skipSyncCoverArea(saveAgreement.getExistAgreement())) {
            return;
        }
        List<YzwContractArea> yzwAreas = CollUtil.defaultIfEmpty(yzwContract.getContractAreaList(), new ArrayList<>());
        if (yzwAreas.stream().anyMatch(area -> StringUtils.equals(area.getAreaName(), "全国"))) {
            saveAgreement.setCoverAreaType(AgreementCoverAreaTypeDict.ALL);
            return;
        }
        saveAgreement.setCoverAreaType(AgreementCoverAreaTypeDict.APPOINT);
        // 覆盖范围
        fillAppointCoverArea(saveAgreement, yzwContract.getContractAreaList(), (pair) -> {
            List<DistrictBO> districts = pair.getRight();
            return districts.get(0);
        });
    }

    /**
     * 从外部拉取协议后，若外部协议的甲方文本为【中国建筑股份有限公司】
     * 或者【中国建筑一局（集团）有限公司】时，则默认映射为根组织【中建一局】；
     * @param purchaserName
     * @return
     */
    private String convertPurchaseName(String purchaserName) {
        if ("中国建筑股份有限公司".equals(purchaserName)
                || "中国建筑一局（集团）有限公司".equals(purchaserName)) {
            return "中建一局";
        } else{
            return purchaserName;
        }
    }
}
