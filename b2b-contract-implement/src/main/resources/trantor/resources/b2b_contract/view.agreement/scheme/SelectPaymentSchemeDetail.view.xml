<?xml version="1.0" encoding="UTF-8" ?>
<View title="价格方案" forModel="b2b_contract_PaymentRelatePriceSchemeBO" type="List" menuView="true"  version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/xsi/0.17.x/base.xsd">
    <Table key="paymentRelatePriceScheme" model="b2b_contract_PaymentRelatePriceSchemeBO" dataCondition="agreementDetail = ?" dataParams="[pageContext.record.id]"  fuzzySearchable="#{false}" >
        <Fields>
            <Field name="agreementBO" show="#{false}"/>
            <Field name="paymentScheme.id" show="#{false}"/>
            <Field name="paymentScheme.code" label="编码" readonly="#{true}" />
            <Field name="paymentScheme.title" label="付款节点" readonly="#{true}" />
            <Field name="paymentScheme.description" label="方案描述" readonly="#{true}" />
            <Field name="paymentScheme.schemeNodes" show="#{false}" />
            <Field name="priceScheme.id" show="#{false}"/>
            <Field name="priceScheme.name" label="价格方案">
                <RenderType>
                    <Action targetView="item_PriceSchemeBO_PriceSchemeDetail"  context="#{{id: this.record.priceScheme.id}}" openViewType="Blank" />
                </RenderType>
            </Field>
        </Fields>
        <RecordActions>
            <Action label="详情"  targetView="b2b_contract_PaymentSchemeBO_PaymentSchemeDetail" record="#{this.record.paymentScheme}"  openViewType="Dialog"/>
        </RecordActions>
    </Table>
</View>
