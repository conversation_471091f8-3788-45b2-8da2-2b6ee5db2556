<View xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/xsi/0.17.x/base.xsd"
      title="使用模版维护" forModel="b2b_contract_AgreementTemplateBO" type="Form" version="2">

    <Steps current="2">
        <Step title="选择协议招标任务" key="1" />
        <Step title="使用模版维护" key="2" />
        <Step title="确认维护" key="3" />
    </Steps>

    <Table singleSelection="#{true}" key="table"
    model="b2b_contract_AgreementTemplateBO" dataFunction="b2b_contract_AgreementTemplatePagingFunc">
        <Search>
            <Fields>
                <Field name="code" lable="模板编码"/>
                <Field name="name" lable="模板名称"/>
                <Field name="jfCompanyBO">
                    <RenderType>
                        <ModelSelect model="md_EntityBO" dataCondition="entityStatus='ENABLED' and merchantType in ('PURCHASER','SUPPLY_CHAIN') "/>
                    </RenderType>
                </Field>
                <Field name="departments"/>
                <Field name="projectTypes"/>
                <Field name="category">
                    <RenderType>
                        <CascadeModelSelect isLeafField="isLeaf" depthLimit="2" parentField="parentCategory"
                                            columnTitles="#{['一级分类', '二级分类', '三级分类', '四级分类']}"
                                            dataSource="md_CategoryBO_CategoryTreeProvider"
                                            model="md_CategoryBO" valueField="id" linkSelectMode="true"
                                            labelField="categoryName" modalWidth="#{800}"
                                            modalTitle="选择分类" searchInputWidth="#{400}"/>
                    </RenderType>
                </Field>
            </Fields>
        </Search>
        <Fields>
            <Field name="code" lable="模板编码"/>
            <Field name="name" lable="模板名称"/>
            <Field name="jfCompanyBO">
                <RenderType>
                    <MainField onlyText="#{true}"/>
                </RenderType>
            </Field>
            <Field name="departments">
                <RenderType>
                    <MainField onlyText="#{true}"/>
                </RenderType>
            </Field>
            <Field name="projectTypes"/>
            <Field name="category.path"/>
        </Fields>
        <RecordActions>
            <Action label="详情" targetView="b2b_contract_AgreementTemplateBO_AgreementTemplateDetail"/>
        </RecordActions>
        <Actions>
            <Action layout="Footer" type="Cancel" action="Close"/>
            <Action layout="Footer" label="上一步"  action="GoBack"/>
            <Action type="Submit" multi="#{true}" action="#{nextSteps}" label="下一步" layout="Footer" />
        </Actions>
    </Table>
</View>