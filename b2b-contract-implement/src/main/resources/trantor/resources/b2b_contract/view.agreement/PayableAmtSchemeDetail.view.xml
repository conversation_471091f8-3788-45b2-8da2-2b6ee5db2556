<?xml version="1.0" encoding="UTF-8" ?>
<View title="账款方案" forModel="b2b_contract_PayableSchemeTO" type="Detail" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/xsi/0.17.x/base.xsd">

    <Detail key="PayableAmtSchemes" model="b2b_contract_PayableSchemeTO"  dataSource="#{()=>pageRecord}" >
        <Fields>
            <Field name="id" show="#{false}"/>
            <Field name="code" show="#{!!this.record.code}"/>
            <Field name="title" label="节点"/>
            <Field name="content" label="账期"/>
            <Field name="mark">
                <RenderType>
                    <Textarea maxLength="#{1000}"/>
                </RenderType>
            </Field>
        </Fields>
        <Actions>
            <Action label="返回" action="Close" layout="Footer"/>
        </Actions>
    </Detail>

    <TableForm title="账单确认后账款节点" model="b2b_contract_PayableSchemeNodeTO" key="PayableAmtSchemesNodes"
               minDataCount="#{1}" lookupFrom="PayableAmtSchemes.schemeNodes" showAdd="#{false}" showDelete="#{false}"
               showSettingBtn="#{false}" genSummaryData="#{false}" rowSelectDisabled="#{false}">
        <Fields>
            <Field name="payNodeType" show="#{ env.type === 'self' }" readonly="#{true}">
                <Validations>
                    <Validation required="#{ env.type === 'self' }" message="#{`应${env.typeMsg}款项类型不能为空`}" />
                </Validations>
            </Field>
            <Field name="payableRatio" readonly="#{true}" label="比例">
                <RenderType>
                    <Number min="0" unit="%"/>
                </RenderType>
                <Validations>
                    <Validation required="#{true}" message="比例不能为空"/>
                </Validations>
            </Field>

            <Field name="payableInfo" readonly="#{true}">
            </Field>
            <MultiField label="账期" columnSize="large">
                <Field name="payablePeriod" columnSize="#{20}" readonly="#{true}">
                    <RenderType>
                        <Number min="0" />
                    </RenderType>
                </Field>
                <Field name="periodType" columnSize="#{20}" readonly="#{true}">
                </Field>
            </MultiField>

        </Fields>

    </TableForm>
</View>
