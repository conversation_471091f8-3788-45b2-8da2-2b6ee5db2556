package io.terminus.gaia.app.b2b.contract.model;

import io.terminus.gaia.md.model.DistrictBO;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.DeleteStrategy;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.Model;
import io.terminus.trantorframework.api.annotation.typemeta.LinkMeta;
import lombok.Data;

import java.util.Optional;

/**
 * 覆盖范围
 *
 * <AUTHOR>
 */
@Data
@Model(deleteStrategy = DeleteStrategy.Physical)
public class AgreementCoverAreaLineBO extends BaseModel<Long> {
    @Field(name = "关联协议", nullable = false)
    @LinkMeta
    private AgreementBO agreementBO;

    @Field(name = "关联覆盖大区", nullable = false)
    @LinkMeta
    private AgreementCoverAreaBO coverAreaBO;

    /**
     * level = 1
     */
    @Field(name = "大区", nullable = false)
    @LinkMeta
    private DistrictBO region;

    /**
     * level > 1
     */
    @Field(name = "关联省市区")
    @LinkMeta
    private DistrictBO district;

    @Field(name = "省市区")
    private String districtName;

    public static AgreementCoverAreaLineBO of(AgreementBO agreementBO, AgreementCoverAreaBO coverAreaBO, DistrictBO region, DistrictBO district) {
        AgreementCoverAreaLineBO coverAreaLineBO = new AgreementCoverAreaLineBO();
        coverAreaLineBO.setAgreementBO(agreementBO);
        coverAreaLineBO.setCoverAreaBO(coverAreaBO);
        coverAreaLineBO.setRegion(region);
        coverAreaLineBO.setDistrict(district);
        coverAreaLineBO.setDistrictName(Optional.ofNullable(district).map(DistrictBO::getDistrictName).orElse(null));

        return coverAreaLineBO;
    }
}
