<?xml version="1.0" encoding="UTF-8"?>
<View title="签署" forModel="contract_ElectronicSignContractCreateTO" type="Form" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://trantor-docs-dev.app.terminus.io/static/v0.16.x/schema/base.xsd">
    <Form key="mainModel" columnNum="1" model="contract_ElectronicSignContractCreateTO" dataFunction="contract_GetElectroincSignContractCreateTOFunc" dataParams="{contract:{'id':env.id}}">

        <Fields>
            <Field name="contract.id" initValue="#{env.id}" show="#{false}"/>
            <Field name="archiveCode" label="归档编号" readonly="#{true}">
                <Validations>
                    <Validation required="#{true}" message="请输入归档编号"/>
                </Validations>
            </Field>
            <Field name="isCanBeOnline" show="#{false}"/>
            <Field name="signWay" label="签署方式" >
                <Validations>
                    <Validation required="#{true}" message="请选择签署方式"/>
                </Validations>
                <RenderType>
                    <Radio direction='horizontal' disabledValues="#{values => values.filter(key => !getContainerByKey('mainModel').data.isCanBeOnline &amp;&amp; key === 'ONLINE')}"/>
                </RenderType>
            </Field>
            <Field label="已签署文件" name="contract.signedAttachment" show="#{this.data.signWay ==='OFFLINE'}">
                <Validations>
                    <Validation required="#{true}" message="请上传已签署文件"/>
                </Validations>
            </Field>
        </Fields>
        <Actions>
            <Action label="取消" type="Cancel" action="GoBack" layout="Footer"/>
            <Action label="确定" type="Submit" confirm="确认提交？" after="GoBack" layout="Footer" validator="#{validateSign}" action="#{submitElectronicSignTask}"/>
        </Actions>
    </Form>
    <TableForm key="signFileBOList" title="签署文件" show="#{getContainerByKey('mainModel').data.isCanBeOnline &amp;&amp; getContainerByKey('mainModel').data.signWay==='ONLINE'}" model="contract_SignFileBO" fuzzySearchable="#{false}" pagination="#{false}" showDelete="#{false}" showAdd="#{false}"
               lookupFrom="mainModel.signFileBOList">
        <Fields>
            <Field name="fileName" label="合同文件" readonly="#{true}"/>
            <Field name="sortNum" label="签署顺序">
                <Validations>
                    <Validation required="#{true}" message="请输入签署顺序"/>
                </Validations>
            </Field>
            <Field name="signAttachment" show="#{false}" submit="#{true}"/>
        </Fields>
    </TableForm>
</View>