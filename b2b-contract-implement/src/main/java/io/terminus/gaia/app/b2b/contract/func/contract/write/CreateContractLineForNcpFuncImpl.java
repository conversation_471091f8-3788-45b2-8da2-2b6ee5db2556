package io.terminus.gaia.app.b2b.contract.func.contract.write;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import io.terminus.gaia.app.b2b.contract.dict.YzContractDetailStatusDict;
import io.terminus.gaia.app.b2b.contract.dict.YzContractStatusDict;
import io.terminus.gaia.app.b2b.contract.model.ContractLineBO;
import io.terminus.gaia.app.b2b.contract.model.PayableSchemeTemplateBO;
import io.terminus.gaia.app.b2b.contract.model.YzContractBO;
import io.terminus.gaia.app.b2b.contract.tmodel.ncp.NcpContractLineRequestDTO;
import io.terminus.gaia.app.b2b.contract.tmodel.ncp.YzContractDetailForNcp;
import io.terminus.gaia.app.b2b.item.model.SkuBOExt;
import io.terminus.gaia.common.constants.GaiaCommonConstants;
import io.terminus.gaia.item.dict.item.ItemTypeNewDict;
import io.terminus.gaia.item.model.item.ItemBO;
import io.terminus.gaia.item.model.price.PriceSchemeBO;
import io.terminus.gaia.item.model.sku.SkuBO;
import io.terminus.gaia.md.model.BrandBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.api.type.Currency;
import io.terminus.trantorframework.json.Json;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 非集采--补充合同行
 * <AUTHOR>
 * @date  2024-07-22
 */
@FunctionImpl
@Slf4j
public class CreateContractLineForNcpFuncImpl implements CreateContractLineForNcpFunc{

    @Override
    public void execute(NcpContractLineRequestDTO requestDTO) {
        log.info("非集采补充合同行--requestDTO：{}", JSON.toJSONString(requestDTO));
        YzContractBO yzContractBO = DS.findOne(YzContractBO.class, "*", YzContractBO.contractCode_field + " = ?", requestDTO.getContractCode());
        if (CollectionUtils.isNotEmpty(requestDTO.getDetailList())) {
            List<ContractLineBO> newLines = new ArrayList<>();

            Set<String> skuCodes = requestDTO.getDetailList().stream().map(YzContractDetailForNcp::getSkuNo).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<String, SkuBOExt> skuMap = CollectionUtils.isEmpty(skuCodes) ? new HashMap<>() :
                    DS.findAll(SkuBOExt.class, "*", "skuCode IN (?)", skuCodes)
                            .stream().collect(Collectors.toMap(SkuBOExt::getSkuCode, Function.identity()));

            requestDTO.getDetailList().forEach(syncLine -> {
                ContractLineBO newLine = new ContractLineBO();

                newLine.setContract(YzContractBO.of(yzContractBO.getId()));
                newLine.setCode(syncLine.getId().toString());
                newLine.setExternalCode(syncLine.getId().toString());
                newLine.setUnitName(syncLine.getUnitName());
                newLine.setMaterialCategoryFullPathName(syncLine.getMaterialFullPathName());
                newLine.setThirdContractIntentionDetailNo(syncLine.getThirdContractIntentionDetailNo());
                newLine.setStatus(convertDetailStatus(syncLine.getDetailStatus()));
                newLine.setIntentionDetailNo(syncLine.getContractIntentionDetailNo());
                //newLine.setIntentionNo(syncContract.getContractIntentionNo());
                newLine.setIntentionNo(syncLine.getContractIntentionNo());
                SkuBOExt skuBO = skuMap.get(syncLine.getSkuNo());
                if (skuBO == null) {
                    log.error("云筑合同同步，根据sku编码【{}】找不到SKU", syncLine.getSkuNo());
                    return;
                }
                newLine.setSku(skuBO);
                newLine.setSpu(skuBO.getSpu());
                newLine.setSkuJson(buildSkuSnapshot(syncLine));
                newLine.setPriceScheme(buildPriceSchemeSnapshot(syncLine));
                // 应收账期方案
                newLine.setPayableSchemeTemplates(buildPayableSchemeTemplateSnapshot(syncLine));
                newLine.setSalePriceCalLineId(syncLine.getSalePriceCalLineId());
                newLine.setPriceType(syncLine.getPriceType());
                newLine.setBidBrands(buildBidBrand(syncLine));
                newLine.setPrcWithTax(syncLine.getPurchasePrice() == null ? null : new io.terminus.trantorframework.api.type.Currency(syncLine.getPurchasePrice()));
                newLine.setPrcWithoutTax(syncLine.getPurchasePriceExcludeTax() == null ? null : new Currency(syncLine.getPurchasePriceExcludeTax()));
                newLine.setTaxRate(Optional.of(syncLine.getTaxRate()).map(e -> e.multiply(GaiaCommonConstants.HUNDRED)).orElse(null));
                newLine.setSignedQty(syncLine.getPurchaseNum());
                newLine.setDiscountFactor(syncLine.getDiscountFactor());
                if (ObjectUtil.isNotNull(syncLine.getAdjustItem())
                        && syncLine.getAdjustItem() == 1) {
                    newLine.setAdjustItem(true);
                } else {
                    newLine.setAdjustItem(false);
                }
                if (ItemTypeNewDict.EQUIPMENT_LEASE.equals(skuBO.getItemType())
                        || ItemTypeNewDict.MATERIAL_LEASE.equals(skuBO.getItemType())) {
                    newLine.setLeaseForm(syncLine.getLeaseForm());
                    newLine.setLeaseTime(syncLine.getLeaseTime());
                }
                newLine.setPlacedQty(BigDecimal.ZERO);
                newLine.setPlacingQty(BigDecimal.ZERO);
                newLine.setStatus(YzContractDetailStatusDict.ACTIVE);
                newLines.add(newLine);
            });

            log.info("非集采补充合同行--封装完成，newLines：{}", JSONObject.toJSONString(newLines));
            DS.create(newLines);
            return;
        }

        //正常情况下，创建成功后直接返回，没有return则抛出异常
        throw new RuntimeException("非集采补充合同行--同步端点失败！");
    }

    /**
     * 品牌
     * @param detail
     * @return
     */
    private static List<BrandBO> buildBidBrand(YzContractDetailForNcp detail) {
        if (CollectionUtils.isEmpty(detail.getBiddingBrand())) {
            return null;
        }

        try {
            Set<String> brandCodes = detail.getBiddingBrand().stream().map(e -> e.getString("brandCode"))
                    .filter(Objects::nonNull).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(brandCodes)) {
                List<BrandBO> brands = DS.findAll(BrandBO.class, "id,brandCode,brandName", "brandCode IN (?)", brandCodes);
                return brands;
            }
            return null;
        } catch (Exception ex) {
            log.error("招标品牌反序列化失败, snapshot:{},cause:{}", Json.toJson(detail.getBiddingBrand()), Throwables.getStackTraceAsString(ex));
            return null;
        }
    }

    /**
     * 应收账期方案快照
     * @param contractDetail
     * @return
     */
    private static List<PayableSchemeTemplateBO> buildPayableSchemeTemplateSnapshot(YzContractDetailForNcp contractDetail) {
        if (StringUtils.isBlank(contractDetail.getPayablePeriod())) {
            return null;
        }
        try {
            return JSON.parseArray(contractDetail.getPayablePeriod(), PayableSchemeTemplateBO.class);
        } catch (Exception ex) {
            log.error("应收账期方案快照反序列化失败, snapshot:{},cause:{}", contractDetail.getPriceScheme(), Throwables.getStackTraceAsString(ex));
            return null;
        }
    }

    /**
     * 价格方案
     * @param contractDetail
     * @return
     */
    private static PriceSchemeBO buildPriceSchemeSnapshot(YzContractDetailForNcp contractDetail) {
        if (StringUtils.isBlank(contractDetail.getPriceScheme())) {
            return null;
        }

        try {
            return JSON.parseObject(contractDetail.getPriceScheme(), PriceSchemeBO.class);
        } catch (Exception ex) {
            log.error("价格方案快照反序列化失败, snapshot:{},cause:{}", contractDetail.getPriceScheme(), Throwables.getStackTraceAsString(ex));
            return null;
        }
    }

    /**
     * sku快照
     * @param yzContractDetail
     * @return
     */
    private static SkuBOExt buildSkuSnapshot(YzContractDetailForNcp yzContractDetail) {
        if (StringUtils.isBlank(yzContractDetail.getSkuSnapshot())) {
            return null;
        }
        try {
            SkuBOExt skuSnapshot = JSON.parseObject(yzContractDetail.getSkuSnapshot(), SkuBOExt.class);
            if (skuSnapshot != null) {
                SkuBOExt skuBO = DS.findById(SkuBOExt.class, skuSnapshot.getId(), "*,item.*");
                // 不执行兼容，为空则填空
                String itemTranscript = Optional.ofNullable(skuBO).map(SkuBO::getItem).map(ItemBO::getItemTranscript).orElse(null);
                if (skuSnapshot.getItem() == null) {
                    ItemBO itemBO = new ItemBO();
                    itemBO.setItemTranscript(itemTranscript);
                    skuSnapshot.setItem(itemBO);
                } else {
                    if (skuSnapshot.getItem().getItemTranscript() == null) {
                        skuSnapshot.getItem().setItemTranscript(itemTranscript);
                    }
                }
            }
            return skuSnapshot;
        } catch (Exception ex) {
            log.error("SKU快照反序列化失败, snapshot:{},cause:{}", yzContractDetail.getSkuSnapshot(), Throwables.getStackTraceAsString(ex));
            return null;
        }
    }

    public static String convertDetailStatus(Integer code) {
        if (code == null) {
            return null;
        }
        switch (code) {
            case 1:
                return YzContractStatusDict.ACTIVE;
            case -1:
                return YzContractStatusDict.STOP;
        }
        return null;
    }
}
