package io.terminus.gaia.contract.func.sign.write;

import io.terminus.gaia.contract.model.sign.SignTaskBO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * 签署任务签署成功
 * @Date 2021/4/15
 * <AUTHOR>
 */
@Function(name = "signed sign task")
public interface SignedSignTaskFunc {

    /**
     * 签署任务已签署
     * @param updateBO 待修改签署任务信息
     * @return 修改结果
     */
    BooleanResult execute(SignTaskBO updateBO);
}
