import {Controller} from 'nusi-sdk'

export default class extends Controller {

    columns = [
        {title: '材料名称', key: 'askPriceLineBO.materialName'},
        {title: '规格型号', key: 'askPriceLineBO.thingSizeDesc'},
        {title: '数量', key: 'askPriceLineBO.needNum'},
        {title: '计量单位', key: 'askPriceLineBO.unitName'},
        {title: '关联标品编码', key: 'askPriceLineBO.spuCode'},
        {title: '关联标品名称', key: 'askPriceLineBO.spuName'},
        {title: '含铜量（kg/米）', key: 'askPriceLineBO.rawMaterialContent'},
        {title: '铜基价（含税）（元/吨）', key: 'askPriceLineBO.purCopperBasicPrice'},
        {title: '延米铜价（含税）（元/米）', key: 'askPriceLineBO.purCopperPrice'},
        { title: '综合单价（含税）（元/米）', key: 'purTaxPrice' }
    ]


    getTabs = () => {
        const askSupplierPrice = this.getContainerByKey('AskSupplierPrice').data
        const currentRound = askSupplierPrice?.currentRound
        if (!currentRound) {
            return []
        }

        return Array.from({length: currentRound}, (_, i) => ({
            title: `第${currentRound - i}轮`,
            key: currentRound - i,
            disabled: currentRound - i != currentRound
        }));

    }
}
