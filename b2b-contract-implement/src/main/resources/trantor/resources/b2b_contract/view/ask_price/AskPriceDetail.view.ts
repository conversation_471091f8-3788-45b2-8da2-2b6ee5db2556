import { Controller, state } from 'nusi-sdk'

export default class extends Controller {
    @state
    columns = []
    @state
    tabs = []

    initColumns = [
        {title: '材料名称', key: 'materialName'},
        {title: '规格型号', key: 'thingSizeDesc'},
        {title: '数量', key: 'needNum'},
        {title: '计量单位', key: 'unit.unitName'},
        {title: '关联标品编码', key: 'spu.spuCode'},
        {title: '关联标品名称', key: 'spu.name'},
        {title: '含铜量（kg/米）', key: 'rawMaterialContent'},
        {title: '铜基价（含税）（元/吨）', key: 'purCopperBasicPrice'},
        {title: '延米铜价（含税）（元/米）', key: 'purCopperPrice'}
        //每个供应商报的含税单价 todo

    ]

    dataLoaded = () => {
        //生成列名信息
        const data = this.getContainerByKey('AskPrice')?.data
        console.log({'AskPrice Data': data})
        // this.columns = [
        //     ...this.initColumns,
        //     {title: '测试', key: 'code'},
        //
        // ]

        //生成轮次名信息
        const currentRound = this.getContainerByKey('AskPrice')?.data?.currentRound
        for (let round = currentRound; round > 0; round--) {
            this.tabs = [
                ...this.tabs,
                {title: round + '轮报价', key: round}
            ]
        }


    }


    openRejectDialog = ()=>{
        const askPrice = this.getContainerByKey('AskPrice').data

        this.openView('b2b_contract_AskSupplierPriceReplyTO_AskPriceRejectSupplier', {
            openViewType: 'Dialog',
            env: {askPriceId:askPrice.id},
            payloadCallback: (payload) => {

            }
        })
    }



}
