package io.terminus.gaia.contract.flow.contract.write;

import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.Flow;

/**
 * 合同变更处理完成的回调
 *
 * <AUTHOR>
 */
@Flow(name = "Modify Contract Callback")
public interface ModifyContractCallbackFlow {

    void execute(ContractBO handledContract, BooleanResult needModify);
}
