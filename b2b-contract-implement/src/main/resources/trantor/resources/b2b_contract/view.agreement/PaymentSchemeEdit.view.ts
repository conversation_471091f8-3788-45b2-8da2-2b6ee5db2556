import {Controller} from 'nusi-sdk'

export default class extends Controller {
    // 回到上一个页面
    goBackWithRecord = async ({record}) => {
        console.log("goBackWithRecord ", record)
        this.goBack({
            withPayload: true,
            payload: record
        })
    }

    // 校验方案
    validateScheme = ({data}) => {
        for (let i = 0; i < data.schemeNodes.length; i++) {
            let node = data.schemeNodes[i];
            if (!node.nodeDict) {
                return "款项类型不能为空";
            }
            if (!node.rate) {
                return "付款比例不能为空";
            }
            if (!node.triggerTypeDict) {
                return "触发方式不能为空";
            }
            if ('auto' === node.triggerTypeDict) {
                if (!node.triggerDay) {
                    return "付款节点不能为空";
                }
                if (node.nodeDict == 'progress') {
                    if (!node.triggerMonth) {
                        return "付款节点不能为空";
                    }
                }
            }
        }

        const nodeDictSet = new Set<string>();
        let hasDuplicateNodeDict = false;

        data.schemeNodes.some((node) => {
            if (nodeDictSet.has(node.nodeDict)) {
                hasDuplicateNodeDict = true;
                return true;
            }
            nodeDictSet.add(node.nodeDict);
            return false;
        });

        if (hasDuplicateNodeDict) {
            return "付款节点不能重复"
        }

        return true;
    }

    getRateTemplate = (record) => {
        const {nodeDict} = record;
        let prefixes = {
            guarantee: '支付至结算金额的',
            complete: '支付至结算金额的',
            settlement: '支付至结算金额的',
            progress: '支付已对帐金额的',
            advance: '预支付合同金额的',
            special: '付款比例',
        };
        if (prefixes[nodeDict]) {
            return `${prefixes[nodeDict] || ''} @{rate} %`;
        } else {
            return ''
        }
    }
    getPayNodeTemplate = (record) => {
        const {nodeDict} = record;
        if ('manual' === record.triggerTypeDict) {
            return '';
        }
        let prefixes = {
            advance: '合同签约后 ',
            progress: '每 @{triggerMonth} 个月',
            settlement: '合同结算日起',
            complete: '工程竣备后 ',
            guarantee: '验收合格后 ',
        };
        let suffixes = {
            advance: '天 ',
            progress: '号',
            settlement: '天',
            complete: '天 ',
            guarantee: '天 ',
        };
        return `@{triggerTypeDict} ${prefixes[nodeDict] || ''} @{triggerDay} ${suffixes[nodeDict] || ''}`;
    }

    // 触发方式
    getTriggerType = (record) => {
        const {nodeDict} = record;
        console.log('getTriggerType', nodeDict)
        if (['settlement', 'complete', 'guarantee'].includes(nodeDict)) {
            return () => ['manual'];
        }
        return () => ['auto', 'manual'];
    }
}
