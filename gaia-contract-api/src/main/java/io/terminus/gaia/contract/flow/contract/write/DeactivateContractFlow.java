package io.terminus.gaia.contract.flow.contract.write;

import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.trantorframework.api.annotation.Flow;

/**
 * 停用合同
 * @Author: fengbo
 * @Date: 2021/8/15
 */
@Flow(name = "deactivate contract flow")
public interface DeactivateContractFlow {

    /**
     * 停用合同
     *
     * @param contract 停用合同
     */
    void execute(ContractBO contract);
}
