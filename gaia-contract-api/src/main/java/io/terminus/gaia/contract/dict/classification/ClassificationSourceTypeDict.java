package io.terminus.gaia.contract.dict.classification;

import io.terminus.trantorframework.api.annotation.Dictionary;
import io.terminus.trantorframework.api.annotation.DictionaryItem;

/**
 * 合同分类来源
 * <AUTHOR>
 */
@Dictionary(name = "Classification Source Type Dict")
public interface ClassificationSourceTypeDict {

    /**
     * 物料
     */
    @DictionaryItem(value = "material")
    String MATERIAL = "MATERIAL";

    /**
     * SKU
     */
    @DictionaryItem(value = "sku")
    String SKU = "SKU";

    /**
     * 无
     */
    @DictionaryItem(value = "null")
    String NULL = "NULL";
}
