package io.terminus.gaia.app.b2b.contract.func.purchase;

import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseLineBO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @class_name: SaveSupplierPriceFuncImpl
 * @desc: 保存供应商报价，计算相关价格
 * @date: 2025/7/7 : 17:46
 * @author: Chonor
 **/
@FunctionImpl
@Slf4j
public class SaveSupplierPriceFuncImpl implements SaveSupplierPriceFunc{
    
    @Override
    public BooleanResult execute(List<RequestPurchaseLineBO> requestPurchaseLineBOList) {
        
        // 参数验证
        if (CollectionUtils.isEmpty(requestPurchaseLineBOList)) {
            throw new BusinessException("需求明细列表不能为空");
        }
        
        log.info("开始处理供应商报价，共{}条明细", requestPurchaseLineBOList.size());
        
        // 批量查询现有数据，避免在循环中查询数据库
        List<Long> lineIds = requestPurchaseLineBOList.stream()
                .map(RequestPurchaseLineBO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        if (CollectionUtils.isEmpty(lineIds)) {
            throw new BusinessException("所有需求明细的ID都为空");
        }
        
        String whereClause = "id IN (" + lineIds.stream().map(String::valueOf).collect(Collectors.joining(",")) + ")";
        List<RequestPurchaseLineBO> existingLines = DS.findAll(RequestPurchaseLineBO.class, 
                "*,requestPurchaseBO.basicPriceBO.*", whereClause);
        
        // 构建ID到对象的映射，便于快速查找
        Map<Long, RequestPurchaseLineBO> existingLineMap = existingLines.stream()
                .collect(Collectors.toMap(RequestPurchaseLineBO::getId, line -> line));
        
        log.info("批量查询到{}条现有明细数据", existingLines.size());
        
        List<RequestPurchaseLineBO> updateList = new ArrayList<>();
        
        for (RequestPurchaseLineBO inputLine : requestPurchaseLineBOList) {
            try {
                RequestPurchaseLineBO existingLine = existingLineMap.get(inputLine.getId());
                RequestPurchaseLineBO updateLine = processSupplierPrice(inputLine, existingLine);
                if (updateLine != null) {
                    updateList.add(updateLine);
                }
            } catch (Exception e) {
                log.error("处理需求明细ID: {} 时发生错误: {}", inputLine.getId(), e.getMessage(), e);
                throw new BusinessException("处理明细ID: " + inputLine.getId() + " 时发生错误: " + e.getMessage());
            }
        }
        
        if (CollectionUtils.isNotEmpty(updateList)) {
            DS.update(updateList);
            log.info("成功更新{}条需求明细的价格信息", updateList.size());
        }
        
        return BooleanResult.TRUE;
    }
    
    /**
     * 处理单条需求明细的供应商报价
     */
    private RequestPurchaseLineBO processSupplierPrice(RequestPurchaseLineBO inputLine, RequestPurchaseLineBO existingLine) {
        
        // 参数验证
        if (Objects.isNull(inputLine.getId())) {
            throw new BusinessException("需求明细ID不能为空");
        }
        
        if (Objects.isNull(inputLine.getPurTaxPrice())) {
            throw new BusinessException("需求明细ID: {"+ inputLine.getId() +"} 的采购含税单价为空");
        }
        
        if (Objects.isNull(existingLine)) {
            throw new BusinessException("未找到ID为 " + inputLine.getId() + " 的需求明细");
        }
        
        log.info("处理需求明细ID: {}, 供应商报价: {}", inputLine.getId(), inputLine.getPurTaxPrice());
        
        // 获取必要的基础数据
        BigDecimal purTaxPrice = inputLine.getPurTaxPrice(); // 供应商报价的采购含税单价
        BigDecimal purDiscountFactor = getValueOrDefault(existingLine.getPurDiscountFactor(), BigDecimal.ONE, "采购折扣系数");
        BigDecimal purCopperBasicPrice = getValueOrDefault(existingLine.getPurCopperBasicPrice(), BigDecimal.ZERO, "铜基价");
        BigDecimal purRawMaterialContent = getValueOrDefault(existingLine.getPurRawMaterialContent(), BigDecimal.ZERO, "含铜量");
        BigDecimal saleDiscountFactor = getValueOrDefault(existingLine.getSaleDiscountFactor(), BigDecimal.ONE, "销售折扣系数");
        
        // 如果没有铜基价，尝试从需求单中获取
//        if (purCopperBasicPrice.compareTo(BigDecimal.ZERO) == 0 &&
//            existingLine.getRequestPurchaseBO() != null &&
//            existingLine.getRequestPurchaseBO().getBasicPriceBO() != null &&
//            existingLine.getRequestPurchaseBO().getBasicPriceBO().getPriceWithTax() != null) {
//            purCopperBasicPrice = existingLine.getRequestPurchaseBO().getBasicPriceBO().getPriceWithTax().getValue();
//            log.info("从需求单获取铜基价: {}", purCopperBasicPrice);
//        }
        
        // 创建更新对象
        RequestPurchaseLineBO updateLine = new RequestPurchaseLineBO();
        updateLine.setId(inputLine.getId());
        
        // 1. 更新供应商报价
        updateLine.setPurTaxPrice(purTaxPrice);
        
        // 2. 计算采购辅材及其他价格
        // 公式：采购含税单价 = （铜基价*含铜量/1000+采购辅材及其他价格）*采购折扣系数
        // 反算：采购辅材及其他价格 = 采购含税单价/采购折扣系数 - 铜基价*含铜量/1000
        BigDecimal copperPricePerMeter = purCopperBasicPrice.multiply(purRawMaterialContent)
                .divide(new BigDecimal("1000"), 6, RoundingMode.HALF_UP);
        
        BigDecimal purOtherCosts = purTaxPrice.divide(purDiscountFactor, 6, RoundingMode.HALF_UP)
                .subtract(copperPricePerMeter)
                .setScale(2, RoundingMode.HALF_UP);
        
        // 确保采购辅材价格不为负数
//        if (purOtherCosts.compareTo(BigDecimal.ZERO) < 0) {
//            log.warn("需求明细ID: {} 计算出的采购辅材价格为负数: {}，设置为0", inputLine.getId(), purOtherCosts);
//            purOtherCosts = BigDecimal.ZERO;
//        }
        
        updateLine.setPurOtherCosts(purOtherCosts);
        
        // 3. 设置销售辅材及其他价格（等于采购辅材及其他价格）
        updateLine.setSaleOtherCosts(purOtherCosts);
        
        // 4. 计算销售含税单价
        // 公式：销售含税单价 = （采购协议铜基价*标品含铜量/1000+销售辅材及其他价格）*销售折扣系数
        BigDecimal saleTaxPrice = copperPricePerMeter.add(purOtherCosts)
                .multiply(saleDiscountFactor)
                .setScale(2, RoundingMode.HALF_UP);
        
        updateLine.setSaleTaxPrice(saleTaxPrice);
        
        // 5. 计算销售利润率
        // 公式：销售利润率 = （销售含税单价-采购含税单价）/采购含税单价*100%
        BigDecimal profitRate = BigDecimal.ZERO;
        if (purTaxPrice.compareTo(BigDecimal.ZERO) > 0) {
            profitRate = saleTaxPrice.subtract(purTaxPrice)
                    .divide(purTaxPrice, 6, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"))
                    .setScale(2, RoundingMode.HALF_UP);
        }
        
        updateLine.setProfitRate(profitRate);
        
        // 6. 计算采购延米铜价和销售延米铜价（都等于铜基价*含铜量/1000）
        updateLine.setPurCopperPrice(copperPricePerMeter.setScale(2, RoundingMode.HALF_UP));
        updateLine.setSaleCopperPrice(copperPricePerMeter.setScale(2, RoundingMode.HALF_UP));
        
        log.info("需求明细ID: {} 价格计算完成 - 采购辅材价格: {}, 销售含税单价: {}, 销售利润率: {}%", 
                inputLine.getId(), purOtherCosts, saleTaxPrice, profitRate);
        
        return updateLine;
    }
    
    /**
     * 获取数值，如果为空则返回默认值
     */
    private BigDecimal getValueOrDefault(BigDecimal value, BigDecimal defaultValue, String fieldName) {
        if (Objects.isNull(value)) {
            log.debug("字段 {} 为空，使用默认值: {}", fieldName, defaultValue);
            return defaultValue;
        }
        return value;
    }
}
