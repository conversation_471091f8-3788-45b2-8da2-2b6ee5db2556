package io.terminus.gaia.app.b2b.contract.model;

import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.RootModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.FieldType;
import io.terminus.trantorframework.api.annotation.Model;
import io.terminus.trantorframework.api.annotation.TransientModel;
import io.terminus.trantorframework.api.annotation.typemeta.AttachmentMeta;
import io.terminus.trantorframework.api.type.Attachment;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version 1.0
 * @classname AgreementTemplateUpdateFileTO
 * @description TODO
 * @date 2024/3/18 18:08
 */
@TransientModel(
        name = "AgreementTemplateUpdateFileTO",
        desc = "合同模版上传文件模型"
)
@Data
public class AgreementTemplateUpdateFileTO extends RootModel<Long> {
    @Field(name = "清单", desc = "附件" , type = FieldType.Attachment)
    @AttachmentMeta(countLimit = 1, maxSize = 1024 * 1024)
    private Attachment agreementTemplateFile;
}
