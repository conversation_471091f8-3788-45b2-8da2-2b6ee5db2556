package io.terminus.gaia.app.b2b.contract.func.agreement.template;

import cn.hutool.core.collection.CollUtil;
import io.terminus.datastore.dsl.Query;
import io.terminus.datastore.dsl.impl.TSQL;
import io.terminus.gaia.app.b2b.contract.dict.AgreementDetailSourceDict;
import io.terminus.gaia.app.b2b.contract.model.AgreementDetailTemplateBO;
import io.terminus.gaia.app.b2b.contract.model.AgreementTemplateBO;
import io.terminus.gaia.app.b2b.contract.model.AgreementTemplateCoverAreaBO;
import io.terminus.gaia.app.b2b.contract.spring.validator.AgreementValidator;
import io.terminus.gaia.app.b2b.contract.util.DistrictUtil;
import io.terminus.gaia.md.model.DistrictBO;
import io.terminus.gaia.organization.context.UserInfoContext;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/23
 */
@FunctionImpl
@Slf4j
@AllArgsConstructor
public class UpdateAgreementTemplateFuncImpl implements UpdateAgreementTemplateFunc{
    private final AgreementValidator agreementValidator;
    /**
     * 从上下文中获取当前用户信息
     */
    private final UserInfoContext userInfoContext;
    
    @Override
    public BooleanResult execute(AgreementTemplateBO agreementTemplateBO) {
        
        //参数校验
        agreementValidator.checkTemplateCreateOrUpdate(agreementTemplateBO);
        
        //协议清单模版
        List<AgreementDetailTemplateBO> templateBODetails = agreementTemplateBO.getDetails();
        if(CollectionUtils.isNotEmpty(templateBODetails)){
            //这里先全部删除清单模版
            Query delete = TSQL.delete(AgreementDetailTemplateBO.class).where(TSQL.field(AgreementDetailTemplateBO.agreementTemplate_field).eq(agreementTemplateBO.getId()));
            DS.delete(delete);
            List<AgreementDetailTemplateBO> detailTemplateBOList = templateBODetails.stream().peek(detail -> {
                detail.setAgreementTemplate(agreementTemplateBO);
                detail.setId(null);
                detail.setBrandBo(null);
                detail.setBrands(null);
                detail.setPriceSchemeBO(null);
                detail.setPrice(null);
                detail.setTaxPrice(null);
                detail.setCreatedAt(new Date());
                detail.setCreatedBy(userInfoContext.getUserInfo().getUser());
                detail.setSource(AgreementDetailSourceDict.AUTOMATIC);
            }).collect(Collectors.toList());
            DS.create(detailTemplateBOList);
        }

        //指定范围
        List<AgreementTemplateCoverAreaBO> coverAreas = agreementTemplateBO.getCoverAreas();
        if (CollectionUtils.isNotEmpty(coverAreas)) {
            Query delete = TSQL.delete(AgreementTemplateCoverAreaBO.class).where(TSQL.field(AgreementTemplateCoverAreaBO.agreementTemplateBO_field).eq(agreementTemplateBO.getId()));
            DS.delete(delete);
            List<AgreementTemplateCoverAreaBO> newCoverAreas = coverAreas.stream().peek(coverArea -> {
                coverArea.setAgreementTemplateBO(agreementTemplateBO);
                coverArea.setId(null);
                List<DistrictBO> requestDistricts = CollUtil.defaultIfEmpty(coverArea.getDistricts(), new ArrayList<>());
                coverArea.setDistrictStr(DistrictUtil.buildDistrictStr(requestDistricts));
            }).collect(Collectors.toList());
            DS.create(newCoverAreas);
        }
        return DS.updateWithNull(agreementTemplateBO);
    }

}
