package io.terminus.gaia.app.b2b.contract.func.price;

import io.terminus.gaia.app.b2b.contract.tmodel.SpuAgreementTO;
import io.terminus.gaia.app.b2b.contract.tmodel.query.QSpuAgreementTO;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * <AUTHOR>
 * @date 2023-05-31
 * @descrition
 */
@Function
public interface PagingSpuAgreementFunc {

    Paging<SpuAgreementTO> execute(QSpuAgreementTO qSpuAgreementTO);

}
