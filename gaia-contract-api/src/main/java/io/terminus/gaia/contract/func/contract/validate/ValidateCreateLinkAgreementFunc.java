package io.terminus.gaia.contract.func.contract.validate;

import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * 生成关联协议参数校验
 * @Author: fengbo
 * @Date: 2021/8/10
 */
@Function(name = "validate create ling agreement func")
public interface ValidateCreateLinkAgreementFunc {
    /**
     * 校验 生成关联协议参数校验
     * @param contract 框架合同
     */
    void execute(ContractBO contract);
}
