package io.terminus.gaia.contract.func.sign.validate;

import io.terminus.gaia.contract.model.sign.SignTaskBO;
import io.terminus.gaia.contract.model.sign.SignatoriesBO;
import io.terminus.gaia.contract.msg.sign.SignTaskExMsg;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Date 2021/4/12
 * <AUTHOR>
 */
@FunctionImpl(name = "validate create sign task param")
public class ValidateCreateSignTaskParamFuncImpl implements ValidateCreateSignTaskParamFunc {


    /**
     * 校验创建签署任务参数是否合法
     * @param signTaskBO 签署任务
     * @throws BusinessException Contract.parametersIncomplete 参数不合法
     */
    @Override
    public void execute(SignTaskBO signTaskBO) {

        //标题
        Optional.ofNullable(signTaskBO.getTitle()).orElseThrow(() -> new BusinessException(SignTaskExMsg.TITLE_NOT_EMPTY));
        //签署方式
        Optional.ofNullable(signTaskBO.getSignWayDict()).orElseThrow(() -> new BusinessException(SignTaskExMsg.SIGNING_METHOD));
        //所属公司
        Optional.ofNullable(signTaskBO.getCompany()).orElseThrow(() -> new BusinessException(SignTaskExMsg.OWNED_COMPANY));

        //签署任务
        Optional.ofNullable(signTaskBO).orElseThrow(() -> new BusinessException(SignTaskExMsg.SIGN_THE_TASK));
        //签署文件
        if (CollectionUtils.isEmpty(signTaskBO.getSignFileList())) {
            throw new BusinessException(SignTaskExMsg.SIG_FILE);
        } else {
            //文件名称
            signTaskBO.getSignFileList().forEach(signFileBO -> {
                Optional.ofNullable(signFileBO.getFileName()).orElseThrow(() -> new BusinessException(SignTaskExMsg.SIG_FILE_NAME));
            });
        }
        //签署方
        if (CollectionUtils.isEmpty(signTaskBO.getSignatoriesList())) {
            throw new BusinessException(SignTaskExMsg.SIGN_OWNER);
        }

        //签署顺序
        final int[] time = {1};
        List<SignatoriesBO> signatoriesBOSortList = signTaskBO.getSignatoriesList().stream()
                .sorted(Comparator.comparing(SignatoriesBO::getSortNum)).collect(Collectors.toList());
        signatoriesBOSortList.forEach(node ->{
            if(node.getSortNum() == time[0]){
                time[0]++;
            }else{
                throw new BusinessException(SignTaskExMsg.SORT_NUM_FAIL);
            }
        });

    }
}
