package io.terminus.gaia.app.b2b.contract.func.askprice.read;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Opt;
import io.terminus.gaia.app.b2b.contract.model.AskPriceLineBO;
import io.terminus.gaia.app.b2b.contract.model.query.QAskPriceBO;
import io.terminus.gaia.app.b2b.contract.model.query.QAskPriceLineBO;
import io.terminus.gaia.contract.utils.ExceptionUtil;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.querymodel.Select;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 按照询价单id，查 询价单详情
 *
 * @author: huangjunwei
 **/
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class QueryRoundAskPriceLineFuncImpl implements QueryRoundAskPriceLineFunc {
    @Override
    public Paging<AskPriceLineBO> execute(QAskPriceLineBO req) {
        validate(req);

        //分页查询
        Select select = new Select();
        select.addField("*")
                .addField(AskPriceLineBO.unit_field + ".*")
                .addField(AskPriceLineBO.spu_field + ".*");
        req.getQueryParams().setSelect(select);
        Paging<AskPriceLineBO> askPriceLineBOPaging = DS.paging(req);
        return askPriceLineBOPaging;
    }

    private void validate(QAskPriceLineBO req) {
        Assert.notNull(Opt.ofNullable(req.getAskPriceBO()).map(QAskPriceBO::getId).orElse(null), ExceptionUtil.create("询价单不能为空"));
        Assert.notNull(req.getRound(), ExceptionUtil.create("当前轮数不能为空"));
    }
}
