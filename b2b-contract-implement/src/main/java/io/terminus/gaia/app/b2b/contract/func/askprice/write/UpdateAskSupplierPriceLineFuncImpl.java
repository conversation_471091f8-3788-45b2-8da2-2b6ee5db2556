package io.terminus.gaia.app.b2b.contract.func.askprice.write;

import cn.hutool.core.lang.Assert;
import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceLineBO;
import io.terminus.gaia.contract.utils.ExceptionUtil;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @time 2025/7/8 17:25
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class UpdateAskSupplierPriceLineFuncImpl implements UpdateAskSupplierPriceLineFunc {
    @Override
    public AskSupplierPriceLineBO execute(AskSupplierPriceLineBO req) {
        Assert.notNull(req.getId(), ExceptionUtil.create("id不能为空"));
        Assert.notNull(req.getPurTaxPrice(), ExceptionUtil.create("综合单价不能为空"));

        AskSupplierPriceLineBO askSupplierPriceLineNew = new AskSupplierPriceLineBO();
        askSupplierPriceLineNew.setId(req.getId());
        askSupplierPriceLineNew.setPurTaxPrice(req.getPurTaxPrice());
        DS.update(askSupplierPriceLineNew);

        AskSupplierPriceLineBO askSupplierPriceLineBO = DS.findById(AskSupplierPriceLineBO.class, req.getId());

        return askSupplierPriceLineBO;
    }
}
