package io.terminus.gaia.contract.flow.contract.write;

import io.terminus.gaia.contract.BaseService;
import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.gaia.contract.util.ContractUtil;
import io.terminus.trantorframework.sdk.sql.DS;
import org.junit.Before;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2021-04-15
 */
public class DeleteContractFlowTests extends BaseService {
    private DeleteContractFlow deleteContractFlow;

    private ContractBO contract;

    @Before
    public void init() {
        contract = ContractUtil.buildContract();
    }

    @Test
    public void test() {
        DS.save(contract);

        deleteContractFlow.execute(contract);
    }


}
