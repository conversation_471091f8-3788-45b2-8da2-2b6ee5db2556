package io.terminus.gaia.contract.flow.sign.write;

import io.terminus.gaia.contract.model.sign.SignatureBO;
import io.terminus.trantorframework.api.annotation.Flow;

import java.util.List;

/**
 * 批量启用电子签名
 *
 * <AUTHOR>
 */
@Flow(name = "Batch enable signature flow")
public interface BatchEnableSignatureFlow {
    /**
     * 批量启用电子签名
     * @param signatureList 待启用的电子签名
     */
    void execute(List<SignatureBO> signatureList);
}
