package io.terminus.gaia.app.b2b.contract.func.price.write;

import cn.hutool.core.lang.Assert;
import io.terminus.gaia.app.b2b.contract.dict.price.SalePriceCalStatusDict;
import io.terminus.gaia.app.b2b.contract.model.price.SalePriceCalBO;
import io.terminus.gaia.app.b2b.contract.model.price.SalePriceCalLineBO;
import io.terminus.gaia.contract.utils.ExceptionUtil;
import io.terminus.platform.sdk.transaction.DSTransaction;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2024/3/12 15:57
 */
@FunctionImpl
@Slf4j
@RequiredArgsConstructor
public class DelSalePriceCalFuncImpl implements DelSalePriceCalFunc {

    @Override
    @DSTransaction
    public BooleanResult execute(List<SalePriceCalBO> req) {
        for (SalePriceCalBO salePriceCalBO : req) {
            Assert.notNull(salePriceCalBO.getId(), ExceptionUtil.create("id不能为空"));
        }

        Set<Long> ids = req.stream().map(SalePriceCalBO::getId).collect(Collectors.toSet());
        //List<SalePriceCalLineBO> salePriceCalLineBOS = DS.findAll(SalePriceCalLineBO.class, "id", "salePriceCalBO in (?)", ids);

        DS.delete(req);
        DS.update(SalePriceCalLineBO.class, "status=?", "salePriceCalBO in (?)", SalePriceCalStatusDict.DISABLED, ids);
        //DS.delete(salePriceCalLineBOS);
        return BooleanResult.TRUE;
    }
}
