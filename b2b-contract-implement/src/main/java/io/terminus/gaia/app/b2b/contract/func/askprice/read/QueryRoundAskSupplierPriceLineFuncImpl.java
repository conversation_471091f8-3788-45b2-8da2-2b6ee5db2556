package io.terminus.gaia.app.b2b.contract.func.askprice.read;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Opt;
import io.terminus.gaia.app.b2b.contract.model.AskPriceLineBO;
import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceLineBO;
import io.terminus.gaia.app.b2b.contract.model.query.QAskPriceBO;
import io.terminus.gaia.app.b2b.contract.model.query.QAskSupplierPriceLineBO;
import io.terminus.gaia.common.utils.EnhanceDS;
import io.terminus.gaia.contract.utils.ExceptionUtil;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.querymodel.Select;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @time 2025/7/8 16:26
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class QueryRoundAskSupplierPriceLineFuncImpl implements QueryRoundAskSupplierPriceLineFunc {
    @Override
    public Paging<AskSupplierPriceLineBO> execute(QAskSupplierPriceLineBO req) {
        validate(req);

        //分页查询
        EnhanceDS.addQueryField(req, "askPriceLineBO.*");
        Paging<AskSupplierPriceLineBO> res = DS.paging(req);
        return res;
    }

    private void validate(QAskSupplierPriceLineBO req) {
        Assert.notNull(Opt.ofNullable(req.getAskPriceBO()).map(QAskPriceBO::getId).orElse(null), ExceptionUtil.create("询价单不能为空"));
        Assert.notNull(req.getRound(), ExceptionUtil.create("当前轮数不能为空"));
    }
}
