package io.terminus.gaia.contract.func.distributor.convert;

import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.gaia.contract.model.contract.ContractDistributorBO;
import io.terminus.trantorframework.api.annotation.Function;

import java.util.List;

/**
 * 数据转换
 *
 * @Author: fengbo
 * @Date: 2021/8/11
 */
@Function(name = "convert Contract to ContractDistributor func")
public interface ConvertContractToContractDistributorFunc {

    /**
     * 数据转换
     *
     * @param contract
     * @return
     */
    List<ContractDistributorBO> execute(ContractBO contract);
}
