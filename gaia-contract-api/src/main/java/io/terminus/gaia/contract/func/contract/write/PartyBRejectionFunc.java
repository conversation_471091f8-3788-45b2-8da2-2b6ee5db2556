package io.terminus.gaia.contract.func.contract.write;

import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * 乙方拒绝
 * @Author: fengbo
 * @Date: 2021/8/7
 */
@Function(name = "Party B Reject")
public interface PartyBRejectionFunc {
    /**
     * 乙方拒绝
     * @param contractBO 框架合同
     * @return 框架合同
     */
    ContractBO execute(ContractBO contractBO);
}
