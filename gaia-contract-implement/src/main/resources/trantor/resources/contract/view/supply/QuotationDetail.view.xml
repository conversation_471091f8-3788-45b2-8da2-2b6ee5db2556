<?xml version="1.0" encoding="UTF-8"?>
<View title="QuotationDetail" forModel="contract_QuotationBO" type="Form" version="2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://trantor-docs-dev.app.terminus.io/static/v0.16.x/schema/base.xsd">
    <Detail key="quotationDetail" model="contract_QuotationBO" dataCondition="#{`id = ${pageContext.record.id}`}">
        <Fields>
            <GroupField title="Basic Info">
                <Field name="quotationCode" show="#{false}" submit="#{true}"/>
                <Field name="quotationTitle">
                    <Validations>
                        <Validation required="#{true}"/>
                    </Validations>
                </Field>
                <Field name="currency"/>
                <Field name="quotationBusinessTypeDict" initValue="#{this.record.quotationBusinessTypeDict? 'PURCHASE': this.record.quotationBusinessTypeDict}" readonly="#{true}" submit="#{true}"/>
                <Field name="company"/>
                <Field name="quotationProvider"/>
                <Field name="startDate"/>
                <Field name="endDate"/>
                <Field name="attachment"/>
                <Field name="remark"/>
            </GroupField>
            <GroupField title="Suitable Condition">
                <Field name="isDefault"/>
                <Field name="suitableList"/>
                <Field name="suitableUnder"/>
            </GroupField>
            <GroupField title="Other Suitable Condition">
                <Field name="quotationCondition.businessGroupList"/>
                <Field name="quotationCondition.entityBOList"/>
                <Field name="quotationCondition.channelList"/>
            </GroupField>
        </Fields>
        <Actions>
            <Action label="edit" show="#{this.record.contractStatusDict==='CREATED'}" targetView="contract_QuotationBO_CreateQuotation"/>
            <Action label="delete" show="#{this.record.contractStatusDict==='CREATED'}" confirm="" logicFlow="contract_DeleteQuotationFlow"/>
            <Action label="close" show="#{this.record.contractStatusDict === 'ENABLED' || this.record.contractStatusDict === 'STOP'}" logicFlow="contract_CloseQuotationFlow"/>
            <Action label="delay" show="#{this.record.contractStatusDict === 'ENABLED' || this.record.contractStatusDict === 'STOP' || this.record.contractStatusDict === 'CLOSED'}" logicFlow="contract_DelayQuotationFlow"/>
            <Action label="enable" show="#{this.record.contractStatusDict === 'CREATED' || this.record.contractStatusDict === 'STOP'}" logicFlow="contract_EnableQuotationFlow"/>
            <Action label="disable" show="#{this.record.contractStatusDict === 'ENABLED'}" logicFlow="contract_DisableQuotationFlow"/>
        </Actions>
    </Detail>
    <Anchors>
        <Anchor>
            <TableForm key="contract_priceList" title="price list" advanced="false" model="contract_PriceListBO" lookupFrom="quotationDetail.priceListBOList" showAdd="#{false}" showDelete="#{false}">
                <Fields>
                    <Field name="material" readonly="#{true}">
                        <RenderType>
                            <ModelSelect extraFields="thingCode,thingName,thingType,category.taxRate,thingStockUnit"/>
                        </RenderType>
                        <Validations>
                            <Validation required="#{true}"/>
                        </Validations>
                    </Field>
                    <Field name="material.thingCode" readonly="#{true}"/>
                    <Field name="material.thingType" readonly="#{true}"/>
                    <Field name="material.category" readonly="#{true}"/>
                    <Field name="material.thingSize" readonly="#{true}"/>
                    <Field name="material.thingStockUnit" readonly="#{true}"/>
                    <Field name="materialPrc" readonly="#{true}"/>
                    <Field name="materialRate" readonly="#{true}"/>
                    <Field name="isDoubleCheck" readonly="#{true}"/>
                    <Field name="minQty" readonly="#{true}"/>
                    <Field name="maxQty" readonly="#{true}"/>
                    <Field name="pricePlanEnable" readonly="#{true}"/>
                    <Field name="pricePlanBO" readonly="#{true}"/>
                </Fields>
            </TableForm>
        </Anchor>
    </Anchors>
</View>