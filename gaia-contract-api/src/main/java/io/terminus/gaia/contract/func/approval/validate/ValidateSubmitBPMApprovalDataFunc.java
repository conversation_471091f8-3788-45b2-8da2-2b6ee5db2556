package io.terminus.gaia.contract.func.approval.validate;

import io.terminus.gaia.contract.tmodel.bpm.SubmitApprovalFromBusinessDataTO;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * 前端页面提交审批时校验前台传参
 */
@Function(name = "validate submit bpm approval data function")
public interface ValidateSubmitBPMApprovalDataFunc {

    void execute (SubmitApprovalFromBusinessDataTO submitApprovalFromBusinessDataTO);
}
