<?xml version="1.0" encoding="UTF-8"?>
<View forModel="b2b_contract_RequestPurchaseBO" type="List" menuView="true" title="需求提报" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://trantor-docs-dev.app.terminus.io/static/v0.16.x/schema/base.xsd">
    <Table key="table" model="b2b_contract_RequestPurchaseBO" dataFunction="b2b_contract_PagingRequestPurchaseForPurFunc"
           dataParams="{status:{type:'Collection',values:['doing','finish']}}"
           fuzzySearchable="#{false}" sortableFields="createdAt" customDerivedData="false">
        <Search>
            <Fields>
                <Field name="code"/>
                <Field name="projectBO" label="项目名称"/>
                <Field name="name"/>
                <Field name="categoryBO">
                    <RenderType>
                        <CascadeModelSelect isLeafField="isLeaf" depthLimit="2" parentField="parentCategory"
                                            columnTitles="#{['一级分类', '二级分类', '三级分类', '四级分类']}"
                                            dataSource="md_CategoryBO_CategoryTreeProvider"
                                            model="md_CategoryBO" valueField="id" linkSelectMode="true"
                                            labelField="categoryName" modalWidth="#{800}"
                                            modalTitle="选择分类" searchInputWidth="#{400}"/>
                    </RenderType>
                </Field>
                <Field name="status">
                    <RenderType>
                        <Select allowValues="#{values => values.filter(item => ['match_making','virtual'].includes(item))}"/>
                    </RenderType>
                </Field>
            </Fields>
        </Search>
        <Fields>
            <Field name="code" fixed="left">
                <RenderType>
                    <Action targetView="b2b_contract_AgreementBO_AgreementInfo" />
                </RenderType>
            </Field>
            <Field name="name"/>
            <Field name="categoryBO.categoryName" label="分类"/>
            <Field name="projectBO.siteName" label="项目名称"/>
            <Field name="departmentBO.departmentName" label="项目所属组织"/>
            <Field name="status"/>
            <Field name="createdBy" label="创建人"/>
            <Field name="createdAt" label="创建时间"/>
        </Fields>
        <RecordActions>
            <!--详情-->
            <Action label="detail" targetView="b2b_contract_RequestPurchaseBO_AdminRequestPurchaseDetail" show="#{this.record.status == 'finish'}"/>

            <!--需求处理-->
            <Action label="需求处理" targetView="b2b_contract_RequestPurchaseBO_AdminRequestPurchaseDetail" show="#{this.record.status == 'doing'}"/>


        </RecordActions>

    </Table>
</View>