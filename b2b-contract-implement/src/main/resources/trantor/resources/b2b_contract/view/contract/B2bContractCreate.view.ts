import {Controller, utils} from 'nusi-sdk'
const {triggerLogicFlow, triggerLogicFunction} = utils

export default class extends Controller {
    listener;

    constructor() {
        super();
    }

    validator = async (data) => {
        try {
            let mainRes = await this.getContainerByKey('contract_Contract').validateData()
        } catch (e) {
            console.log(e);
            return false;
        }
        return true;
    }

    //校验有效期开始日期
    checkStartAt = (rule, value, callback, record) => {
        const endAt = record.endAt;
        if (!_.isUndefined(value) && !_.isUndefined(endAt)) {
            if (value >= endAt) {
                callback("生效日期不得大于失效日期");
            }
        }
        callback();
    }

    //校验有效期结束日期
    checkEndAt = (rule, value, callback, record) => {
        const startAt = record.startAt;
        if (!_.isUndefined(value) && !_.isUndefined(startAt)) {
            if (value <= startAt) {
                callback("失效日期不得小于生效日期");
            }
        }
        callback();
    }

    validateContractName = async (data: any) => {
        if (_.isUndefined(data.data.contractName) || data.data.contractName == '') {
            return "请填写合同名称";
        }
        return true;
    }

    onFieldValueChange = async ({value, fieldName}) => {
        //合同乙方
        if (fieldName === 'partyB') {
            this.loadDistributor()
        }
        // //合同甲方
        // if (fieldName === 'partyA') {
        //     this.loadDepartment();
        // }
    }

    //签约供应商
    loadDistributor = () => {
        const entityB = this.getContainerByKey('contract_Contract').data.partyB;
        if (entityB != null) {
            const container = this.getContainerByKey('contract_Distributor')
            container.updateData([{
                performanceEntity: entityB,
            }]);
        }
    }

    // //适用范围
    // loadDepartment = () => {
    //     const entityA = this.getContainerByKey('contract_Contract').data.partyA;
    //     this.triggerLogicFunction('contract_DefaultServiceAreaDataSource', entityA).then((result) => {
    //         const container = this.getContainerByKey('contract_Contract')
    //         container.updateData({
    //             serviceAreaList: result
    //         })
    //     })
    // }
}