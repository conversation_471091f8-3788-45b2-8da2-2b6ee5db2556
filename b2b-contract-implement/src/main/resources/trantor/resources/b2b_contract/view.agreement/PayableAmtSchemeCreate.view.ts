import {Controller} from 'nusi-sdk'

export default class extends Controller {

    // 回到上一个页面
    goBackWithRecord = async ({record}) => {
        console.log("goBackWithRecord ", record)
        this.goBack({
            withPayload: true,
            payload: record
        })
    }

    // 校验方案
    validateScheme = ({data}) => {
        // console.log("@@@@@@data", data)
        // 校验节点+起来=100

        if(this.env.type !== 'self') {
            let percent = data.schemeNodes.map((detail) => detail.payableRatio).reduce((prev, curr) => prev + curr, 0);
            if (percent != 100) {
               return "应收比例合计不为100%，请调整后确认！";
            }
        } else {
            const typeMap = data.schemeNodes.reduce((acc, node) => {
                if (!acc[node.payNodeType]) {
                    acc[node.payNodeType] = [];
                }
                acc[node.payNodeType].push(node);
                return acc;
            }, {});

            let progressRatio = null, completeRatio = null, settlementRatio = null;
            for (const key in typeMap) {
                if (typeMap.hasOwnProperty(key)) {
                    const nodes = typeMap[key];
                    if (nodes.length > 1 && key !== 'progress') {
                        return '应付账期【' + translateNodeDict(key) + '】仅能配置一项';
                    }

                    const sum = nodes.reduce((acc, node) => acc + node.payableRatio, 0);
                    if (key === 'guarantee' && sum > 5)
                        return '应付账期【质保金】应付比例不能超过5%';
                    if (sum > 100)
                        return '应付账期【' + translateNodeDict(key) + '】应付比例合计不能超过100%';

                    if (key === 'progress') progressRatio = sum;
                    if (key === 'complete') completeRatio = sum;
                    if (key === 'settlement') settlementRatio = sum;
                }
            }

            if (settlementRatio !== null) {
                if (completeRatio !== null && settlementRatio <= completeRatio) {
                    return '结算款比例必须大于完工款比例';
                }
                if (progressRatio !== null && settlementRatio <= progressRatio) {
                    return '结算款比例必须大于进度款比例';
                }
            }

            if (completeRatio !== null) {
                if (progressRatio !== null && completeRatio <= progressRatio) {
                    return '完工款比例必须大于进度款比例';
                }
            }
        }
        return true;
    }

    PayableAmtSchemesNodesChange = ({fieldName, value, index, record})=>{
        const schemeContainer = this.getContainerByKey('PayableAmtSchemesNodes')

        let title = schemeContainer.data.map(item => (this.env.type !== 'self' ? '' : `${item?.payNodeType ? translateNodeDict(item?.payNodeType) : ' '}`) + `${item?.payableRatio ? item?.payableRatio : '-'}` + '%').join(" | ");
        let content = schemeContainer.data.map(item => "对账确认后"+  `${item?.payablePeriod ?? '-'}` + translatePeriodDict(item.periodType)).join(" | ");

        const payableAmtSchemesContainer = this.getContainerByKey('PayableAmtSchemes')
        payableAmtSchemesContainer.updateData({...payableAmtSchemesContainer.data, title, content})
    }

    function translatePeriodDict(nodeDict) {
        switch (nodeDict) {
            case 'DAY':
                return '天'
            case 'MONTH':
                return '月'

            default:
                return "";
        }
    }

    function translateNodeDict(nodeDict) {
        switch (nodeDict) {
            case 'progress':
                return '进度款'
            case 'complete':
                return '完工款'
            case 'settlement':
                return '结算款'
            case 'guarantee':
                return '质保金'

            default:
                return "";
        }
    }

}