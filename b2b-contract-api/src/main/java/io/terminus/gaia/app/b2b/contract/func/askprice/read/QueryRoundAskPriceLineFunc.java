package io.terminus.gaia.app.b2b.contract.func.askprice.read;

import io.terminus.gaia.app.b2b.contract.model.AskPriceLineBO;
import io.terminus.gaia.app.b2b.contract.model.query.QAskPriceLineBO;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * 根据轮次和询价单id，查询询价单明细和供应商报价单价
 *
 * @author: huangjunwei
 **/
@Function
public interface QueryRoundAskPriceLineFunc {

    Paging<AskPriceLineBO> execute(QAskPriceLineBO req);
}
