package io.terminus.gaia.contract.func.sign.read;

import io.terminus.gaia.contract.model.sign.SignatoriesBO;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * @program: gaia-contract
 * @description: 根据签署任务和签署顺序查出签署人
 * @author: 彭书强(檀凡)
 * @created: 2021/04/28 11:50
 */
@Function(name = "Query signer")
public interface QuerySignatoriesFunc {

    /**
     * 根据签署任务和签署顺序查出签署人
     * @param signatoriesBO
     * @return
     */
    SignatoriesBO execute(SignatoriesBO signatoriesBO);
}
