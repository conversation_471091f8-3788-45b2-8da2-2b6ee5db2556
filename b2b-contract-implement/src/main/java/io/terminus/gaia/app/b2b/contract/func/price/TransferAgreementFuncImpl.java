package io.terminus.gaia.app.b2b.contract.func.price;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import io.terminus.gaia.app.b2b.contract.model.AgreementBO;
import io.terminus.gaia.app.b2b.contract.model.AgreementDetailBO;
import io.terminus.gaia.app.b2b.contract.tmodel.AgreementFloatPriceTO;
import io.terminus.gaia.app.b2b.contract.tmodel.SpuAgreementTO;
import io.terminus.gaia.app.b2b.contract.tmodel.sync.AgreementPriceLine;
import io.terminus.gaia.organization.model.DepartmentBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @date 2023-06-02
 * @descrition
 */
@Slf4j
@FunctionImpl
public class TransferAgreementFuncImpl implements TransferAgreementFunc {

    @Override
    public AgreementFloatPriceTO execute(SpuAgreementTO toList) {

        log.info("TransferAgreementFunc request: {}", JSON.toJSONString(toList));

        if (CollUtil.isEmpty(toList.getAgreementDetailList())) {
            return new AgreementFloatPriceTO();
        }

        List<Long> detailIds = toList.getAgreementDetailList().stream().map(AgreementDetailBO::getId).collect(toList());

        List<AgreementDetailBO> allDetails = DS.findAll(AgreementDetailBO.class, "*, agreementBO.*, agreementBO.yfCompanyBO.*", "id in (?)", detailIds);

        // 按协议分组，一个协议为一行
        Map<AgreementBO, List<AgreementDetailBO>> map = allDetails.stream().collect(Collectors.groupingBy(AgreementDetailBO::getAgreementBO));

        AgreementFloatPriceTO priceTO = new AgreementFloatPriceTO();

        List<AgreementPriceLine> lines = new ArrayList<>(map.size());

        map.forEach((k,v) -> {
            AgreementPriceLine priceLine = new AgreementPriceLine();
            k.setDetails(v);
            priceLine.setAgreementBO(k);
            lines.add(priceLine);
        });

        priceTO.setAgreementPriceLines(lines);

        priceTO.setSupplier(DS.findOne(DepartmentBO.class, "*", "departmentName = ?", "中建一局"));

        return priceTO;
    }
}
