package io.terminus.gaia.app.b2b.contract.tmodel.price;

import io.terminus.gaia.app.b2b.contract.model.PayableSchemeTemplateBO;
import io.terminus.gaia.app.b2b.contract.model.price.SalePriceCalLineBO;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.FieldType;
import io.terminus.trantorframework.api.annotation.TransientModel;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/3/8 17:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TransientModel
public class SalePriceCalLineWrapTO extends BaseModel<Long> {

    @Field(name = "销售价格计算行", type = FieldType.Json)
    private List<SalePriceCalLineBO> salePriceCalLineList;

    @Field(name = "账期", type = FieldType.Json)
    private PayableSchemeTemplateBO payableSchemeTemplateBO;

    @Field(name = "展示基准价", defaultValue = "false")
    private Boolean showBasicPrice;
}
