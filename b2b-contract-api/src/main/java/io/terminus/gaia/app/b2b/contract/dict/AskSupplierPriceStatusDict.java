package io.terminus.gaia.app.b2b.contract.dict;

import io.terminus.trantorframework.api.annotation.Dictionary;
import io.terminus.trantorframework.api.annotation.DictionaryItem;
import io.terminus.trantorframework.api.annotation.typemeta.Icon;

/**
 * 运营端-供应商报价单状态
 * <AUTHOR>
 */
@Dictionary
public interface AskSupplierPriceStatusDict {

    @DictionaryItem(value = "待报价", icon = Icon.dot, iconColor = Icon.Color.Yellow, displayOrder = 1)
    String PRICE_DOING = "price_doing";

    @DictionaryItem(value = "已报价", icon = Icon.dot, iconColor = Icon.Color.Orange, displayOrder = 2)
    String DONE = "done";

    @DictionaryItem(value = "已驳回", icon = Icon.dot, iconColor = Icon.Color.Red, displayOrder = 5)
    String PRICE_REJECT = "price_reject";

    @DictionaryItem(value = "中标", icon = Icon.dot, iconColor = Icon.Color.Black, displayOrder = 6)
    String WIN = "win";

    @DictionaryItem(value = "已作废", icon = Icon.dot, iconColor = Icon.Color.Purple, displayOrder = 7)
    String INVALID = "invalid";
}
