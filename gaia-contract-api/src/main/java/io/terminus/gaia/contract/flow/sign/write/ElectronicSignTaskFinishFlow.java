package io.terminus.gaia.contract.flow.sign.write;

import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.gaia.contract.tmodel.sign.ElectronicSignResultTO;
import io.terminus.trantorframework.api.annotation.Flow;

/**
 * 电子签署成功flow
 *
 * <AUTHOR>
 */
@Flow(name = "finish electronic sign task finish flow")
public interface ElectronicSignTaskFinishFlow {

    void execute (ContractBO contractBO);
}
