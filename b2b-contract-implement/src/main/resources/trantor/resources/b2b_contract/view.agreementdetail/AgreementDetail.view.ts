import {Controller, showMessage, utils} from 'nusi-sdk'

const {triggerLogicFlow, triggerLogicFunction} = utils

export default class extends Controller {
    listener;

    constructor() {
        super();
    }

    onDataLoaded = (data) => {
        var container = this.getContainerByKey('agreementDetail');
        let a = data?.spuBO?.rawMaterialContent * data?.copperBasicPrice / 1000 ;
        // 将yanmiCopperPrice赋值到data对象中
        data.yanmiCopperPrice = parseFloat(a?.toFixed(2));
        container.updateData(data);
    }

    // 映射标品
    mapSpu = async ({record}) => {
        this.openView('item_SpuBO_SingleSelectAgreementSpu', {
            env: {agreementId: record.agreementBO.id , thingMaterialId:record.thingMaterialId},
            openViewType: 'Dialog',
            payloadCallback: async (context) => {
                const container = this.getContainerByKey('agreementDetail')
                console.log('payloadCallback ', context)
                const selectSpu = context.record[0]
                try {
                    record.spuBO = selectSpu
                    let request = record;
                    let result = await this.triggerLogicFunction('b2b_contract_AgreementAndSpuMappingFunc', [request]);
                    showMessage({
                        level: "Weak",
                        message: "操作成功",
                        type: "Success"
                    })
                    container.refresh()
                    console.log('b2b_contract_AgreementAndSpuMappingFunc result, ', result)
                } catch (ex) {
                    console.log('b2b_contract_AgreementAndSpuMappingFunc 调用失败', ex.message);
                    showMessage({
                        level: "Weak",
                        message: ex.message,
                        type: "Error"
                    })
                }
            }
        })
    }

    // 设置价格方案
    setPriceScheme = async ({record}) => {
        if (record?.agreementBO?.type==='match_making'){
            this.openView('b2b_contract_PaymentRelatePriceSchemeCTO_SelectPaymentScheme', {
                openViewType: 'Dialog',
                record:record
            })
        } else {
            this.openView('item_PriceSchemeBO_SingleSelectAgreementPriceScheme', {
                env: {
                    spuId: record.spuBO.id,
                    agreementId: record.agreementBO.id
                },
                openViewType: 'Dialog',
                payloadCallback: async (context) => {
                    const container = this.getContainerByKey('agreementDetail')
                    console.log('payloadCallback ', context)
                    const selectPriceScheme = context.record[0]
                    try {
                        record.priceSchemeBO = selectPriceScheme
                        let request = record;
                        let result = await this.triggerLogicFunction('b2b_contract_AgreementAndPriceMappingFunc', [request]);
                        showMessage({
                            level: "Weak",
                            message: "操作成功",
                            type: "Success"
                        })
                        container.refresh()
                        console.log('b2b_contract_AgreementAndPriceMappingFunc result, ', result)
                    } catch (ex) {
                        console.log('b2b_contract_AgreementAndPriceMappingFunc 调用失败', ex.message);
                        showMessage({
                            level: "Weak",
                            message: ex.message,
                            type: "Error"
                        })
                    }
                }
            })
        }
    }

    viewPriceScheme = async ({record})=>{
        if (record?.agreementBO?.type==='match_making'){
            this.openView('b2b_contract_PaymentRelatePriceSchemeBO_SelectPaymentSchemeDetail', {
                openViewType: 'Dialog',
                record:record
            })
        }else {
            if (!record?.priceSchemeBO?.id){
                showMessage({
                    level: "Weak",
                    message: "未设置价格方案",
                    type: "Error"
                })
            }else {
                this.openView('item_PriceSchemeBO_PriceSchemeDetail', {
                    openViewType: 'Blank',
                    record:{id:record.priceSchemeBO.id}
                })
            }
        }
    }

    public formatAttributes(value) {
        return value?.map(attr => {
            const {attributeName, attributeValueType, attributeValueTO, unitName} = attr;
            let attrValue;
            if (attributeValueType === 'TEXT') {
                attrValue = attributeValueTO?.textValue;
            }
            if (attributeValueType === 'NUMBER') {
                attrValue = attributeValueTO?.numberValue;
            }
            if (attrValue === undefined) {
                attrValue = '无'
            }
            return `${attributeName}: ${attrValue}${unitName || ''}`
        }).join(' | ');
    }

}
