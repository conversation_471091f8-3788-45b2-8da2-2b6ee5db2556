package io.terminus.gaia.app.b2b.contract.model;

import io.terminus.gaia.app.b2b.contract.tmodel.AgreementBrandTO;
import io.terminus.gaia.md.model.CategoryBO;
import io.terminus.gaia.organization.model.DepartmentBO;
import io.terminus.gaia.organization.model.ext.ProjectBO;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.TransientModel;
import io.terminus.trantorframework.api.annotation.typemeta.LinkMeta;
import io.terminus.trantorframework.api.annotation.typemeta.LookupMeta;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 创建：询价单、供应商报价单
 *
 * <AUTHOR>
 */
@Data
@TransientModel(name = "创建询价单、供应商报价单")
public class AskPriceTO extends BaseModel<Long> {
    @Field(name = "需求池", desc = "需求池")
    @LinkMeta
    private RequestPurchaseBO requestPurchaseBO;

    @Field(name = "分类", desc = "分类id")
    @LinkMeta
    private CategoryBO categoryBO;

    @Field(name = "项目", desc = "项目id")
    @LinkMeta
    private ProjectBO projectBO;

    /**
     * 项目所属部门，例如xxxxx项目部
     */
    @Field(name = "项目所属部门", desc = "项目所属部门")
    @LinkMeta
    private DepartmentBO departmentBO;

    /**
     * 项目所属子企业单位名称，例如中建一局华南公司、中建一局二公司
     */
    @Field(name = "项目所属子企业单位名称")
    private String enterpriseName;

    /**
     * 来源：采购方提的需求->需求池->询价
     */
    @Field(name = "铜基价（含税）", desc = "铜基价（含税）")
    private BigDecimal basePriceWithTax;

    @Field(name = "协议", desc = "协议")
    @LinkMeta
    private AgreementBO agreementBO;

    @Field(name = "需求品牌/供应商/要询价的需求池清单行", desc = "需求品牌/供应商/要询价的需求池清单行")
    @LinkMeta
    private List<AgreementBrandTO> agreementBrandTOList;

    @Field(name = "当前询价单的报价轮次", desc = "当前询价单的报价轮次")
    private Integer round;

//    /**
//     * 询价单-供应商报价信息（供应商报价合价、供应商信息、协议信息、第n轮次询价报价、供应商询价报价状态）-（供应商询价报价明细清单）
//     * */
//    @Field(name = "供应商报价单", desc = "供应商报价单")
//    @LookupMeta(linkField = AskSupplierPriceBO.id_field)
//    private List<AskSupplierPriceBO> askSupplierPriceBOList;

//    @Field(name = "备注")
//    private String remark;

}
