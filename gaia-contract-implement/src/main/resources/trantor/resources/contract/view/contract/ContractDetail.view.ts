import { Controller, utils, Toast } from 'nusi-sdk'

const { triggerServerAction, triggerViewAction } = utils

export default class ContractDynamicTemplate extends Controller {

    termsThingsShow = async (data : any) => {
        console.log(data);
        return true;
    }

    onValidateTerminating = async (data : any) => {
        let rs = await triggerServerAction('contract_center_admin_PurchaserContractListVO_PurchaseContractCreateAction::verifyTerminatingContract', {
            record: data.data
        })

        if (!rs.data) {
            return "当前有合同变更在处理中，请勿重复变更！"
        }
        return rs.data
    }


    onValidateChanging = async (data : any) => {

        let rs = await triggerServerAction('contract_center_admin_PurchaserContractListVO_PurchaseContractCreateAction::verifyChangingContract', {
            record: data.data
        })

        if (!rs.data) {
            return "当前有合同变更在处理中，请勿重复变更！"
        }
        return rs.data
    }

}