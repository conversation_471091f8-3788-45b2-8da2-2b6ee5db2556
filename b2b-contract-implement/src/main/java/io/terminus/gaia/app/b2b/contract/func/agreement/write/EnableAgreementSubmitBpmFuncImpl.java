package io.terminus.gaia.app.b2b.contract.func.agreement.write;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import io.terminus.gaia.app.b2b.contract.dict.AgreementCoverAreaTypeDict;
import io.terminus.gaia.app.b2b.contract.dict.AgreementStatusDict;
import io.terminus.gaia.app.b2b.contract.dict.AgreementTypeDict;
import io.terminus.gaia.app.b2b.contract.func.agreement.read.PagingWorkspaceAgreementDetailFunc;
import io.terminus.gaia.app.b2b.contract.model.AgreementBO;
import io.terminus.gaia.app.b2b.contract.model.AgreementBpmBO;
import io.terminus.gaia.app.b2b.contract.model.AgreementCoverAreaBO;
import io.terminus.gaia.app.b2b.contract.model.AgreementDetailBO;
import io.terminus.gaia.app.b2b.contract.model.query.QAgreementBO;
import io.terminus.gaia.app.b2b.contract.model.query.QAgreementDetailBO;
import io.terminus.gaia.app.b2b.contract.spring.validator.AgreementValidator;
import io.terminus.gaia.app.b2b.item.util.B2BOSSFileHelper;
import io.terminus.gaia.app.b2b.contract.tmodel.PayableSchemeTO;
import io.terminus.gaia.common.open.bpm.client.BpmClient;
import io.terminus.gaia.common.utils.EnhanceDS;
import io.terminus.gaia.contract.dict.common.ApproveStatusDict;
import io.terminus.gaia.md.dict.SubmitZJBPMCodeDict;
import io.terminus.gaia.md.model.BrandBO;
import io.terminus.gaia.md.tmodel.request.BpmRequestTO;
import io.terminus.gaia.md.tmodel.response.BpmResponseTO;
import io.terminus.gaia.organization.context.UserCompanyContext;
import io.terminus.gaia.organization.context.UserInfoContext;
import io.terminus.gaia.organization.model.DepartmentBO;
import io.terminus.gaia.organization.setting.DevMockSetting;
import io.terminus.gaia.settlement.dict.bpm.ZhiYuanInvoiceEnum;
import io.terminus.gaia.settlement.dict.bpm.ZhiYuanPriceCalRuleEnum;
import io.terminus.gaia.settlement.dict.bpm.ZhiYuanYesEnum;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.Order;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.api.type.Attachment;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.json.Json;
import io.terminus.trantorframework.querymodel.QParams;
import io.terminus.trantorframework.querymodel.Select;
import io.terminus.trantorframework.querymodel.type.support.QLongId;
import io.terminus.trantorframework.querymodel.type.support.QString;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class EnableAgreementSubmitBpmFuncImpl implements EnableAgreementSubmitBpmFunc {
    private final AgreementValidator agreementValidator;
    private final UserCompanyContext userCompanyContext;
    private final UserInfoContext userInfoContext;
    private final DevMockSetting devMockSetting;

    /**
     * 致远bpm上传文件接口
     */
    @Value("${outSystem.seeYonUpdateFileUrl:https://oa.cscec1b.net/seeyon/rest/attachment}")
    private final String seeYonUpdateFileUrl = "https://oa.cscec1b.net/seeyon/rest/attachment";

    /**
     * 致远bpm获取tokenUrl
     */
    @Value("${outSystem.seeYonTokenUrl:https://oa.cscec1b.net/seeyon/rest/token}")
    private final String seeYonTokenUrl = "https://oa.cscec1b.net/seeyon/rest/token";

    private final B2BOSSFileHelper b2BOSSFileHelper;
    private final PagingWorkspaceAgreementDetailFunc pagingWorkspaceAgreementDetailFunc;

    @Override
    public BooleanResult execute(AgreementBO agreementBO) {
        AgreementBO existAgreement = EnhanceDS.safeFindOne(AgreementBO.class,
                "*,category.*,coverAreas.*,coverAreas.region.districtName,coverAreas.districtStr,paymentSchemes.*,yfCompanyBO.*,jfCompanyBO.*,taxRateBO.*, agreementBpmBO.*",
                "id = ?", agreementBO.getId());
        agreementValidator.checkEnable(agreementBO, existAgreement);
        Map<String, Object> data = new HashMap<>();
        //主要信息表
        Map<String, Object> formmain_4835 = this.buildFormMain4835(existAgreement);
        //覆盖范围明细表
        List<Map<String, Object>> formson_5325 = this.buildFormSon5325(existAgreement);
        //附件明细表
        List formson_4836 = this.buildFormSon4836(existAgreement);
        if(Boolean.TRUE.equals(devMockSetting.getCommonDebugMock())) {
            data.put("formmain_7200", formmain_4835);
            data.put("formson_7202", formson_5325);//覆盖范围明细表
            data.put("formson_7201", formson_4836);//附件明细表
        } else {
            data.put("formmain_4835", formmain_4835);
            data.put("formson_5325", formson_5325);//覆盖范围明细表
            data.put("formson_4836", formson_4836);//附件明细表
        }

        List<Long> attachmentsFileIds = new ArrayList<>();
        if (existAgreement.getAttachment() != null && CollUtil.isNotEmpty(existAgreement.getAttachment().getFiles())) {
            List<Attachment.File> files = existAgreement.getAttachment().getFiles();
            for (Attachment.File attachmentFile:files) {
                updateFileToSeeYon(attachmentFile,attachmentsFileIds);
            }
        }

        List formson_8582 = this.buildFormson8582(existAgreement);
        data.put("formson_8582", formson_8582);

        BpmRequestTO bpmRequestTO = new BpmRequestTO();
        bpmRequestTO.setData(data);
        String templateCode = this.buildTemplateCode(existAgreement);
        bpmRequestTO.setTemplateCode(templateCode);
        //draft: 发送0，待发1
        bpmRequestTO.setDraft("0");
        if(Boolean.TRUE.equals(devMockSetting.getCommonDebugMock())) {
            bpmRequestTO.setLoginName(devMockSetting.getApprovalLauncher());
        } else {
            bpmRequestTO.setLoginName(userInfoContext.getUserInfo().getUser().getUsername());
        }
        if (existAgreement.getAgreementBpmBO() != null && StringUtils.isNotEmpty(existAgreement.getAgreementBpmBO().getBpmSubjectTitle())) {
            bpmRequestTO.setSubject(existAgreement.getAgreementBpmBO().getBpmSubjectTitle());
        } else {
            String subjectTitle = StrUtil.format("商城自营采购合同审批单/ 乙方（{}）", existAgreement.getYfCompanyBO().getEntityName()) +
                    StrUtil.format("/合同编号（{}）", existAgreement.getCode());
            bpmRequestTO.setSubject(subjectTitle);
        }

        bpmRequestTO.setAttachments(attachmentsFileIds);

        //提交审批
        log.info("SubmitEnableAgreementApplyFuncImpl:BpmClient.param:{}", JSON.toJSON(bpmRequestTO));
        BpmResponseTO call = BpmClient.call(bpmRequestTO);
        log.error("bpm返回信息:{}", Json.toJson(call));
        if (!call.getIsSuccess()) {
            throw new BusinessException("发起审批异常:" + call.getMessage());
        }
        String result = call.getAppBussinessData();
        JSONObject jsonObj = JSONUtil.parseObj(result);
        //更新框架协议状态
        if (jsonObj.containsKey("summaryId") && StringUtils.isNotBlank(jsonObj.getStr("summaryId"))) {
            AgreementBO updateAgreementBo = new AgreementBO();
            updateAgreementBo.setId(agreementBO.getId());
            updateAgreementBo.setSummaryId(jsonObj.getStr("summaryId"));
            if(Boolean.TRUE.equals(devMockSetting.getCommonDebugMock())) {
                updateAgreementBo.setSummaryName(devMockSetting.getApprovalLauncher());
            } else {
                updateAgreementBo.setSummaryName(userInfoContext.getUserInfo().getUser().getUsername());
            }
            updateAgreementBo.setApproveStatusDict(ApproveStatusDict.APPROVING);
            updateAgreementBo.setStatus(AgreementStatusDict.ENABLED_AUDIT);
            updateAgreementBo.setStatusBefore(existAgreement.getStatus());
            updateAgreementBo.setOperator(userInfoContext.getUserInfo().getEmployee());
            DS.update(updateAgreementBo);
            return BooleanResult.TRUE;
        }
        return BooleanResult.FALSE;
    }

    private void updateFileToSeeYon(Attachment.File attachmentFile,List<Long> attachmentsFileIds) {
        /*File file = new File("https:"+attachmentFile.getUrl());

        String  fileUrl = uploadFile(seeYonUpdateFileUrl,file);*/
        String  fileUrl = uploadFile(seeYonUpdateFileUrl,attachmentFile);
        if(StringUtils.isNotBlank(fileUrl)){
            attachmentsFileIds.add(new Long(fileUrl));
        }
    }

    private String uploadFile(String seeYonUpdateFileUrl, Attachment.File attachmentFile) {
        String  fileUrl = "";
        try{
            RestTemplate restTemplate = new RestTemplate();
            //设置请求头
            HttpHeaders headers = new HttpHeaders();
            MediaType type = org.springframework.http.MediaType.parseMediaType("multipart/form-data");
            headers.setContentType(type);
            String token = getSeeyonToken();
            if(StringUtils.isBlank(token)){
                return fileUrl;
            }
            headers.set("token", token);

            //获取华为云流信息
            InputStream inputStream = b2BOSSFileHelper.loadFile(attachmentFile.getUrl());
            //将流转化为byte
            byte[] fileBytes = inputStreamToByteArray(inputStream);

            File tempFile = File.createTempFile("tempfile/", "."+attachmentFile.getType());
            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                fos.write(fileBytes);
            }
            FileSystemResource fileSystemResource = new FileSystemResource(tempFile);
            //设置请求体
            MultiValueMap<String, Object> form = new LinkedMultiValueMap<>();
            form.add("file", fileSystemResource);
            //用HttpEntity封装整个请求报文
            HttpEntity<MultiValueMap<String, Object>> files = new HttpEntity<>(form, headers);
            String c=  restTemplate.postForObject(seeYonUpdateFileUrl, files, String.class);
            // 删除临时文件
            tempFile.delete();

            System.out.println("c:"+c);
            Map map = JSON.parseObject(c.toString(), Map.class);
            System.out.println("map:"+map);
            List list =  (List) map.get("atts");
            System.out.println("list:"+list);
            Map<String,Object> sonMap = (Map<String, Object>) list.get(0);
            System.out.println("sonMap:"+sonMap);
            fileUrl = (String) sonMap.get("fileUrl");
            System.out.println(fileUrl);
        }catch(Exception exception){
            log.error("调用致远上传文件报错");
            exception.printStackTrace();
        }
        return fileUrl;
    }

    public byte[] inputStreamToByteArray(InputStream inputStream)  {
        try{
            ByteArrayOutputStream buffer = new ByteArrayOutputStream();

            int nRead;
            byte[] data = new byte[16384]; // 你可以根据需要调整这个缓冲区的大小

            while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, nRead);
            }

            buffer.flush();
            return buffer.toByteArray();
        }catch(Exception exception){
            log.error("调用致远上传文件报错");
            exception.printStackTrace();
        }
        return null;
    }

    private String getSeeyonToken() {
        String tokenId = "";
        // 构造请求体
        RestTemplate restTemplate = new RestTemplate();
        // 构造请求参数
        Map map = new HashMap();
        map.put("userName", "shangcheng");
        map.put("password", "0fae50c4-d48b-47a3-9a8f-364f1d0f4e33");
        map.put("loginName", userInfoContext.getUserInfo().getUser().getUsername());

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建HttpEntity
        HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(map), headers);

        // 发送POST请求
        ResponseEntity<String> response = restTemplate.postForEntity(seeYonTokenUrl, request, String.class);

        if (response.getStatusCode() == HttpStatus.OK) {
            // 假设响应体是一个JSON字符串，你需要解析它以获取token
            com.alibaba.fastjson.JSONObject responseJson = JSON.parseObject(response.getBody());
            // 假设token在响应体的"id"字段中
            tokenId = responseJson.getString("id"); // 根据实际响应结构调整

        } else {
            log.info("获取致远token失败");
        }
        return tokenId;
    }

    @NotNull
    private List buildFormSon4836(AgreementBO existAgreement) {
        List formson_4836 = new ArrayList<>();
        if (existAgreement.getAttachment() != null && CollUtil.isNotEmpty(existAgreement.getAttachment().getFiles())) {
            List<Attachment.File> files = existAgreement.getAttachment().getFiles();
            for (int i = 1; i <= files.size(); i++) {
                Map<String, Object> map = Maps.newHashMap();
                map.put("序号1", i);

                String fileValue = "";
                if (files.get(i - 1) != null) {
                    fileValue = fileValue + files.get(i - 1).getName()
                            + "（点链接下载：https:" + files.get(i - 1).getUrl() + "）";
                }
                map.put("附件名称", fileValue);//合同审批相关资料
                formson_4836.add(map);
            }
        }
        return formson_4836;
    }

    @NotNull
    private List<Map<String, Object>> buildFormSon5325(AgreementBO existAgreement) {
        List<Map<String, Object>> formson_5325 = new ArrayList<>();
        if (AgreementCoverAreaTypeDict.APPOINT.equals(existAgreement.getCoverAreaType()) &&
                !CollectionUtils.isEmpty(existAgreement.getCoverAreas())) {
            for (int i = 0; i < existAgreement.getCoverAreas().size(); i++) {
                AgreementCoverAreaBO agreementCoverAreaBO = existAgreement.getCoverAreas().get(i);

                Map<String, Object> map = Maps.newHashMap();
                map.put("序号1", i + 1);
                map.put("大区", agreementCoverAreaBO.getRegion().getDistrictName());
                map.put("省市区", agreementCoverAreaBO.getDistrictStr());
                formson_5325.add(map);
            }
        }
        return formson_5325;
    }

    @NotNull
    private Map<String, Object> buildFormMain4835(AgreementBO existAgreement) {
        AgreementBpmBO agreementBpmBO = existAgreement.getAgreementBpmBO();
        Map<String, Object> formmain_4835 = Maps.newHashMap();
        formmain_4835.put("项目名称", "/");
        formmain_4835.put("公司", "智采公司");
        Optional.ofNullable(userCompanyContext.getCompanyInfo().getOrgId()).ifPresent(orgId -> {
            DepartmentBO departmentBO = DS.findById(DepartmentBO.class, orgId);
            if (departmentBO != null && StringUtils.isNotEmpty(departmentBO.getPath())) {
                formmain_4835.put("经办部门", departmentBO.getPath());
            }
        });
        formmain_4835.put("经办人", userInfoContext.getUserInfo().getUser().getNickname());
        formmain_4835.put("经办人手机号", userInfoContext.getUserInfo().getUser().getMobile());
        formmain_4835.put("合同类型", "采购合同");
        if (StringUtils.isNotEmpty(existAgreement.getName())) {
            formmain_4835.put("合同名称", existAgreement.getName());
        }
        if (existAgreement.getJfCompanyBO() != null) {
            formmain_4835.put("甲方单位", existAgreement.getJfCompanyBO().getEntityName());
        }
        if (existAgreement.getYfCompanyBO() != null) {
            formmain_4835.put("乙方单位", existAgreement.getYfCompanyBO().getEntityName());
        }
        if (existAgreement.getCategory() != null) {
            formmain_4835.put("品类", existAgreement.getCategory().getPath());
        }
        if (StringUtils.isNotEmpty(existAgreement.getCode())) {
            formmain_4835.put("商城合同编号", existAgreement.getCode());
        }
        if (agreementBpmBO != null && StringUtils.isNotEmpty(agreementBpmBO.getInnerContractCode())) {
            formmain_4835.put("内部合同编号", agreementBpmBO.getInnerContractCode());
        }
        if (StringUtils.isNotEmpty(existAgreement.getName())) {
            formmain_4835.put("合同周期", DateUtil.format(existAgreement.getEffectiveAt(), DatePattern.NORM_DATE_PATTERN) +
                    " 至 " + DateUtil.format(existAgreement.getExpireAt(), DatePattern.NORM_DATE_PATTERN));
        }
        if (agreementBpmBO != null && agreementBpmBO.getSupplementAgreement() != null && agreementBpmBO.getSupplementAgreement()) {
            formmain_4835.put("是否补充协议", ZhiYuanYesEnum.YES.getOaId());
            if (StringUtils.isNotEmpty(agreementBpmBO.getMainContractName())) {
                formmain_4835.put("原合同名称", agreementBpmBO.getMainContractName());
            }
            if (StringUtils.isNotEmpty(agreementBpmBO.getMainContractCode())) {
                formmain_4835.put("原合同编号", agreementBpmBO.getMainContractCode());
            }
        } else {
            formmain_4835.put("是否补充协议", ZhiYuanYesEnum.NO.getOaId());
            formmain_4835.put("原合同名称", "无");
            formmain_4835.put("原合同编号", "无");
        }

        if (agreementBpmBO != null){
            ZhiYuanPriceCalRuleEnum zhiYuanPriceCalRuleEnum = ZhiYuanPriceCalRuleEnum.getInstanceByLocalTag(agreementBpmBO.getPriceCalRule());
            if (zhiYuanPriceCalRuleEnum != null) {
                formmain_4835.put("计价规则", zhiYuanPriceCalRuleEnum.getOaId());
            }
            ZhiYuanInvoiceEnum zhiYuanInvoiceEnum = ZhiYuanInvoiceEnum.getInstanceByLocalTag(agreementBpmBO.getInvoiceTypeBpm());
            if (zhiYuanInvoiceEnum != null) {
                formmain_4835.put("票据类型", zhiYuanInvoiceEnum.getOaId());
            }
        }
        formmain_4835.put("签约金额-含税", existAgreement.getTaxAmt());
        formmain_4835.put("签约金额-不含税", existAgreement.getNoTaxAmt());
        if (existAgreement.getTaxRateBO() != null) {
            formmain_4835.put("税率", existAgreement.getTaxRateBO().getTaxRateShow());
        }
        formmain_4835.put("签约税额", existAgreement.getTaxPrc());
        if(agreementBpmBO != null){
            formmain_4835.put("保修比例",  agreementBpmBO.getMaintenanceRate() == null ? "0%" : agreementBpmBO.getMaintenanceRate() + "%");
            formmain_4835.put("履约保证金类型", agreementBpmBO.getPerformanceBondType());
            formmain_4835.put("付款方式", agreementBpmBO.getPayWay());
        }

        if (!CollectionUtils.isEmpty(existAgreement.getPaymentSchemes())) {
            formmain_4835.put("付款方案描述", existAgreement.getPaymentSchemes().get(0).getDescription());
        }
        // 自营用账期方案
        if(AgreementTypeDict.SELF.equals(existAgreement.getType()) && CollUtil.isNotEmpty(existAgreement.getPayableSchemeList())) {
            StringBuilder description = new StringBuilder();
            final PayableSchemeTO payableSchemeTO = existAgreement.getPayableSchemeList().get(0);
            description.append("节点：").append(payableSchemeTO.getTitle()).append("；\n")
                    .append("账期：").append(payableSchemeTO.getContent()).append("；\n")
                    .append("方案描述：").append(payableSchemeTO.getMark());
            formmain_4835.put("付款方案描述", description);
        }
        if (AgreementCoverAreaTypeDict.ALL.equals(existAgreement.getCoverAreaType())) {
            formmain_4835.put("覆盖范围", "全部");
        } else {
            //指定覆盖范围，按列表展示
            formmain_4835.put("覆盖范围", "指定地区");
        }
        return formmain_4835;
    }


    /**
     * formson_8582
     * @param existAgreement
     * @return
     */
    private List<Map<String, Object>> buildFormson8582(AgreementBO existAgreement) {
        QAgreementDetailBO qAgreementDetailBO = new QAgreementDetailBO();
        QAgreementBO qAgreementBO = new QAgreementBO();
        qAgreementBO.setId(new QLongId(existAgreement.getId()));
        qAgreementDetailBO.setAgreementBO(qAgreementBO);
        qAgreementDetailBO.setFromSite(new QString("agreementView"));
        QParams qParams = new QParams();
        qParams.setSelect(new Select());
        Select select = new Select();
        select.addField("id");
        select.addField("source");
        select.addField("agreementBO", new Select.Field("id"), new Select.Field("name"), new Select.Field("type"));
        select.addField("saleEntityBO", new Select.Field("id"), new Select.Field("entityName"));
        select.addField("syncInfo");
        select.addField("statusDict");
        select.addField("spuBO", new Select.Field("id"), new Select.Field("name"),
                new Select.Field("spuCode"), new Select.Field("thing"),
                new Select.Field("thingName"), new Select.Field("attributes"));
        select.addField("categoryBO", new Select.Field("id"), new Select.Field("categoryName"),
                new Select.Field("path"));
        select.addField("taxRate", new Select.Field("id"), new Select.Field("taxRate"), new Select.Field("taxRateShow"));
        select.addField("supplyInfo");
        select.addField("unit", new Select.Field("id"), new Select.Field("unitName"));
        Set<Select.Field> brandFields = new HashSet<>();
        brandFields.add(new Select.Field("id"));
        brandFields.add(new Select.Field("brandName"));
        Select.Field brandField = new Select.Field();
        brandField.setFields(brandFields);
        select.addField("brands", brandField);
        select.addField("priceSchemeBO", new Select.Field("id"), new Select.Field("name"));
        select.addField("outerCode");
        select.addField("createBy", new Select.Field("id"), new Select.Field("username"));
        select.addField("createdAt");
        Order order = new Order();
        order.setAsc(false);
        order.setField("updatedAt");
        order.setFields(new LinkedHashSet<String>(){
            {
                add("updatedAt");
                add("id");
            }
        });
        qParams.setOrder(order);
        qParams.setSelect(select);
        qAgreementDetailBO.setQueryParams(qParams);
        Paging<AgreementDetailBO> agreementDetailBOS = pagingWorkspaceAgreementDetailFunc.execute(qAgreementDetailBO);

        List<Map<String, Object>> formson_8582 = new ArrayList<>();
        int i = 1;
        for (AgreementDetailBO agreementDetailBO : agreementDetailBOS.getData()) {
            Map<String, Object> detailMap = Maps.newHashMap();
            detailMap.put("协议清单序号", i);
            detailMap.put("关联标品", agreementDetailBO.getSpuBO().getName());
            detailMap.put("分类", agreementDetailBO.getCategoryBO().getPath());
            detailMap.put("物料", agreementDetailBO.getThingMaterial());
            detailMap.put("属性", agreementDetailBO.getAttribute());
            detailMap.put("清单行税率", agreementDetailBO.getTaxRate().getTaxRateShow());
            detailMap.put("含税价", agreementDetailBO.getSupplyInfo().getTaxPrice());
            detailMap.put("不含税价", agreementDetailBO.getSupplyInfo().getPrice());
            if (agreementDetailBO.getBrands() != null && !agreementDetailBO.getBrands().isEmpty()) {
                detailMap.put("品牌", agreementDetailBO.getBrands().stream().map(BrandBO::getBrandName).collect(Collectors.joining(",")));
            }
            formson_8582.add(detailMap);
            i++;
        }
        return formson_8582;
    }

    @NotNull
    private String buildTemplateCode(AgreementBO existAgreement) {
        String templateCode = SubmitZJBPMCodeDict.AGREEMENT_CREATE;
        if(devMockSetting.getCommonDebugMock()) {
            templateCode = SubmitZJBPMCodeDict.AGREEMENT_CREATE + "_test";
        }
        String bpmSuffix = null;
        if (AgreementTypeDict.SELF.equals(existAgreement.getType())) {
            bpmSuffix = "zcgylgs";
        } else {
            //bpmSuffix = queryBpmTemplateCodeFunc.execute(userCompanyContext.getCompanyInfo()).getTemplateCodeSuffix();
            log.error("失败：当前仅支持自营采购协议提交审批到致远OA！{}", existAgreement.getCode());
            throw new BusinessException("失败：仅自营采购协议需提交审批，请联系商城运营人员！");
        }
        if (StringUtils.isBlank(bpmSuffix)) {
            log.error("失败：当前用户的组织审批编码为空！{}", userCompanyContext.getCompanyInfo().getOrgId());
            throw new BusinessException("失败：请联系商城运营人员配置组织审批编码！");
        }
        templateCode = templateCode + "_" + bpmSuffix;
        return templateCode;
    }
}
