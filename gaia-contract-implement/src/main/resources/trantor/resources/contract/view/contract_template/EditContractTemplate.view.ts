import { Controller, utils } from 'nusi-sdk'

const { triggerLogicFlow, triggerLogicFunction } = utils

export default class QuestionController extends Controller {
    editAndEnable = async (data : any) => {
              var result = await this.triggerLogicFlow("contract_EditContractTemplateFlow",data.context);
              var result = await this.triggerLogicFlow("contract_EnableContractTemplateFlow",data.context.contractTemplate);
    }
    submitValidate = async (data : any) => {
        if(data){
            data=data.data
            console.log(data)
            var m = new Map();
            var groupList = data.formGroupList
            var groupNameList = new Array()
            for(var i in groupList){
                var name = groupList[i].groupName
                console.log(groupNameList,name)
                if(groupNameList.indexOf(name)>-1){
                    return "分组名称不能重复";
                }else {
                    groupNameList.push(name)
                }
            }
            var lineList = data.formLineList
            var sortNumList = new Array()
            for(var i in lineList){
                var key = lineList[i].formGroup.groupName + lineList[i].sortNum
                console.log(sortNumList,key)
                if(sortNumList.indexOf(key)>-1){
                    return "同组内排序不可重复"
                }else {
                    sortNumList.push(key)
                }
            }

        }
        return true;

    }

    onFieldValueChange = async (any, fieldName, number) => {
              if(fieldName == 'dataSource'){
                if(any == 'MANUAL_INPUT'){
                    var formlineContainer = this.getContainerByKey('formLine');
                    var data = formlineContainer.data;
                    var arr = new Array();
                    for(var i in data){
                        var l = data[i]
                        if(i == number){
                           l.formLineDynamicData=null
                        }
                        arr[i] = l;
                    }
                    formlineContainer.updateData(arr);
                }
              }

          }

          groupChange = async (any,fieldName,number) => {
              if(fieldName == 'parent.groupName' ){
                  var formGroupContainer = this.getContainerByKey('formGroups');
                  var data = formGroupContainer.data;
                  for(var i in data){
                      var node = data[i];
                      var name = node.groupName;
                      if(any ==name ){
                          data[i].isLeaf =false;
                          formGroupContainer.updateData(data);
                      }
                  }
              }else if(fieldName == 'groupName' ){
                    var formGroupContainer = this.getContainerByKey('formGroups');
                    var data = formGroupContainer.data;
                    var groupId = data[number].id;
                    console.log(data[number],groupId)
                    var formlineContainer = this.getContainerByKey('formLine');
                    var formLineData = formlineContainer.data;
                    for(var i in formLineData){
                           var node = formLineData[i];
                           console.log('node',node)
                           if(groupId == node.formGroup.id){
                                formLineData[i].formGroup.groupName = any;
                                formlineContainer.updateData(formLineData);
                           }
                    }
              }
          }
}