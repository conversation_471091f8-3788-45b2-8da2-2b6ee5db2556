package io.terminus.gaia.app.b2b.contract.func.contract.read;

import io.terminus.gaia.contract.dict.contract.ContractStatusDict;
import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.gaia.contract.msg.contract.ContractExMsg;
import io.terminus.gaia.contract.utils.JsonUtil;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Author: fengbo
 * @Date: 2021/9/7
 */
@Slf4j
@RequiredArgsConstructor
@FunctionImpl
public class GetLastSupplementContractFuncImpl implements GetLastSupplementContractFunc {
    @Override
    public ContractBO execute(ContractBO contract) {
        //合同ID不为空
        if (contract == null || contract.getId() == null) {
            throw new BusinessException(ContractExMsg.CONTRACT_ID_IS_NULL);
        }
        log.info("Function GetLastSupplementContractFunc 查询入参 id {} ", contract.getId());
        //查询合同信息
        contract = DS.findById(ContractBO.class, contract.getId(), "*,categoryBOList.*,contractDistributorList.*,contractDistributorList.performanceEntity.*");
        //必须是非补充合同
        if (contract.getIsSupplementContract()) {
            throw new BusinessException(ContractExMsg.MUST_MASTER_CONTRACT);
        }
        List<ContractBO> supplementList
                = DS.findAll(ContractBO.class, "*,categoryBOList.*,contractDistributorList.*,contractDistributorList.performanceEntity.*", "parent=? and contractStatusDict=? order by createdAt desc ", contract.getId(), ContractStatusDict.SIGNED);
        if (CollectionUtils.isEmpty(supplementList)) {
//            log.info("Function GetLastSupplementContractFunc 返回的是主协议,协议数据:" + JsonUtil.getJsonExcludeRootFields(contract));
            return contract;
        }
        ContractBO contractBO = supplementList.get(0);
        contractBO.setContractDistributorList(contract.getContractDistributorList());
//        log.info("Function GetLastSupplementContractFunc 返回的是补充协议,协议数据:" + JsonUtil.getJsonExcludeRootFields(contractBO));
        return contractBO;
    }
}
