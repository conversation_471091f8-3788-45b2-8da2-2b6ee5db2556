package io.terminus.gaia.app.b2b.contract.spring.service;

import cn.hutool.core.collection.CollUtil;
import io.terminus.datastore.dsl.Query;
import io.terminus.datastore.dsl.impl.TSQL;
import io.terminus.gaia.app.b2b.contract.dict.AgreementDetailLoseReasonDict;
import io.terminus.gaia.app.b2b.contract.dict.AgreementDetailStatusDict;
import io.terminus.gaia.app.b2b.contract.dict.InventoryStatusDict;
import io.terminus.gaia.app.b2b.contract.func.agreement.AgreementUpdateTriggerFunc;
import io.terminus.gaia.app.b2b.contract.model.AgreementDetailBO;
import io.terminus.gaia.app.b2b.contract.model.PaymentRelatePriceSchemeBO;
import io.terminus.gaia.item.dict.OnShelfStatusDict;
import io.terminus.gaia.item.model.item.ItemBO;
import io.terminus.gaia.item.model.sku.SkuBO;
import io.terminus.gaia.item.model.spu.SpuBO;
import io.terminus.platform.sdk.transaction.DSTransaction;
import io.terminus.trantorframework.json.Json;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AgreementDetailService {

    private final AgreementUpdateTriggerFunc agreementUpdateTriggerFunc;

    /**
     * 创建清单
     *
     * @param details
     */
    public void createAgreementDetail(List<AgreementDetailBO> details) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }

        log.info("创建清单:{}", Json.toJson(details));
        DS.create(details);

        details.forEach(detail -> {
            if (AgreementDetailStatusDict.START_USING.equals(detail.getStatusDict()) &&
                    InventoryStatusDict.IN_STOCK.equals(detail.getInventoryStatus())) {
                // 启用 && 有货
                // 后续动作
            }
        });

        updateItemToTrigger(details);
    }

    /**
     * 失效清单
     *
     * @param details
     */
    @DSTransaction
    public void loseAgreementDetail(List<AgreementDetailBO> details) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }

        List<AgreementDetailBO> updates = details.stream().map(detail -> {
            AgreementDetailBO toUpdate = AgreementDetailBO.of(detail.getId());
            toUpdate.setStatusDict(AgreementDetailStatusDict.LOSE_EFFICACY);
            toUpdate.setLoseReason(StringUtils.defaultIfBlank(detail.getLoseReason(), AgreementDetailLoseReasonDict.MANUAL));
            return toUpdate;
        }).collect(Collectors.toList());

        log.info("失效清单:{}", Json.toJson(updates));
        DS.update(updates);

        updateItemToTrigger(updates);
    }

    /**
     * 启用清单
     *
     * @param details
     */
    @DSTransaction
    public void enableAgreementDetail(List<AgreementDetailBO> details) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }

        List<AgreementDetailBO> updates = details.stream().map(detail -> {
            AgreementDetailBO toUpdate = AgreementDetailBO.of(detail.getId());
            toUpdate.setStatusDict(AgreementDetailStatusDict.START_USING);
            toUpdate.setInventoryStatus(detail.getInventoryStatus());
            toUpdate.setLoseReason(detail.getLoseReason());
            return toUpdate;
        }).collect(Collectors.toList());
        log.info("启用清单:{}", Json.toJson(updates));
        DS.update(updates);

        updateItemToTrigger(updates);
    }

    /**
     * 禁用清单
     *
     * @param details
     */
    @DSTransaction
    public void disableAgreementDetail(List<AgreementDetailBO> details) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }

        List<AgreementDetailBO> updates = details.stream().map(detail -> {
            AgreementDetailBO toUpdate = AgreementDetailBO.of(detail.getId());
            toUpdate.setStatusDict(AgreementDetailStatusDict.STOP_USING);
            toUpdate.setInventoryStatus(detail.getInventoryStatus());
            toUpdate.setLoseReason(detail.getLoseReason());
            return toUpdate;
        }).collect(Collectors.toList());
        log.info("禁用清单:{}", Json.toJson(updates));
        DS.update(updates);

        updateItemToTrigger(updates);
    }

    /**
     * 失效 -》异常
     *
     * @param details
     */
    @DSTransaction
    public void unusualAgreementDetail(List<AgreementDetailBO> details) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }

        List<AgreementDetailBO> updates = details.stream().map(detail -> {
            AgreementDetailBO toUpdate = AgreementDetailBO.of(detail.getId());
            toUpdate.setStatusDict(AgreementDetailStatusDict.UNUSUAL);
            toUpdate.setInventoryStatus(detail.getInventoryStatus());
            toUpdate.setLoseReason(detail.getLoseReason());
            return toUpdate;
        }).collect(Collectors.toList());
        log.info("协议清单设为异常: {}", Json.toJson(updates));
        DS.update(updates);


        updateItemToTrigger(updates);
    }

    /**
     * 删除清单
     *
     * @param details
     */
    @DSTransaction
    public void deleteAgreementDetail(List<AgreementDetailBO> details) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }

        List<Long> itemIds = queryItemIds(details);
        log.info("删除清单:{}", Json.toJson(details));
        DS.delete(details);

        // 删除付款方案关联的价格方案
        List<Long> detailIdList = details.stream().map(AgreementDetailBO::getId).collect(Collectors.toList());
        Query query = TSQL.delete(PaymentRelatePriceSchemeBO.class).where(TSQL.field(PaymentRelatePriceSchemeBO.agreementDetail_field).in(detailIdList));
        DS.delete(query);


        updateItem(itemIds);

        updateAgreementComplete(details);
    }

    /**
     * 修改清单库存状态
     *
     * @param details
     */
    @DSTransaction
    public void setInventoryStatus(List<AgreementDetailBO> details, String inventoryStatus) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }

        List<AgreementDetailBO> updates = details.stream().map(detail -> {
            AgreementDetailBO toUpdate = AgreementDetailBO.of(detail.getId());
            toUpdate.setInventoryStatus(inventoryStatus);
            return toUpdate;
        }).collect(Collectors.toList());

        log.info("修改清单库存状态:{}", Json.toJson(updates));
        DS.update(updates);

        updateItemToTrigger(updates);
    }


    /**
     * 触发商品更新到es
     *
     * @return
     */
    private void updateItemToTrigger(List<AgreementDetailBO> agreementDetailBOS) {
        updateAgreementComplete(agreementDetailBOS);

        List<Long> itemIds = queryItemIds(agreementDetailBOS);
        updateItem(itemIds);
    }

    private void updateAgreementComplete(List<AgreementDetailBO> agreementDetailBOS) {
        CompletableFuture.runAsync(() -> {
            Set<Long> detailSet = agreementDetailBOS.stream().map(AgreementDetailBO::getId).collect(Collectors.toSet());
            List<AgreementDetailBO> agreementDetailBOList = DS.findAll(AgreementDetailBO.class, "id,agreementBO.id", "id in (?)", detailSet);

            for (AgreementDetailBO agreementDetailBO : agreementDetailBOList) {
                try {
                    agreementUpdateTriggerFunc.execute(agreementDetailBO.getAgreementBO());
                } catch (Exception e) {
                    log.error("更新是否维护完整失败，id={}", agreementDetailBO.getId());
                }
            }
        });
    }


    private List<Long> queryItemIds(List<AgreementDetailBO> agreementDetailBOS) {
        List<Long> agreementDetailIds = agreementDetailBOS.stream().map(AgreementDetailBO::getId).collect(Collectors.toList());
        if (CollUtil.isEmpty(agreementDetailIds)) {
            return Collections.emptyList();
        }

        List<AgreementDetailBO> agreementDetailBOSDb = DS.findAll(AgreementDetailBO.class, "*", "id in (?)", agreementDetailIds);
        Set<Long> spuIds = agreementDetailBOSDb.stream().map(AgreementDetailBO::getSpuBO).filter(Objects::nonNull).map(SpuBO::getId).collect(Collectors.toSet());

        if (CollUtil.isEmpty(spuIds)) {
            return Collections.emptyList();
        }

        List<SkuBO> skuBOList = DS.findAll(SkuBO.class, "*", "spu in (?)", spuIds);
        List<Long> itemIds = skuBOList.stream().map(SkuBO::getItem).filter(Objects::nonNull).map(ItemBO::getId).collect(Collectors.toList());

        return itemIds;
    }

    private void updateItem(List<Long> itemIds) {
        if (CollUtil.isEmpty(itemIds)) {
            return;
        }
        DS.update(ItemBO.class, "refreshDate=?", "id in (?) and onShelfStatusDict=?", new Date().getTime(), itemIds, OnShelfStatusDict.ON_SHELF);

    }
}
