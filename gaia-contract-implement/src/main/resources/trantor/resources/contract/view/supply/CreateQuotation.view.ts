import { Controller, utils, PubSub } from 'nusi-sdk'

import {openView} from utils;

export default class ContractEditView extends Controller {
    listener;

    constructor() {
        super();
        this.listener = PubSub.subscribe('price_PricePlanBO/createPricePlan', (data) => {
            console.log(data);
            let contractPriceListContainer = this.getContainerByKey('contract_priceList');
            let contractPriceListData = contractPriceListContainer.data;
            contractPriceListData[parseInt(data.line) - 1] =
                {   ...contractPriceListData[parseInt(data.line) - 1],
                    pricePlanBO: {
                        id: data.id
                    }
                };
            contractPriceListContainer.updateData(contractPriceListData);
        })
    }

    createPricePlan = ({context}) => {
        //
        console.log(context);
        this.openView('price_PricePlanTemplateBO_ContractPricePlanCreateTemplateChooseList', {
            openViewType: "Dialog",
            env: {
                line: context._TEMPID
            }
        })
    }

}