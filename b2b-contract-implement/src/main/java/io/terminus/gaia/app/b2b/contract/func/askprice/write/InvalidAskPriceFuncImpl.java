package io.terminus.gaia.app.b2b.contract.func.askprice.write;

import io.terminus.gaia.app.b2b.contract.dict.AskPriceStatusDict;
import io.terminus.gaia.app.b2b.contract.dict.AskSupplierPriceStatusDict;
import io.terminus.gaia.app.b2b.contract.model.AskPriceBO;
import io.terminus.gaia.app.b2b.contract.model.AskPriceLineBO;
import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceBO;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 作废询价单
 *
 * <AUTHOR>
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class InvalidAskPriceFuncImpl implements InvalidAskPriceFunc {
    @Override
    public void execute(AskPriceBO askPriceBO) {
        if (askPriceBO == null || askPriceBO.getId() == null) {
            throw new BusinessException("作废失败！原因：入参询价单id必须非空");
        }
        //校验
        AskPriceBO askPriceBOInDB = DS.findById(AskPriceBO.class, askPriceBO.getId());
        if (askPriceBOInDB == null) {
            throw new BusinessException("作废失败！原因：询价单不存在");
        }
        //作废运营端询价单
        AskPriceBO updateAskPriceBO = new AskPriceBO();
        updateAskPriceBO.setId(askPriceBO.getId());
        updateAskPriceBO.setStatus(AskPriceStatusDict.INVALID);
        DS.update(askPriceBO);

        //询价单明细：不用管，因为明细必须用询价单来查！

        //作废供应商报价单
        AskSupplierPriceBO updateAskSupplierPriceBO = new AskSupplierPriceBO();
        updateAskSupplierPriceBO.setAskPriceBO(askPriceBO);
        updateAskSupplierPriceBO.setStatus(AskSupplierPriceStatusDict.INVALID);
        DS.update(updateAskSupplierPriceBO);
    }
}
