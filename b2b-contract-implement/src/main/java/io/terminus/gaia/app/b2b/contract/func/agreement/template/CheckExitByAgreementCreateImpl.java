package io.terminus.gaia.app.b2b.contract.func.agreement.template;

import io.terminus.datastore.dsl.Query;
import io.terminus.datastore.dsl.impl.TSQL;
import io.terminus.draco.web.autoconfig.context.UserContext;
import io.terminus.gaia.app.b2b.contract.model.AgreementBO;
import io.terminus.gaia.app.b2b.contract.model.AgreementTemplateBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/23
 */
@FunctionImpl
public class CheckExitByAgreementCreateImpl implements CheckExitByAgreementCreate {
    @Override
    public AgreementTemplateBO execute(AgreementBO agreementBO) {
        Query query = TSQL.select(TSQL.field("id"),TSQL.field(AgreementTemplateBO.code_field))
                .from(AgreementTemplateBO.class)
                .where(TSQL.field(AgreementTemplateBO.agreementBO_field).eq(agreementBO.getId()))
                .and(TSQL.field(AgreementTemplateBO.maintainUsers_field).contains(UserContext.getUserId()));
        List<AgreementTemplateBO> result = DS.findAll(query);
        if (result != null && result.size() > 0){
            return result.get(0);
            //throw  new BusinessException("招标协议编码"+agreementBO.getCode()+"已经被该用户引用为模版，请先删除模版再进行指定！");
        }

        return null;
    }
}
