package io.terminus.gaia.contract.flow.sign.write;

import io.terminus.gaia.contract.model.sign.SignatoriesBO;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.api.annotation.Flow;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 批量拒绝
 *
 * <AUTHOR>
 */
@Flow(name = "Batch reject signatories flow")
public interface BatchRejectSignatoriesFlow {
    /**
     * 批量拒绝
     * @param signatoriesBoList 待拒绝的签署方
     */
    void execute(List<SignatoriesBO> signatoriesBoList);
}
