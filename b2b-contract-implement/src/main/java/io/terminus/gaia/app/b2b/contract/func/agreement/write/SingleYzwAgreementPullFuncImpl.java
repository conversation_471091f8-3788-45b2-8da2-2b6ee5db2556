package io.terminus.gaia.app.b2b.contract.func.agreement.write;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import io.terminus.gaia.app.b2b.contract.constants.LockKey;
import io.terminus.gaia.app.b2b.contract.model.AgreementBO;
import io.terminus.gaia.app.b2b.contract.spring.model.yzw.YzwContract;
import io.terminus.gaia.app.b2b.contract.spring.model.yzw.YzwContractPagingCriteria;
import io.terminus.gaia.app.b2b.contract.spring.proxy.YzwProxy;
import io.terminus.gaia.app.b2b.contract.spring.service.AgreementService;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.lock.Lock;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@FunctionImpl
@Slf4j
@RequiredArgsConstructor
public class SingleYzwAgreementPullFuncImpl implements SingleYzwAgreementPullFunc {

    private final YzwProxy yzwProxy;
    private final AgreementService agreementService;

    @Override
    public BooleanResult execute(AgreementBO agreementBO) {
        executeWithLock(agreementBO);
        return BooleanResult.TRUE;
    }

    private void executeWithLock(AgreementBO agreementBO) {

        if (StrUtil.isBlank(agreementBO.getExternalCode())) {
            throw new BusinessException("云筑协议编码不能为空");
        }
        AgreementBO exist = DS.findOne(AgreementBO.class,"*", "externalCode = ?",agreementBO.getExternalCode());

        String lockKey = "";
        if (Objects.nonNull(exist)) {
            lockKey = LockKey.SYNC_AGREEMENT + "_" + exist.getId();
            if (!Lock.lock(lockKey, 3, TimeUnit.MINUTES)) {
                throw new BusinessException("当前协议正在同步，请稍后再试");
            }
        }

        log.info("开始同步协议【{}】", agreementBO.getExternalCode());
        try {
            syncFromYzw(agreementBO);
        } finally {
            if (Objects.nonNull(exist)) {
                Lock.unlock(lockKey);
            }
        }
    }

    /**
     * 集采同步
     *
     * @param agreementBO
     */
    private void syncFromYzw(AgreementBO agreementBO) {

        YzwContractPagingCriteria criteria = new YzwContractPagingCriteria();
        criteria.setLimitRows(10);
        criteria.setSystemCategoryList(null);
        criteria.setContractCode(agreementBO.getExternalCode());
        criteria.setStatusList(null);
        criteria.setContractType(null);
        criteria.setContractTypes(ListUtil.toList(0,1,2));
        List<YzwContract> contractList = yzwProxy.getSimpleContractList(criteria);
        if (CollUtil.isNotEmpty(contractList)) {
            YzwContract yzwContract = yzwProxy.getContractDetail(String.valueOf(contractList.get(0).getContractSysNo()));
            if (yzwContract == null) {
                throw new BusinessException("根据【" + agreementBO.getExternalCode() + "】找不到云筑网合同");
            }
            agreementService.saveYzwAgreement(ListUtil.toList(yzwContract));
        } else {
            throw new BusinessException("根据【" + agreementBO.getExternalCode() + "】找不到云筑网合同");
        }

    }

}
