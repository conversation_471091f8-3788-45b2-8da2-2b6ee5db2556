package io.terminus.gaia.contract.func.contract.validate;

import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.gaia.contract.msg.contract.ContractExMsg;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;

import java.util.Objects;

/**
 * 检测签署任务
 * <AUTHOR>
 * @date 2021-04-16
 */
@FunctionImpl(name = "validate sign contract callback params")
public class ValidateSignContractCallbackParamsFuncImpl implements ValidateSignContractCallbackParamsFunc{
    /**
     * 检测签署任务
     * @param contractBO 框架合同
     */
    @Override
    public void execute(ContractBO contractBO) {
        if (Objects.isNull(contractBO.getId())) {
            throw new BusinessException(ContractExMsg.CONTRACT_ID_IS_NULL);
        }
    }
}
