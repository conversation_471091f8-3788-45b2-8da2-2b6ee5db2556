import {action, Controller, state} from 'nusi-sdk'

export default class extends Controller {


    // 批量删除校验
    validateSelect = async (record) => {
        let data = record.data;
        if (data.length > 1) {
            return "最多只能选择一个价格方案"
        }
        return true;
    }

    public formatAttributes(value) {
        return value?.map(attr => {
            const {attributeName, attributeValueType, attributeValueTO, unitName} = attr;
            let attrValue;
            if (attributeValueType === 'TEXT') {
                attrValue = attributeValueTO?.textValue;
            }
            if (attributeValueType === 'NUMBER') {
                attrValue = attributeValueTO?.numberValue;
            }
            if (attrValue === undefined) {
                attrValue = '无'
            }
            return `${attributeName}: ${attrValue}${unitName || ''}`
        }).join(' | ');
    }

    alreadyGoBack = false;

    // 选择
    onTableSelect = (selectedData) => {
        if (this.alreadyGoBack) {
            return
        }
        this.alreadyGoBack = true
        if (this._.isEmpty(selectedData)) {
            return
        }
        if (this.env.selection === 'multi') {
            return
        }
        this.goBack({withPayload: true, payload: {record: selectedData}})
    }
}


