package io.terminus.gaia.contract.func.contract.write;

import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.gaia.contract.model.contract.ContractEntityRelBO;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.flow.LF;
import io.terminus.trantorframework.sdk.sql.DS;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@FunctionImpl
public class CreateContractMultiPartyFuncImpl implements CreateContractMultiPartyFunc {
    @Override
    public void execute(ContractBO contractBO) {
        // 如果是多方签署，保存关联关系
        List<EntityBO> partyCList = contractBO.getPartyCList();
        if (CollectionUtils.isEmpty(partyCList)) {
            return;
        }
        List<ContractEntityRelBO> relations = LF.map(partyCList, entityBO -> {
            ContractEntityRelBO contractEntityRelBO = new ContractEntityRelBO();
            contractEntityRelBO.setContract(contractBO);
            contractEntityRelBO.setEntity(entityBO);
            return contractEntityRelBO;
        });

        DS.create(relations);
    }
}
