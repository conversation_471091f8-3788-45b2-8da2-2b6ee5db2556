package io.terminus.gaia.app.b2b.contract.spring.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import io.terminus.datastore.dsl.Query;
import io.terminus.datastore.dsl.Record;
import io.terminus.datastore.dsl.SelectConditionStep;
import io.terminus.datastore.dsl.SelectJoinStep;
import io.terminus.datastore.dsl.impl.TSQL;
import io.terminus.gaia.app.b2b.contract.dict.ContractSyncSourceDict;
import io.terminus.gaia.app.b2b.contract.dict.YzContractStatusDict;
import io.terminus.gaia.app.b2b.contract.model.AgreementSyncMessageBO;
import io.terminus.gaia.app.b2b.contract.model.ContractLineBO;
import io.terminus.gaia.app.b2b.contract.model.YzContractBO;
import io.terminus.gaia.app.b2b.contract.spring.converter.YzContractConverter;
import io.terminus.gaia.app.b2b.contract.spring.model.yz.YzContract;
import io.terminus.gaia.app.b2b.contract.spring.model.yz.YzContractDetail;
import io.terminus.gaia.app.b2b.contract.spring.model.yz.YzResult;
import io.terminus.gaia.app.b2b.contract.spring.repo.YzContractRepo;
import io.terminus.gaia.common.open.yz.properties.YzProperties;
import io.terminus.gaia.common.utils.EnhanceDS;
import io.terminus.trantorframework.Page;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.json.Json;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class YzContractService {
    @Autowired
    private YzProperties yzProperties;

    @Autowired
    private YzContractRepo yzContractRepo;

    /**
     * 全量刷新合同行逻辑，查出所有生效的合同，放到线程池中进行并行操作
     * @return 是否成功
     */
    @Async
    public CompletableFuture<Void> updateContractLineRemarkAsync(String contractCode){
        log.info("update contract line remark async param {}" , contractCode);
        updateContractRemark(contractCode);
        return CompletableFuture.completedFuture(null);
    }

    /**
     * 更新合同清单行备注信息，从云筑库中拉出备注数据，存入端点库中。
     * <p>
     * 更新逻辑：
     * - 如果传入合同编码，则仅更新对应合同；
     * - 如果未传入合同编码，则更新所有合同。
     *
     * @param contractCode 合同编码，可为空
     */
    private void updateContractRemark(String contractCode) {
        int batchSize = 1000;
        int pageNum = 1;

        // 当传入contractCode时，只查一条合同
        if (StringUtils.isNotBlank(contractCode)) {
            YzContractBO existContract = DS.findOne(YzContractBO.class, "*,subContracts.*", "contractCode = ?", contractCode);
            if (existContract == null) {
                return;
            }
            List<String> contractIdList = Collections.singletonList(existContract.getExternalCode());
            processContractIdBatch(contractIdList);
            return;
        }

        // 当contractCode为空，分页查询所有合同
        while (true) {
            // 假设DS.findPage方法支持分页查询，第一个参数是类，第二参数是分页参数，第三参数是查询条件
            // 这里根据你实际的分页查询API调整
            Page page = new Page(pageNum, batchSize);
            Paging<YzContractBO> paging = DS.paging(YzContractBO.class, "*", "1=1", page);

            if (Objects.isNull(paging) || CollectionUtils.isEmpty(paging.getData())) {
                log.info("所有合同查询完成，分页查询无数据，结束处理。");
                break;
            }
            List<String> contractIdList = paging.getData().stream()
                    .map(YzContractBO::getExternalCode)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(contractIdList)) {
                log.info("当前分页合同列表为空，跳过处理。页码：{}", pageNum);
                pageNum++;
                continue;
            }
            processContractIdBatch(contractIdList);
            pageNum++;
        }
    }

    private void processContractIdBatch(List<String> contractIdList) {
        int batchSize = 10;
        for (int i = 0; i < contractIdList.size(); i += batchSize) {
            List<String> contractIds = contractIdList.subList(i, Math.min(i + batchSize, contractIdList.size()));
            List<YzContractDetail> batchDetailList = new ArrayList<>();

            // Step 1: 查询合同详情
            for (String contractId : contractIds) {
                try {
                    YzContract yzContract = this.getYzContractByCode(contractId);
                    if (yzContract != null && CollectionUtils.isNotEmpty(yzContract.getDetailList())) {
                        batchDetailList.addAll(yzContract.getDetailList());
                    }
                } catch (Exception e) {
                    log.error("getYzContractByCode 异常，contractId={}，异常信息：{}", contractId, e.getMessage());
                }
            }

            // Step 2: 判断是否有合同详情
            if (CollectionUtils.isEmpty(batchDetailList)) {
                log.info("当前批次未获取到任何合同详情，跳过后续处理。");
                continue;
            }

            // Step 3: 收集 intentionDetailNo
            List<String> intentionDetailNoList = batchDetailList.stream()
                    .map(YzContractDetail::getContractIntentionDetailNo)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(intentionDetailNoList)) {
                log.info("当前批次合同详情中无有效的意向编号，跳过后续处理。");
                continue;
            }

            // Step 4: 批量查询本地 ContractLineBO
            List<ContractLineBO> contractLineList = DS.findAll(
                    TSQL.selectFrom(ContractLineBO.class)
                            .where(TSQL.field("intentionDetailNo").in(intentionDetailNoList))
            );

            if (CollectionUtils.isEmpty(contractLineList)) {
                log.info("当前批次未查询到本地合同意向详情记录，跳过更新。");
                continue;
            }

            // Step 5: 建立映射
            Map<String, ContractLineBO> contractLineMap = contractLineList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(
                            ContractLineBO::getIntentionDetailNo,
                            Function.identity(),
                            (a, b) -> a
                    ));

            // Step 6: 更新本地数据（考虑批量更新）
            List<ContractLineBO> needUpdateList = new ArrayList<>();
            for (YzContractDetail detail : batchDetailList) {
                if (detail == null) continue;

                ContractLineBO contractLineBO = contractLineMap.get(detail.getContractIntentionDetailNo());
                if (contractLineBO != null
                        && StringUtils.isNotBlank(detail.getRemark())
                        && !StringUtils.equals(detail.getRemark(), contractLineBO.getRemark())) {
                    ContractLineBO newContractLineBO = new ContractLineBO();
                    newContractLineBO.setId(contractLineBO.getId());
                    newContractLineBO.setRemark(detail.getRemark());
                    needUpdateList.add(newContractLineBO);
                }
            }

            try {
                if (CollectionUtils.isNotEmpty(needUpdateList)) {
                    DS.update(needUpdateList);
                    log.info("本批次共更新 {} 条合同意向详情备注", needUpdateList.size());
                }
            } catch (Exception e) {
                log.error("更新合同详情备注时发生异常，异常信息：{}", e.getMessage(), e);
            }
            log.info("处理了本批次合同详情，共 {} 条详情", batchDetailList.size());
        }
    }



    /**
     *
     * @param action 1 创建 2变更 3 作废
     * @param contractNo 只能拉取主合同
     * @return
     */
    public boolean save(int action, String contractNo) {
        YzContractBO existContract = DS.findOne(YzContractBO.class, "*,subContracts.*", "externalCode = ?", contractNo);
        if (existContract != null && action == 1) {
            log.warn("【{}】重复创建，跳过", contractNo);
            return true;
        }
        if (existContract == null && action != 1) {
            log.warn("不存在的合同编号【{}】, action={}", contractNo, action);
            return true;
        }
        Map<String, YzContractBO> existSubContractMap = existContract == null ? new HashMap<>() :
                CollUtil.defaultIfEmpty(existContract.getSubContracts(), new ArrayList<>())
                        .stream().collect(Collectors.toMap(
                                YzContractBO::getExternalCode,
                                Function.identity(),
                                (existing, replacement) -> existing // 遇到重复键时保留原值
                        ));
        ;

        YzContract contractDTO = getYzContractByCode(contractNo);
        try {
            switch (action) {
                case 1:
                    log.info("======================== 新建云筑合同 =============================");
                    // 创建
                    yzContractRepo.createContract(YzContractConverter.convert(contractDTO, null));
                    return true;
                case 2:
                    // 更新
                    log.info("======================== 更新主合同 =============================");
                    log.info("contractDTO:{}", JSON.toJSONString(contractDTO));
                    yzContractRepo.updateContractAndRel(YzContractConverter.convert(contractDTO, existContract));
                    if (CollectionUtils.isNotEmpty(contractDTO.getSubContracts())) {
                        log.info("======================== 更新补充合同 =============================");
                        contractDTO.getSubContracts().forEach(subContract -> {
                            // 不接受作废的更新
                            if (subContract.isDrop()) {
                                return;
                            }
                            YzContractBO existSubContract = existSubContractMap.get(subContract.getId().toString());
                            if (existSubContract == null) {
                                log.info("云筑合同同步，新建补充合同【{}-{}】，主合同【{}-{}】", subContract.getId(), subContract.getContractName(), contractDTO.getId(), contractDTO.getContractName());
                                yzContractRepo.createContract(YzContractConverter.convert(subContract, null));
                            } else {
                                log.info("更新补充合同【{}-{}】", subContract.getId(), subContract.getContractName());
                                yzContractRepo.updateContractAndRel(YzContractConverter.convert(subContract, existSubContract));
                            }
                        });
                    }
                    return true;
                case 3:
                    log.info("======================== 作废云筑合同 =============================");
                    yzContractRepo.updateContractAndRel(YzContractConverter.convert(contractDTO, existContract));

                    if (CollectionUtils.isNotEmpty(contractDTO.getSubContracts())) {
                        contractDTO.getSubContracts().forEach(subContract -> {
                            // 只接受作废
                            if (!subContract.isDrop()) {
                                return;
                            }
                            YzContractBO existSubContract = existSubContractMap.get(subContract.getId().toString());
                            if (existSubContract == null) {
                                return;
                            }
                            // 已作废
                            if (YzContractStatusDict.STOP.equals(existSubContract.getContractStatusDict())) {
                                return;
                            }
                            log.info("作废云筑补充合同【{}-{}】", subContract.getId(), subContract.getContractName());
                            yzContractRepo.updateContractAndRel(YzContractConverter.convert(subContract, existSubContract));
                        });
                    }

                    return true;
                default:
                    log.warn("未知的合同action={}", action);
                    return false;
            }
        } catch (Exception ex) {
            log.error("云筑合同save失败：{}", ex);
            log.error("云筑合同save失败：{}", Throwables.getStackTraceAsString(ex));
            AgreementSyncMessageBO existSyncMessage = EnhanceDS.safeFindOne(AgreementSyncMessageBO.class,
                    "*", "externalId = ? AND source = ?",
                    contractNo, ContractSyncSourceDict.YZ_SYNC);
            if (existSyncMessage != null) {
                AgreementSyncMessageBO toUpdate = new AgreementSyncMessageBO();
                toUpdate.setId(existSyncMessage.getId());
                toUpdate.setMessage(Json.toJson(contractDTO));
                toUpdate.setError(StrUtil.subSufByLength(Throwables.getStackTraceAsString(ex), 1000));
                DS.update(existSyncMessage);
            } else {
                AgreementSyncMessageBO syncMessageBO = new AgreementSyncMessageBO();
                syncMessageBO.setExternalId(contractNo);
                syncMessageBO.setExternalCode(contractDTO.getContractNo());
                syncMessageBO.setExternalName(contractDTO.getContractName());
                syncMessageBO.setSource(ContractSyncSourceDict.YZ_SYNC);
                syncMessageBO.setMessage(Json.toJson(contractDTO));
                DS.create(syncMessageBO);
            }
            return false;
        }
    }

    /**
     * 根据编码查询云筑合同
     *
     * @param code
     * @return
     */
    public YzContract getYzContractByCode(String code) {
        try {
            String url = UrlBuilder.ofHttp(yzProperties.getHost() + "/open/contract/detail")
                    .addQuery("contractId", code).build();
            YzResult<YzContract> yzResult = JSONUtil.toBean(HttpRequest.get(url).execute().body(), new TypeReference<YzResult<YzContract>>() {
            }, true);
            log.info("查询云筑合同【{}】:{}", code, Json.toJson(yzResult));
            if (!yzResult.isSuccess()) {
                throw new BusinessException(StrUtil.format("云筑合同信息查询失败：{}", yzResult.getMessage()));
            }
            YzContract contractDTO = yzResult.getData();
            if (contractDTO == null) {
                throw new BusinessException(StrUtil.format("根据【{}】查询不到云筑合同", code));
            }
            return contractDTO;
        } catch (Exception ex) {
            if (ex instanceof BusinessException) {
                throw ex;
            }
            log.error("查询云筑合同未知异常：{}", Throwables.getStackTraceAsString(ex));
            throw new BusinessException(StrUtil.format("查询云筑合同未知异常：{}", ex.getMessage()));
        }
    }
}
