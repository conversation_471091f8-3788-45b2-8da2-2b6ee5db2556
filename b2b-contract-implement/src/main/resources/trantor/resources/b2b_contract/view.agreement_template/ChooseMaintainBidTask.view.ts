/**
 *  controller template generated by trantor devtools
 *
 */
import { Controller, Toast, state, showMessage } from 'nusi-sdk'

export default class extends Controller {


    constructor() {
        super()
    }

    public onChange() {
        // TODO write your code here
    }

    nextSteps = async (ctx) => {
        let result = new Array();
        ctx.context.map(obj => result.push(obj));
        console.log('===== result =====',result)

        const idList = ctx.context.map(item => item.id);
        if (idList.length === 0) {
            Toast.error("至少添加一个招标任务");
            return false;
        }

        // 跳转标记，如果所选的招标任务下所有的已维护列表为空将进入全部维护页面
        let flag = false;
        let hasMaintainCount = 0;

        // 循环判断每个招标任务下是否存在已经维护的协议
        result.forEach(item => {
            // 判断选择的招标任务列表下的维护过的列表是否为空
            if (Array.isArray(item.maintainList) && item.maintainList.length > 0){
                hasMaintainCount++;
            }
        })

        if (hasMaintainCount > 0){
            // 表示有已经维护的协议
            this.openView('b2b_contract_ChooseMaintainBidTaskTO_ChooseMaintainBidTaskDialog', {
                openViewType: 'Dialog',
                openViewSize: 'xs',
                env: { taskList: result}
            })
        }else{
            // 表示所有协议均为维护
            this.openView('b2b_contract_AgreementTemplateBO_NoMaintainAgreementTaskBid', {
                openViewType: 'Dialog',
                openViewSize: 'xs',
                env: { taskList: result}
            })
        }



    }
}