package io.terminus.gaia.contract.flow.sign.write;

import io.terminus.gaia.contract.model.sign.SignatureBO;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.api.annotation.Flow;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 批量停用电子签名
 *
 * <AUTHOR>
 */
@Flow(name = "Batch disableSignatureFlow")
public interface BatchDisableSignatureFlow {
    /**
     * 批量停用电子签名
     * @param signatureList 待停用的电子签名
     */
    void execute(List<SignatureBO> signatureList);
}
