package io.terminus.gaia.app.b2b.contract.func.purchase;

import io.terminus.gaia.app.b2b.contract.func.agreement.write.MatchSalePriceFunc;
import io.terminus.gaia.app.b2b.contract.model.AgreementDetailBO;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseBO;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseLineBO;
import io.terminus.gaia.app.b2b.contract.tmodel.AutoMatchingAgreementResultTO;
import io.terminus.gaia.item.func.sku.read.SmartMatchSkuImportFunc;
import io.terminus.gaia.item.model.sku.SkuBO;
import io.terminus.gaia.item.model.spu.SpuBO;
import io.terminus.gaia.item.tmodel.SmartMatchSkuRequestTO;
import io.terminus.gaia.item.tmodel.SmartMatchSkuResultTO;
import io.terminus.gaia.item.tmodel.SmartMatchSkuTO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @class_name: AutoMatchingAgreementFuncImpl
 * @desc: 一键匹配协议实现
 * @date: 2025/7/2 : 16:35
 * @author: Chonor
 **/
@FunctionImpl
@Slf4j
@RequiredArgsConstructor
public class AutoMatchingAgreementFuncImpl implements AutoMatchingAgreementFunc{

    private final SmartMatchSkuImportFunc smartMatchSkuImportFunc;
    private final MatchSalePriceFunc matchSalePriceFunc;
    private final CheckAllInAgreementFunc checkAllInAgreementFunc;

    @Override
    public AutoMatchingAgreementResultTO execute(RequestPurchaseBO requestPurchaseBO) {
        log.info("开始执行一键匹配协议，需求单ID: {}", requestPurchaseBO.getId());
        
        Long requestPurchaseId = requestPurchaseBO.getId();
        if (requestPurchaseId == null) {
            throw new BusinessException("需求单ID不能为空");
        }

        // 1. 获取所有的 RequestPurchaseLineBO 需求明细
        List<RequestPurchaseLineBO> requestPurchaseLines = DS.findAll(RequestPurchaseLineBO.class, 
                "*,brandBO.*", 
                "requestPurchaseBO.id = ?", 
                requestPurchaseId);

        if (CollectionUtils.isEmpty(requestPurchaseLines)) {
            log.warn("需求单ID: {} 没有找到需求明细", requestPurchaseId);
            return AutoMatchingAgreementResultTO.success(0, 0, 0);
        }

        log.info("找到需求明细 {} 条", requestPurchaseLines.size());

        // 2. 批量构建智能匹配请求
        SmartMatchSkuRequestTO batchSmartMatchRequest = buildBatchSmartMatchRequest(requestPurchaseLines);
        
        // 3. 批量调用智能匹配
        SmartMatchSkuResultTO batchSmartMatchResult = null;
        try {
            log.info("开始批量智能匹配SKU，总数: {}", requestPurchaseLines.size());
            batchSmartMatchResult = smartMatchSkuImportFunc.execute(batchSmartMatchRequest);
            log.info("批量智能匹配SKU完成，成功: {}, 失败: {}", 
                    batchSmartMatchResult.getSuccessSkuList().size(), 
                    batchSmartMatchResult.getFailSkuList().size());
        } catch (Exception e) {
            log.error("批量智能匹配SKU失败，错误: {}", e.getMessage(), e);
            return AutoMatchingAgreementResultTO.success(0, requestPurchaseLines.size(), requestPurchaseLines.size());
        }

        // 4. 根据批量匹配结果处理每条数据
        AutoMatchingAgreementResultTO result = processBatchMatchResult(requestPurchaseLines, batchSmartMatchResult);
        
        // 5. 检查需求单是否所有明细都匹配到协议
        checkAllInAgreementFunc.execute(requestPurchaseBO);
        
        return result;
    }

    /**
     * 构建批量智能匹配请求
     */
    private SmartMatchSkuRequestTO buildBatchSmartMatchRequest(List<RequestPurchaseLineBO> requestPurchaseLines) {
        SmartMatchSkuRequestTO smartMatchRequest = new SmartMatchSkuRequestTO();
        List<SmartMatchSkuTO> smartMatchSkuTOList = new ArrayList<>();
        
        for (int i = 1; i <= requestPurchaseLines.size(); i++) {
            RequestPurchaseLineBO requestPurchaseLine = requestPurchaseLines.get(i-1);
            
            // 跳过没有品牌信息的需求明细
            if (requestPurchaseLine.getBrandBO() == null) {
                log.warn("需求明细ID: {} 品牌信息为空，跳过匹配", requestPurchaseLine.getId());
                continue;
            }
            
            SmartMatchSkuTO smartMatchSkuTO = new SmartMatchSkuTO();
            smartMatchSkuTO.setSkuName(requestPurchaseLine.getThingSizeDesc());
            smartMatchSkuTO.setBrandName(requestPurchaseLine.getBrandBO().getBrandName());
            smartMatchSkuTO.setOriginalIndex(i); // 设置原始索引，用于后续匹配结果对应
            smartMatchSkuTOList.add(smartMatchSkuTO);
        }
        
        smartMatchRequest.setSmartMatchSkuTOList(smartMatchSkuTOList);
        return smartMatchRequest;
    }

    /**
     * 处理批量匹配结果
     */
    private AutoMatchingAgreementResultTO processBatchMatchResult(List<RequestPurchaseLineBO> requestPurchaseLines, 
                                                                  SmartMatchSkuResultTO batchSmartMatchResult) {
        int successCount = 0;
        int failCount = 0;
        // 用于收集没有销售测算数据的协议名称
        Set<String> failedAgreementNames = new LinkedHashSet<>();
        
        // 构建成功匹配的SKU映射 (originalIndex -> SkuBO)
        Map<Integer, SkuBO> successSkuMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(batchSmartMatchResult.getSuccessSkuList())) {
            for (SkuBO skuBO : batchSmartMatchResult.getSuccessSkuList()) {
                if (skuBO.getOriginalIndex() != null) {
                    successSkuMap.put(skuBO.getOriginalIndex(), skuBO);
                }
            }
        }
        
        // 记录失败的索引
        Set<Integer> failedIndexes = new HashSet<>();
        if (CollectionUtils.isNotEmpty(batchSmartMatchResult.getFailSkuList())) {
            for (SmartMatchSkuTO failSku : batchSmartMatchResult.getFailSkuList()) {
                if (failSku.getOriginalIndex() != null) {
                    failedIndexes.add(failSku.getOriginalIndex());
                }
            }
        }
        
        // 5. 遍历每个需求明细进行后续处理
        for (int i = 1; i <= requestPurchaseLines.size(); i++) {
            RequestPurchaseLineBO requestPurchaseLine = requestPurchaseLines.get(i-1);
            
            try {
                // 跳过没有品牌信息的需求明细
                if (requestPurchaseLine.getBrandBO() == null) {
                    log.warn("需求明细ID: {} 品牌信息为空，跳过处理", requestPurchaseLine.getId());
                    failCount++;
                    continue;
                }
                
                // 检查是否在失败列表中
                if (failedIndexes.contains(i)) {
                    log.warn("需求明细ID: {} 智能匹配SKU失败", requestPurchaseLine.getId());
                    failCount++;
                    continue;
                }
                
                // 获取匹配成功的SKU
                SkuBO matchedSku = successSkuMap.get(i-1);
                if (matchedSku == null) {
                    log.warn("需求明细ID: {} 未找到匹配的SKU", requestPurchaseLine.getId());
                    failCount++;
                    continue;
                }
                
                // 处理匹配成功的SKU
                MatchResult matchResult = processMatchedSku(requestPurchaseLine, matchedSku);
                if (matchResult.isSuccess()) {
                    successCount++;
                } else {
                    failCount++;
                    // 收集销售测算失败的协议名称
                    if (StringUtils.isNotBlank(matchResult.getFailedAgreementName())) {
                        failedAgreementNames.add(matchResult.getFailedAgreementName());
                    }
                }
                
            } catch (Exception e) {
                log.error("处理需求明细失败，明细ID: {}, 错误: {}", requestPurchaseLine.getId(), e.getMessage(), e);
                failCount++;
            }
        }
        
        int totalCount = requestPurchaseLines.size();
        log.info("一键匹配协议完成，成功: {} 条，失败: {} 条", successCount, failCount);

        // 6. 根据是否有销售测算失败来决定返回结果
        if (!failedAgreementNames.isEmpty()) {
            return AutoMatchingAgreementResultTO.salePriceFailed(successCount, failCount, totalCount, new ArrayList<>(failedAgreementNames));
        } else {
            return AutoMatchingAgreementResultTO.success(successCount, failCount, totalCount);
        }
    }

    /**
     * 处理匹配成功的SKU，进行协议匹配和价格计算
     */
    private MatchResult processMatchedSku(RequestPurchaseLineBO requestPurchaseLine, SkuBO matchedSku) {
        Long lineId = requestPurchaseLine.getId();
        
        log.info("处理匹配成功的SKU，需求明细ID: {}, SKU: {}", lineId, matchedSku.getSkuCode());

        // 1. 根据 SkuBO 找到关联的 SpuBO
        SpuBO spuBO = matchedSku.getSpu();
        if (spuBO == null) {
            log.warn("SKU: {} 没有关联的SPU", matchedSku.getSkuCode());
            return new MatchResult(false);
        }

        // 将匹配到的标品落库
        RequestPurchaseLineBO update = new RequestPurchaseLineBO();
        update.setId(requestPurchaseLine.getId());
        update.setSpu(spuBO);
        DS.update(update);

        // 2. 根据 SpuBO + brandBO 匹配对应的 AgreementDetailBO 协议清单
        List<AgreementDetailBO> agreementDetails = DS.findAll(AgreementDetailBO.class, 
                "*,spuBO.*,agreementBO.*,brandBo.id,saleEntityBO.*", 
                "spuBO.id = ? AND brandBo.id = ? AND statusDict = 'START_USING'", 
                spuBO.getId(), 
                requestPurchaseLine.getBrandBO().getId());

        if (CollectionUtils.isEmpty(agreementDetails)) {
            log.warn("未找到匹配的协议清单，需求明细 ID: {}，SPU ID: {}, 品牌ID: {}",
                    lineId, spuBO.getId(), requestPurchaseLine.getBrandBO().getId());
            return new MatchResult(false);
        }

        // 取第一个匹配的协议清单
        AgreementDetailBO agreementDetailBO = agreementDetails.get(0);
        
        log.info("匹配到协议清单，协议清单ID: {}, 协议ID: {}", 
                agreementDetailBO.getId(), 
                agreementDetailBO.getAgreementBO() != null ? agreementDetailBO.getAgreementBO().getId() : "null");

        // 3. 进行价格计算和匹配销售测算
        return performPriceCalculation(requestPurchaseLine, agreementDetailBO);
    }

    /**
     * 执行价格计算和匹配销售测算
     */
    private MatchResult performPriceCalculation(RequestPurchaseLineBO requestPurchaseLine, AgreementDetailBO agreementDetailBO) {
        try {
            RequestPurchaseLineBO update = new RequestPurchaseLineBO();
            update.setId(requestPurchaseLine.getId());
//            update.setBrandBO(agreementDetailBO.getBrandBo());
            update.setSupplier(agreementDetailBO.getSaleEntityBO());
            update.setSpu(agreementDetailBO.getSpuBO());
            update.setAgreementDetailBO(agreementDetailBO);
            update.setAgreement(agreementDetailBO.getAgreementBO());

            // 采购含铜量
            final BigDecimal purRawMaterialContent = agreementDetailBO.getSpuBO().getRawMaterialContent();
            update.setPurRawMaterialContent(purRawMaterialContent);

            // 采购铜基价
            final BigDecimal purCopperBasicPrice = requestPurchaseLine.getPurCopperBasicPrice();

            // 采购辅材及其他价格
            final BigDecimal purOtherCosts = agreementDetailBO.getOtherCosts();
            update.setPurOtherCosts(purOtherCosts);
            // 销售辅材及其他价格，给默认值 = 采购辅材及其他价格
            update.setSaleOtherCosts(purOtherCosts);

            // 采购折扣系数
            final BigDecimal purDiscountFactor = agreementDetailBO.getPurDiscountFactor();
            update.setPurDiscountFactor(purDiscountFactor);

            // 采购延米铜价 = （含铜量*铜基价/1000）
            final BigDecimal purCopperPrice = purRawMaterialContent.multiply(purCopperBasicPrice).divide(new BigDecimal("1000"), 6, RoundingMode.HALF_UP);
            update.setPurCopperPrice(purCopperPrice);
            // 销售延米铜价 = 采购延米铜价
            update.setSaleCopperPrice(purCopperPrice);

            // 采购含税单价 = （铜基价*标品含铜量/1000+辅材价格）*采购折扣系数
            final BigDecimal purTaxPrice = purRawMaterialContent.multiply(purCopperBasicPrice).divide(new BigDecimal("1000"), 6, RoundingMode.HALF_UP).add(purOtherCosts).multiply(purDiscountFactor);
            update.setPurTaxPrice(purTaxPrice);

            // 匹配销售测算
            final BooleanResult matchSalePrice = matchSalePriceFunc.execute(update);

            if (!matchSalePrice.getValue()) {
                // 如果销售测算匹配失败，记录协议名称
                String agreementName = agreementDetailBO.getAgreementBO() != null ?
                        agreementDetailBO.getAgreementBO().getName() : "未知协议";
                log.warn("销售测算匹配失败，需求明细ID: {}, 协议名称: {}", requestPurchaseLine.getId(), agreementName);
                return new MatchResult(false, agreementName);
            }

            // 更新数据库
            DS.update(update);

            log.info("需求明细ID: {} 匹配协议成功", requestPurchaseLine.getId());
            return new MatchResult(true);
            
        } catch (Exception e) {
            log.error("价格计算失败，需求明细ID: {}, 错误: {}", requestPurchaseLine.getId(), e.getMessage(), e);
            return new MatchResult(false);
        }
    }

    /**
     * 匹配结果封装类
     */
    private static class MatchResult {
        private boolean success;
        private String failedAgreementName;

        public MatchResult(boolean success) {
            this.success = success;
        }

        public MatchResult(boolean success, String failedAgreementName) {
            this.success = success;
            this.failedAgreementName = failedAgreementName;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getFailedAgreementName() {
            return failedAgreementName;
        }
    }
}
