package io.terminus.gaia.contract.func.classification.validate;

import io.terminus.gaia.contract.model.classification.ContractClassificationBO;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * 合同新增的校验
 *
 * <AUTHOR>
 */
@Function
public interface ValidateCreateContractClassificationFunc {

    /**
     * 合同新增的校验
     *
     * @param contractClassificationBO
     */
    void execute(ContractClassificationBO contractClassificationBO);
}
