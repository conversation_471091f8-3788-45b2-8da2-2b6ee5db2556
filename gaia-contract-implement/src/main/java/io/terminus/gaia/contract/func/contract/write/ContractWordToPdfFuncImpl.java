package io.terminus.gaia.contract.func.contract.write;

import cn.hutool.core.lang.UUID;
import com.google.common.collect.Lists;
import io.terminus.gaia.common.WordHelper;
import io.terminus.gaia.common.enums.FileTypeEnum;
import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.gaia.contract.util.MagerWordToOssUtil;
import io.terminus.gaia.contract.util.OSSFileHelper;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.api.type.Attachment;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;

/**
 * 手动创建word转pdf服务
 *
 * <AUTHOR>
 * @date 2021/12/2 下午5:07
 */
@FunctionImpl
@RequiredArgsConstructor
@Slf4j
public class ContractWordToPdfFuncImpl implements ContractWordToPdfFunc {
    @Autowired
    private final OSSFileHelper ossFileHelper;

    @Autowired
    private final WordHelper wordHelper;

    @Autowired
    private final MagerWordToOssUtil magerWordToOssUtil;

    @Override
    public void execute(ContractBO contractBO) {
        //没有合同文件生成
        if (contractBO.getContractAttachment() == null || CollectionUtils.isEmpty(contractBO.getContractAttachment().getFiles())) {
            log.info("[LISTENER]- no need to consume ContractBO Add MQ");
            return;
        }
        //合同文件
        Attachment.File file = contractBO.getContractAttachment().firstFile();
        //pdf则不处理
        if ("pdf".equals(file.getType()) || "PDF".equals(file.getType())) {
            log.warn("pdf文件不再处理转换文件格式");
            return;
        }
//        //外部附件
//        if (contractBO.getOutAttachment() != null) {
//            Attachment.File outFile = contractBO.getOutAttachment().firstFile();
//            //合并文件
//            try {
//                String mergeDocUrl = magerWordToOssUtil.mergeDoc(file.getName(), file.getType(), file.getUrl(), outFile.getUrl());
//            } catch (Exception e) {
//                log.error("合并文件异常，文件路径为{},{}", file.getUrl(), outFile.getUrl());
//                log.error(Throwables.getStackTraceAsString(e));
//            }
//        }

        // 转PDF
        Attachment attachmentPDF = new Attachment();
        Attachment.File filePDF = contractBO.getContractAttachment().firstFile();
        log.info("word转pdf开始，contractCode为{}", contractBO.getContractCode());
        try {
            InputStream inputStreamPDF = wordHelper.
                    office2File(filePDF.getUrl().startsWith("https:") ? filePDF.getUrl() : "https:" + filePDF.getUrl(), filePDF.getType(), FileTypeEnum.PDF);
            log.info("word转pdf结束，contractCode为{}", contractBO.getContractCode());
            //上传的文件名
            String uploadFileName = filePDF.getName().split("\\.")[0];
            //上传pdf文件到oss
            String filePath = null;
            filePath = ossFileHelper.uploadWithFileName(inputStreamPDF, UUID.randomUUID().toString(), "pdf");
            filePDF.setUrl(URLDecoder.decode(filePath));
            filePDF.setType("pdf");
            filePDF.setName(file.getName().split("\\.")[0] + ".pdf");
            attachmentPDF.setFiles(Lists.newArrayList(filePDF));
            //更新合同PDF文件信息
            ContractBO updateContract = new ContractBO();
            updateContract.setId(contractBO.getId());
            updateContract.setContractAttachmentPdf(attachmentPDF);
            DS.update(updateContract);
        } catch (IOException e) {
            log.error("contractBO attachment pdf fail", e.toString());
        } catch (Exception e) {
            log.error("word转pdf失败，contractCode为{}", contractBO.getContractCode());
        }

    }
}
