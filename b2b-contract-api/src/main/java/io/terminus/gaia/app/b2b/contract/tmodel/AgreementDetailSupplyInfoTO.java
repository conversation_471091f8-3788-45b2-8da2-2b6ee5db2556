package io.terminus.gaia.app.b2b.contract.tmodel;

import io.terminus.gaia.app.b2b.contract.model.AgreementBO;
import io.terminus.gaia.md.model.CompanyBO;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.TransientModel;
import io.terminus.trantorframework.api.annotation.typemeta.CurrencyMeta;
import io.terminus.trantorframework.api.annotation.typemeta.FloatMeta;
import io.terminus.trantorframework.api.annotation.typemeta.LinkMeta;
import io.terminus.trantorframework.api.type.Currency;
import lombok.*;


/**
 * 清单供应信息
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TransientModel(name = "Agreement Detail SupplyInfo TO")
public class AgreementDetailSupplyInfoTO extends BaseModel<Long> {

    private static final long serialVersionUID = -8287066568423507750L;

    /**
     * 关联标品
     */
    @Field(name = "销售主体（公司模型）")
    @LinkMeta
    private EntityBO saleEntityBO;

    @Field(name = "框架协议")
    @LinkMeta
    private AgreementBO agreementBO;

    @Field(name = "不含税价")
    @CurrencyMeta(intDigits = 10, decimalDigits = 4)
    private Currency price;

    @Field(name = "含税价")
    @CurrencyMeta(intDigits = 10, decimalDigits = 4)
    private Currency taxPrice;

    @Field(name = "供货周期")
    private Integer supplyPeriod;

    @Field(name = "报价依据")
    private String quotationBasis;

}
