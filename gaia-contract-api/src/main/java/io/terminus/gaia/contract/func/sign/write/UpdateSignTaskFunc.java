package io.terminus.gaia.contract.func.sign.write;

import io.terminus.gaia.contract.model.sign.SignTaskBO;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * @program: gaia-contract
 * @description: 更新签署任务状态--已签署
 * @author: 彭书强(檀凡)
 * @created: 2021/04/28 14:36
 */
@Function(name = "update SignTask status")
public interface UpdateSignTaskFunc {

    /**
     * 更新签署任务状态
     * @param signTaskBO
     */
    void execute(SignTaskBO signTaskBO);
}
