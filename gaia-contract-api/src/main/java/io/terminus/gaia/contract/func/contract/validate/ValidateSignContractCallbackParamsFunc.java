package io.terminus.gaia.contract.func.contract.validate;

import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * <AUTHOR>
 * @date 2021-04-16
 */
@Function(name = "validate sign contract callback params")
public interface ValidateSignContractCallbackParamsFunc {
    /**
     * 校验签署完成回调参数
     * @param contractBO 框架合同
     */
    void execute(ContractBO contractBO);
}
