package io.terminus.gaia.contract.tmodel.bpm;

import com.alibaba.fastjson.annotation.JSONField;
import io.terminus.trantorframework.api.RootModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.FieldType;
import io.terminus.trantorframework.api.annotation.TransientModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@TransientModel(name = "req message")
public class SubmitDalaranBaseTO extends RootModel<Long> {

    /**
     * DATA下根结点， 固定节点非空
     */
    @Field(name = "i request", type = FieldType.Json)
    @JSONField(name = "I_REQUEST")
    private IRequestTO iRequest;

}
