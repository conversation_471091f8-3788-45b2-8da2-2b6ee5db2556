package io.terminus.gaia.contract.func.contract.write;

import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.gaia.contract.model.contract.ContractEntityRelBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.flow.LF;
import io.terminus.trantorframework.sdk.sql.DS;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@FunctionImpl
public class EditContractMultiPartyFuncImpl implements EditContractMultiPartyFunc {
    @Override
    public void execute(ContractBO contractBO) {
        ContractBO previousContract = DS.findById(ContractBO.class, contractBO.getId());
        // 编辑前有三方签署的记录，则将原始记录全部删除
        if (previousContract.getIsMultiParty()) {
            List<ContractEntityRelBO> relations = DS.findAll(ContractEntityRelBO.class,
                    "*", "contract = ?",
                    contractBO.getId());
            if (CollectionUtils.isNotEmpty(relations)) {
                // 将原有的关联关系先全部删除
                DS.delete(relations);
            }
        }

        if (CollectionUtils.isEmpty(contractBO.getPartyCList()) || !contractBO.getIsMultiParty()) {
            return;
        }
        // 插入新的关联关系
        List<ContractEntityRelBO> relations = LF.map(contractBO.getPartyCList(), entityBO -> {
            ContractEntityRelBO contractEntityRelBO = new ContractEntityRelBO();
            contractEntityRelBO.setContract(contractBO);
            contractEntityRelBO.setEntity(entityBO);
            return contractEntityRelBO;
        });

        DS.create(relations);
    }
}
