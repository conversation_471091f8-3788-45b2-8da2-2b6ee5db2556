package io.terminus.gaia.app.b2b.contract.flow.contract.write;

import com.alibaba.fastjson.JSON;
import io.terminus.gaia.app.b2b.contract.func.material.convert.ConvertContractToMaterialApproveDataFunc;
import io.terminus.gaia.app.b2b.item.dict.item.MaterialsApprovalTypeDict;
import io.terminus.gaia.app.b2b.item.flow.materialslist.write.BatchApproveMaterialsFlow;
import io.terminus.gaia.app.b2b.item.tmodel.item.ItemApprovalTO;
import io.terminus.gaia.app.notice.flow.StationLetterSendFlow;
import io.terminus.gaia.app.notice.tmodel.StationLetterSenderTO;
import io.terminus.gaia.contract.dict.common.ApproveStatusDict;
import io.terminus.gaia.contract.dict.contract.ContractStatusDict;
import io.terminus.gaia.contract.flow.contract.write.PartyBRejectionFlow;
import io.terminus.gaia.contract.func.contract.validate.ValidateIsMasterContractFunc;
import io.terminus.gaia.contract.func.contract.write.PartyBRejectionFunc;
import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.gaia.contract.utils.JsonUtil;
import io.terminus.gaia.md.model.CurrencyBO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.FlowImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 乙方拒绝
 *
 * <AUTHOR>
 * @date 2021-04-21
 */
@RequiredArgsConstructor
@FlowImpl(name = "Party B Reject flow")
@Slf4j
public class PartyBRejectionFlowImpl implements PartyBRejectionFlow {

    private final PartyBRejectionFunc partyBRejectionFunc;

    private final ConvertContractToMaterialApproveDataFunc convertContractToMaterialApproveDataFunc;

    private final BatchApproveMaterialsFlow batchApproveMaterialsFlow;

    private final ValidateIsMasterContractFunc validateIsMasterContractFunc;

    private final StationLetterSendFlow stationLetterSendFlow;

    @Override
    public BooleanResult execute(ContractBO contractBO) {

        ContractBO existContractBo = DS.findOne(ContractBO.class,"*,operator.*", "id=?", contractBO.getId());

        //更新合同状态为已创建，记录拒绝原因
        partyBRejectionFunc.execute(existContractBo);
        //判断是否是主合同
        BooleanResult isMaster= validateIsMasterContractFunc.execute(existContractBo);
        //非主合同
        if(!isMaster.getValue()){

            //发送站内信
            letterSend(existContractBo);
            return BooleanResult.TRUE;

        }else {
            //组装材料审批数据
            ItemApprovalTO itemApprovalTO = convertContractToMaterialApproveDataFunc.execute(existContractBo);
            //更新材料清单状态
            itemApprovalTO.setApprovalType(MaterialsApprovalTypeDict.MATERIALS_APPLY_REFUSED);

            if (itemApprovalTO != null && !CollectionUtils.isEmpty(itemApprovalTO.getSkuBOExtList())) {
                batchApproveMaterialsFlow.execute(itemApprovalTO);
            }

            //发送站内信
            letterSend(existContractBo);
        }

        return BooleanResult.TRUE;

    }


    /**
     * 发送站内信
     * @param contractBO
     */
    private void letterSend(ContractBO contractBO) {
        log.info("【乙方拒绝同意】集采协议{}审核{},发送站内信",contractBO.getContractCode(),contractBO.getContractStatusDict().equals(ContractStatusDict.CONFIRMED)?"同意":"拒绝");

        try {
            StationLetterSenderTO stationLetterSenderTO = new StationLetterSenderTO();

            Map<String, String> templateMapping = new HashMap<>();
            templateMapping.put("contractName",getOperatorLetterUrlUtil(contractBO));

            //发送给经办人
            List<String> receiverIdList = new ArrayList<>();
            receiverIdList.add(String.valueOf(contractBO.getOperator().getId()));

            //拒绝
            stationLetterSenderTO.setTemplateCode("STATION_LETTER1638341154614");
            stationLetterSenderTO.setNoticeBusinessType("融创中国");
            stationLetterSenderTO.setTemplateMapping(templateMapping);
            stationLetterSenderTO.setReceiverIdList(receiverIdList);
            log.info("站内信输入参数:{}", JSON.toJSONString(stationLetterSenderTO));
            stationLetterSendFlow.execute(stationLetterSenderTO);
            log.info("【乙方拒绝同意】集采协议发送站内信给经办人成功");
        }catch (Exception e){
            log.error("【乙方拒绝同意】集采协议发送站内信给经办人失败");
        }
    }

    public static String getOperatorLetterUrlUtil(ContractBO contractBO) {
        try {
            String url = "/view/contract_ContractBO_B2bContractDetail?";
            ContractBO queryContractBo = new ContractBO();
            queryContractBo.setId(contractBO.getId());
            queryContractBo.setIsUseTemplate(contractBO.getIsUseTemplate());
            String queryEnv = URLEncoder.encode(JsonUtil.getJsonExcludeRootFields(queryContractBo), "UTF-8");

            BaseModel<Long> baseModel = new BaseModel();
            baseModel.setId(baseModel.getId());
            String id = URLEncoder.encode(JsonUtil.getJsonExcludeRootFields(baseModel), "UTF-8");


             url = "<a href='" + url  + "record=" + id  + "&env="+queryEnv+"' target='_blank'>" + contractBO.getContractName() + "</a>";
            return url;
        } catch (Exception e) {
            throw new BusinessException("编码出错");
        }
    }
}
