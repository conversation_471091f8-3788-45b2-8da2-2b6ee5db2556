package io.terminus.gaia.app.b2b.contract.func.contract.read;

import io.terminus.gaia.app.b2b.contract.tmodel.GetContractQuotaSettingReqTO;
import io.terminus.gaia.app.b2b.contract.tmodel.GetContractQuotaSettingRespTO;
import io.terminus.trantorframework.api.annotation.Function;

import java.util.List;

@Function
public interface GetContractQuotaSettingFunc {

    List<GetContractQuotaSettingRespTO> execute(List<GetContractQuotaSettingReqTO> reqTOList);

}
