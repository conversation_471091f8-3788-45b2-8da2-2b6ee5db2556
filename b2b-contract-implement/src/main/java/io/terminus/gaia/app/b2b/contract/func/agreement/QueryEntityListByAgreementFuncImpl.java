package io.terminus.gaia.app.b2b.contract.func.agreement;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import io.terminus.gaia.app.b2b.contract.model.AgreementBO;
import io.terminus.gaia.app.b2b.contract.model.AgreementDealerBO;
import io.terminus.gaia.app.b2b.contract.tmodel.EntityForAgreementTO;
import io.terminus.gaia.app.b2b.contract.tmodel.QueryEntityByAgreementTO;
import io.terminus.gaia.md.model.DistrictBO;
import io.terminus.gaia.organization.model.ProjectDetailBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;

import java.util.ArrayList;
import java.util.List;

@FunctionImpl
public class QueryEntityListByAgreementFuncImpl implements QueryEntityListByAgreementFunc {
    @Override
    public List<EntityForAgreementTO> execute(QueryEntityByAgreementTO queryEntityByAgreementTO) {
        if (ObjectUtil.isNull(queryEntityByAgreementTO)) {
            throw new BusinessException("入参不能为空");
        }
        if (ObjectUtil.isNull(queryEntityByAgreementTO.getAgreementCode())) {
            throw new BusinessException("入参协议编码不能为空");
        }
//        if (ObjectUtil.isNull(queryEntityByAgreementTO.getSiteCode())) {
//            throw new BusinessException("入参项目编码不能为空");
//        }
        // 初始化出参
        List<EntityForAgreementTO> entityForAgreementTOList = new ArrayList<>();

        AgreementBO agreementBO = DS.findOne(AgreementBO.class, "*, yfCompanyBO.*, yfCompanyBO.company.*", "code = ?", queryEntityByAgreementTO.getAgreementCode());
        if (ObjectUtil.isNull(agreementBO)) {
            throw new BusinessException("协议不存在");
        }
        if (ObjectUtil.isNotNull(agreementBO.getYfCompanyBO())) {
            EntityForAgreementTO main = new EntityForAgreementTO();
            main.setId(agreementBO.getYfCompanyBO().getId());
            main.setEntityCode(agreementBO.getYfCompanyBO().getEntityCode());
            main.setEntityName(agreementBO.getYfCompanyBO().getEntityName());
            main.setEntityShortName(agreementBO.getYfCompanyBO().getEntityShortName());
            main.setSocialCreditCode(agreementBO.getYfCompanyBO().getCompany().getSocialCreditCode());
            main.setIsMain(true);
            if (StrUtil.isNotBlank(queryEntityByAgreementTO.getEntityName())) {
                if (main.getEntityName().contains(queryEntityByAgreementTO.getEntityName())) {
                    entityForAgreementTOList.add(main);
                }
            } else {
                entityForAgreementTOList.add(main);

            }
        }
        List<AgreementDealerBO> agreementDealerBOList = new ArrayList<>();
        if (ObjectUtil.isNull(queryEntityByAgreementTO.getSiteCode())) {
            agreementDealerBOList = DS.findAll(AgreementDealerBO.class, "*, dealer.*, dealer.company.*" , "agreementBO = ?", agreementBO.getId());
        } else {
            ProjectDetailBO projectDetailBO = DS.findOne(ProjectDetailBO.class, "*", "id = ?", queryEntityByAgreementTO.getSiteCode());
            if (ObjectUtil.isNotNull(projectDetailBO) && ObjectUtil.isNotNull(projectDetailBO.getAreaCode())) {
                DistrictBO districtBO = DS.findOne(DistrictBO.class, "*", "districtCode = ?", projectDetailBO.getAreaCode());
                String[] idPath = districtBO.getIdPath().split("/");
                if (ObjectUtil.isNotNull(idPath)) {
                    agreementDealerBOList = DS.findAll(AgreementDealerBO.class, "*, dealer.*, dealer.company.*", "agreementBO = ? and region = ?", agreementBO.getId(), idPath[0]);
                }
            }
        }
        if (CollectionUtil.isNotEmpty(agreementDealerBOList)) {
            agreementDealerBOList.forEach(agreementDealerBO -> {
                EntityForAgreementTO dealer = new EntityForAgreementTO();
                dealer.setId(agreementDealerBO.getDealer().getId());
                dealer.setEntityCode(agreementDealerBO.getDealer().getEntityCode());
                dealer.setEntityName(agreementDealerBO.getDealer().getEntityName());
                dealer.setEntityShortName(agreementDealerBO.getDealer().getEntityShortName());
                dealer.setSocialCreditCode(agreementDealerBO.getDealer().getCompany().getSocialCreditCode());
                dealer.setIsMain(false);
                if (StrUtil.isNotBlank(queryEntityByAgreementTO.getEntityName())) {
                    if (dealer.getEntityName().contains(queryEntityByAgreementTO.getEntityName())) {
                        entityForAgreementTOList.add(dealer);
                    }
                } else {
                    entityForAgreementTOList.add(dealer);
                }
            });
        }

        return entityForAgreementTOList;
    }
}
