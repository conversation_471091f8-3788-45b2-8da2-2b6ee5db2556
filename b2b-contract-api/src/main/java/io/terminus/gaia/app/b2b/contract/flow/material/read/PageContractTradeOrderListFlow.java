package io.terminus.gaia.app.b2b.contract.flow.material.read;

import io.terminus.gaia.app.b2b.item.model.query.QSkuBOExt;
import io.terminus.gaia.app.b2b.trade.model.contract.B2bContractOrderExtBO;
import io.terminus.gaia.app.b2b.trade.model.contract.query.QB2bContractOrderExtBO;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.api.annotation.Flow;

/**
 * @Author: fengbo
 * @Date: 2021/10/13
 */
@Flow(name = "page contract trade order list flow")
public interface PageContractTradeOrderListFlow {
    Paging<B2bContractOrderExtBO> execute(QB2bContractOrderExtBO qTradeContract);
}
