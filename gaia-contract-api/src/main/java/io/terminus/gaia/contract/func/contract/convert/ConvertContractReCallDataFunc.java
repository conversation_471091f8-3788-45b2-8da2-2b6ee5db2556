package io.terminus.gaia.contract.func.contract.convert;

import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * 合同TO模型convert合同BO模型
 * @Author: fengbo
 * @Date: 2021/8/18
 */
@Function(name = "convert contract recall data")
public interface ConvertContractReCallDataFunc {
    /**
     * 撤回合同数据转换
     *
     * @param contract 框架合同
     * @return 框架合同
     */
    ContractBO execute(ContractBO contract);
}
