package io.terminus.gaia.contract.func.supply.validate;

import io.terminus.gaia.contract.BaseService;
import io.terminus.gaia.contract.dict.supply.QuotationStatusDict;
import io.terminus.gaia.contract.model.supply.QuotationBO;
import io.terminus.gaia.contract.util.QuotationFlowHolder;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.util.ModelUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-04-23
 */
public class ValidateBatchDisableQuotationParamsFuncTest extends BaseService {
    ValidateBatchDisableQuotationParamsFunc validateBatchDisableQuotationParamsFunc;
    List<QuotationBO> quotationList;
    @Before
    public void setUp() throws Exception {
        quotationList = new ArrayList<>();
        QuotationBO quotation = ModelUtils.clone(QuotationFlowHolder.quotation);
//        quotation.setQuotationStatus(QuotationStatusDict.ENABLED);
        quotationList.add(quotation);
    }

    @Test
    public void execute() {
        Assert.assertThrows(BusinessException.class, () ->
        validateBatchDisableQuotationParamsFunc.execute(quotationList));
    }
}