package io.terminus.gaia.app.b2b.contract.flow.contract.write;

import io.terminus.gaia.app.b2b.contract.func.contract.convert.ConvertSyncAgreementToSapDataFunc;
import io.terminus.gaia.app.b2b.contract.func.contract.write.PushAgreementToDalaranFunc;
import io.terminus.gaia.app.b2b.contract.tmodel.sync.PushToSapAgreementTO;
import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FlowImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: fengbo
 * @Date: 2021/9/26
 */
@Slf4j
@RequiredArgsConstructor
@FlowImpl(name = "push agreement to sap flow impl")
public class PushAgreementToSapFlowImpl implements PushAgreementToSapFlow {

    private final ConvertSyncAgreementToSapDataFunc convertSyncAgreementToSapDataFunc;

    private final PushAgreementToDalaranFunc pushAgreementToDalaranFunc;

    @Override
    public BooleanResult execute(ContractBO contractBO) {
        log.info("开始推送sap,contractBO:{}", contractBO.toString());
        ContractBO pushContract = DS.findById(ContractBO.class, contractBO.getId());
        //合同数据不存在
        if (pushContract == null || pushContract.getId() == null) {
//            throw new BusinessException(ContractExMsg.CONTRACT_IS_NULL);
            log.warn("can not find contract by id {} ", contractBO.getId());
            return BooleanResult.TRUE;
        }

        //补充协议则不推送sap
//        if(pushContract.getIsSupplementContract()){
//            return BooleanResult.TRUE;
//        }
        PushToSapAgreementTO pushToSapAgreementTO = convertSyncAgreementToSapDataFunc.execute(contractBO);
        log.info("推送sap,pushToSapAgreementTO:{}", pushToSapAgreementTO.toString());
        //推送数据到dalaran
        pushAgreementToDalaranFunc.execute(pushToSapAgreementTO);
        return BooleanResult.TRUE;
    }
}
