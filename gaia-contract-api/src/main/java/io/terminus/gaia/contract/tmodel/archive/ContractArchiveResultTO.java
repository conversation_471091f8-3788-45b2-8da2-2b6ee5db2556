package io.terminus.gaia.contract.tmodel.archive;

import io.terminus.trantorframework.api.RootModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.TransientModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@TransientModel(name = "Contract Archive Result TO")
@Data
public class ContractArchiveResultTO extends RootModel<Long> {

    /**
     * 结果描述
     * */
    @Field(name = "message")
    private String message;

    /**
     * 是否成功
     * */
    @Field(name = "is success")
    private Boolean isSuccess;


}
