package io.terminus.gaia.contract.func.entity.read;

import io.terminus.gaia.md.model.EntityBO;
import io.terminus.trantorframework.api.annotation.Function;

import java.util.List;

/**
 * 获取供应商下属的经销商信息
 *
 * @Author: fengbo
 * @Date: 2021/8/22
 */
@Function
public interface GetDealerEntityFunc {

    /**
     * 获取供应商下属的经销商信息
     *
     * @param entityBO
     * @return 主体列表
     */
    List<EntityBO> execute(EntityBO entityBO);

}
