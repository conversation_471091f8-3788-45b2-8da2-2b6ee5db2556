package io.terminus.gaia.app.b2b.contract.func.agreement.write;

import io.terminus.gaia.app.b2b.contract.model.AgreementDetailBO;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseLineBO;
import io.terminus.gaia.app.b2b.contract.func.purchase.CheckAllInAgreementFunc;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * @class_name: ManualMatchingAgreementFuncImpl
 * @desc: 手动匹配协议
 * @date: 2025/6/26 : 11:31
 * @author: Chonor
 **/
@FunctionImpl
@Slf4j
@RequiredArgsConstructor
public class ManualMatchingAgreementFuncImpl implements ManualMatchingAgreementFunc{

    private final MatchSalePriceFunc matchSalePriceFunc;
    private final CheckAllInAgreementFunc checkAllInAgreementFunc;

    @Override
    public BooleanResult execute(RequestPurchaseLineBO requestPurchaseLineBO) {

        final Long id = requestPurchaseLineBO.getId();
        if(Objects.isNull(id)){
            throw new BusinessException("请选择要匹配的需求明细");
        }

        final AgreementDetailBO agreementDetailBO = requestPurchaseLineBO.getAgreementDetailBO();
        if(Objects.isNull(agreementDetailBO)){
            throw new BusinessException("请选择要匹配的协议");
        }

        final RequestPurchaseLineBO requestPurchaseLineBOFromDB = DS.findById(RequestPurchaseLineBO.class, id);
        final AgreementDetailBO agreementDetailBOFromDB = DS.findById(AgreementDetailBO.class, agreementDetailBO.getId(), "*,spuBO.*,agreementBO.*,brandBo.id,saleEntityBO.*");

        RequestPurchaseLineBO update = new RequestPurchaseLineBO();
        update.setId(id);
//        update.setBrandBO(agreementDetailBOFromDB.getBrandBo());
        update.setSupplier(agreementDetailBOFromDB.getSaleEntityBO());
        update.setSpu(agreementDetailBOFromDB.getSpuBO());
        update.setAgreementDetailBO(agreementDetailBOFromDB);
        update.setAgreement(agreementDetailBOFromDB.getAgreementBO());
        update.setInAgreement(true); // 匹配到协议，设置为true

        //计算金额

        // 采购含铜量
        final BigDecimal purRawMaterialContent = (agreementDetailBOFromDB.getSpuBO() != null && agreementDetailBOFromDB.getSpuBO().getRawMaterialContent() != null) ? 
                agreementDetailBOFromDB.getSpuBO().getRawMaterialContent() : BigDecimal.ZERO;
        update.setPurRawMaterialContent(purRawMaterialContent);

        // 采购铜基价
        final BigDecimal purCopperBasicPrice = requestPurchaseLineBOFromDB.getPurCopperBasicPrice() != null ? 
                requestPurchaseLineBOFromDB.getPurCopperBasicPrice() : BigDecimal.ZERO;

        // 采购辅材及其他价格
        final BigDecimal purOtherCosts = agreementDetailBOFromDB.getOtherCosts() != null ? 
                agreementDetailBOFromDB.getOtherCosts() : BigDecimal.ZERO;
        update.setPurOtherCosts(purOtherCosts);
        // 销售辅材及其他价格，给默认值 = 采购辅材及其他价格
        update.setSaleOtherCosts(purOtherCosts);

        // 采购折扣系数
        final BigDecimal purDiscountFactor = agreementDetailBOFromDB.getPurDiscountFactor() != null ? 
                agreementDetailBOFromDB.getPurDiscountFactor() : BigDecimal.ZERO;
        update.setPurDiscountFactor(purDiscountFactor);

        // 采购延米铜价 = （含铜量*铜基价/1000）
        final BigDecimal purCopperPrice = purRawMaterialContent.multiply(purCopperBasicPrice).divide(new BigDecimal("1000"), 6, RoundingMode.HALF_UP);
        update.setPurCopperPrice(purCopperPrice);
        // 销售延米铜价 = 采购延米铜价
        update.setSaleCopperPrice(purCopperPrice);

        // 采购含税单价 = （铜基价*标品含铜量/1000+辅材价格）*采购折扣系数
        final BigDecimal purTaxPrice = purRawMaterialContent.multiply(purCopperBasicPrice).divide(new BigDecimal("1000"), 6, RoundingMode.HALF_UP).add(purOtherCosts).multiply(purDiscountFactor);
        update.setPurTaxPrice(purTaxPrice);

        // 匹配销售测算
        final BooleanResult matchSalePrice = matchSalePriceFunc.execute(update);
        if (!matchSalePrice.getValue()) {
            // 如果销售测算匹配失败，抛出业务异常
            String agreementName = agreementDetailBOFromDB.getAgreementBO() != null ? 
                    agreementDetailBOFromDB.getAgreementBO().getName() : "未知协议";
            log.warn("销售测算匹配失败，需求明细ID: {}, 协议名称: {}", id, agreementName);
            throw new BusinessException("当前协议清单没有销售测算数据，请补充后重新匹配协议！");
        }

        DS.update(update);

        // 检查需求单是否所有明细都匹配到协议
        if (requestPurchaseLineBOFromDB.getRequestPurchaseBO() != null && requestPurchaseLineBOFromDB.getRequestPurchaseBO().getId() != null) {
            checkAllInAgreementFunc.execute(requestPurchaseLineBOFromDB.getRequestPurchaseBO());
        }

        log.info("手动匹配协议成功，需求明细ID: {}", id);
        return BooleanResult.TRUE;
    }
}
