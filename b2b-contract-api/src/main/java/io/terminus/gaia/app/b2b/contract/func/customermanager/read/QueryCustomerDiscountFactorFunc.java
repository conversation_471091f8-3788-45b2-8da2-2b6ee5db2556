package io.terminus.gaia.app.b2b.contract.func.customermanager.read;

import io.terminus.gaia.app.b2b.contract.tmodel.QueryCustomerDiscountFactorTO;
import io.terminus.gaia.app.b2b.trade.tmodel.BigDecimalResult;
import io.terminus.trantorframework.api.annotation.Function;

@Function
public interface QueryCustomerDiscountFactorFunc {

    BigDecimalResult execute(QueryCustomerDiscountFactorTO request);
}
