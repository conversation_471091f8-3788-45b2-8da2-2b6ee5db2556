package io.terminus.gaia.contract.func.sign.write;

import io.terminus.gaia.contract.model.sign.SignatoriesBO;
import io.terminus.gaia.contract.tmodel.sign.SignatoriesSignTO;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * 根据签署方整合签署信息
 *
 * <AUTHOR>
 */
@Function(name = "Get signatoriesBO sign function")
public interface GetSignatoriesSignFunc {
    /**
     * 根据签署方整合签署信息
     * @param signatoriesBO 签署方
     * @return 签署信息
     */
    SignatoriesSignTO execute(SignatoriesBO signatoriesBO);
}
