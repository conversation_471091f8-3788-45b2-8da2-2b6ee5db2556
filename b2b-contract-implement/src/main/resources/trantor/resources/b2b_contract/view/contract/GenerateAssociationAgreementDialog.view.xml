<?xml version="1.0" encoding="UTF-8"?>
<View title="复制协议" type="List" forModel="contract_ContractBO" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://trantor-docs-dev.app.terminus.io/static/v0.16.x/schema/base.xsd">
    <Form key="contract_Contract" model="contract_ContractBO">
        <Fields>
            <Field label="协议主体" name="partyAList" >
                <RenderType>
                    <ModelSelect dataFunc="contract_GetEntityByEntityRoleFunc"
                                 dataParams="#{{ roleType: {values: ['Z006']} }}"
                                 optionFormat="#{option => option.entityName}"/>
                </RenderType>
                <Validations>
                    <Validation required="#{true}" message="please select partyA"/>
                </Validations>
            </Field>
            <Field name="id" show="#{false}" initValue="#{env.id}" submit="#{true}"/>
            <Field name="partyA" show="#{false}" initValue="#{env.partyA}" submit="#{true}"/>
            <Field name="contractCode" show="#{false}" submit="#{true}" initValue="#{env.contractCode}"/>
            <Field name="contractCode" show="#{false}" submit="#{true}" initValue="#{env.contractCode}"/>
            <Field name="isNotBOM" show="#{false}" submit="#{true}" initValue="#{env.isNotBOM}"/>
        </Fields>
        <Actions>
            <Action label="取消" action="GoBack" layout="Footer"/>
            <Action type="Submit" label="确认" logicFlow="contract_CreateLinkAgreementFlow"
                    validator="#{validator}"
                    after="GoBack"
                    layout="Footer"/>
        </Actions>
    </Form>
</View>