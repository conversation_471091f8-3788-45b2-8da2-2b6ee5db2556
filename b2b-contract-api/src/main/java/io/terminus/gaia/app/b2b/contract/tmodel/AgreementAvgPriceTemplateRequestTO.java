package io.terminus.gaia.app.b2b.contract.tmodel;

import io.terminus.gaia.app.b2b.contract.model.AgreementAvgPriceBO;
import io.terminus.trantorframework.api.RootModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.FieldType;
import io.terminus.trantorframework.api.annotation.TransientModel;
import io.terminus.trantorframework.api.annotation.typemeta.LinkMeta;
import lombok.Data;

@Data
@TransientModel
public class AgreementAvgPriceTemplateRequestTO extends RootModel<Long> {

    @Field(name = "协议清单", type = FieldType.Json)
    private SpuAgreementTO spuAgreementTO;

    @Field(name = "协议平均价")
    @LinkMeta
    private AgreementAvgPriceBO agreementAvgPrice;

    @Field(name = "上传文件url")
    private String importFile;
}
