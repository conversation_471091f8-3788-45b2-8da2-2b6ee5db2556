/**
 *  controller template generated by trantor devtools
 *
 */
import { Controller, Toast, state, showMessage } from 'nusi-sdk'

export default class extends Controller {


    constructor() {
        super()
    }

    public onChange() {
        // TODO write your code here
    }

    nextSteps = async (ctx) => {

        const container = this.getContainerByKey('table');
        const idList = container.data.map(item => item.id);
        if (idList.length === 0) {
            Toast.error("至少添加一个协议");
            return false;
        }

        const categoryId = container.data[0].category.id; // 获取第一条数据的分类 ID
        const hasDifferentCategoryId = !container.data.every(item => item.category.id === categoryId);
        if (hasDifferentCategoryId) {
          Toast.error("仅可选择相同分类的协议");
          return false;
        }

        const idString = idList.join(',');
        const data = {
            "ids": idString
        }
        this.openView('b2b_contract_SpuAgreementTO_EnsureAgreementSpu', {
            openViewType: 'Self',
            record: data,
            env: { source: this.env.source }
        })

    }
}