package io.terminus.gaia.app.b2b.contract.spring.validator;

import cn.hutool.core.collection.ListUtil;
import io.terminus.gaia.app.b2b.contract.dict.AgreementDetailStatusDict;
import io.terminus.gaia.app.b2b.contract.dict.AgreementStatusDict;
import io.terminus.gaia.app.b2b.contract.model.AgreementBO;
import io.terminus.gaia.app.b2b.contract.model.AgreementDetailBO;
import io.terminus.gaia.item.model.spu.SpuBO;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class AgreementDetailValidator {

    /**
     * 同一协议下的spu不能重复
     *
     * @param bindSpu
     * @param allDetails
     */
    public void checkBindSpu(SpuBO bindSpu, List<AgreementDetailBO> allDetails) {
        Map<Long, List<AgreementDetailBO>> detailGroupMap = allDetails.stream().filter(e -> e.getAgreementBO() != null).collect(Collectors.groupingBy(e -> e.getAgreementBO().getId()));
        detailGroupMap.forEach((agreementId, partDetails) -> {
            List<Long> curSpuIds = partDetails.stream().filter(e -> e.getSpuBO() != null).map(e -> e.getSpuBO().getId()).collect(Collectors.toList());
            List<AgreementDetailBO> existDetails = DS.findAll(AgreementDetailBO.class, "*",
                    "statusDict != 'LOSE_EFFICACY' AND loseReason != 'manual' AND agreementBO= ?", agreementId);
            List<Long> existSpuIds = existDetails.stream().filter(abs -> Objects.nonNull(abs.getSpuBO())).map(x -> x.getSpuBO().getId()).collect(Collectors.toList());
            existSpuIds.removeAll(curSpuIds);
            if (existSpuIds.contains(bindSpu.getId())) {
                throw new BusinessException("清单对应的框架协议下已经存在其他清单绑定该spu!");
            }
        });
    }

    /**
     * 协议清单启用校验
     */
    public void checkEnableAgreementDetail(AgreementDetailBO detailBO, AgreementBO existAgreement ) {
        List<String> agreementExpects = ListUtil.toList(AgreementStatusDict.ENABLED);
        if (!agreementExpects.contains(existAgreement.getStatus())) {
            throw new BusinessException("仅【启用】状态的协议可以启用协议清单");
        }
        List<String> expects = ListUtil.toList(AgreementDetailStatusDict.STOP_USING);
        if (!expects.contains(detailBO.getStatusDict())) {
            throw new BusinessException("仅【停用】状态的协议可以启用协议清单");
        }
    }

    /**
     * 协议清单禁用校验
     */
    public void checkDisableAgreementDetail(AgreementDetailBO detailBO) {


        List<String> expects = ListUtil.toList(AgreementDetailStatusDict.START_USING);
        if (!expects.contains(detailBO.getStatusDict())) {
            throw new BusinessException("仅【启用】状态的协议可以停用协议清单");
        }
    }
}
