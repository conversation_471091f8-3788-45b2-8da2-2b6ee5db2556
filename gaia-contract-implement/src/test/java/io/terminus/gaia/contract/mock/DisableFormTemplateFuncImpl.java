package io.terminus.gaia.contract.mock;

import io.terminus.gaia.partner.func.form.write.DisableFormTemplateFunc;
import io.terminus.gaia.partner.model.form.FormTemplateBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/9
 */
@FunctionImpl
public class DisableFormTemplateFuncImpl implements DisableFormTemplateFunc {

    @Override
    public void execute(@Valid List<FormTemplateBO> formTemplateBOS) {

    }
}
