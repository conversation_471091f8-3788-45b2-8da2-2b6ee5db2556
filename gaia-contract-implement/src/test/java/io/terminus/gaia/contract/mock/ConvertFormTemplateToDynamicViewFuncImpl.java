package io.terminus.gaia.contract.mock;

import io.terminus.gaia.partner.func.form.convert.ConvertFormTemplateToDynamicViewFunc;
import io.terminus.gaia.partner.model.form.FormTemplateBO;
import io.terminus.trantor.module.base.dynamicview.DynamicView;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FunctionImpl;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> 韩俊文(柚屿)
 **/
@FunctionImpl
public class ConvertFormTemplateToDynamicViewFuncImpl implements ConvertFormTemplateToDynamicViewFunc {
    @Override
    public List<DynamicView> execute(@Valid FormTemplateBO formTemplateBO, @Valid BooleanResult isDetailView) {
        return Collections.emptyList();
    }
}
