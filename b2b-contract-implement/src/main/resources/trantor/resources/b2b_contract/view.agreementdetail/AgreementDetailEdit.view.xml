<?xml version="1.0" encoding="UTF-8"?>
<View title="编辑协议清单" forModel="b2b_contract_AgreementDetailBO" type="Form" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://trantor-docs-dev.app.terminus.io/static/v0.16.x/schema/base.xsd">
    <Form key="agreementDetail" model="b2b_contract_AgreementDetailBO"
          dataCondition="id = ?" dataParams="[pageContext.record.id]" onFieldChange="#{mainModelChange}"
          onDataLoaded="#{onDataLoaded}">
        <Fields>
            <GroupField title="供应信息">
                <Field name="agreementBO.id" show="#{false}"/>
                <Field name="agreementBO.name" label="框架协议" readonly="#{true}"/>
                <Field name="saleEntityBO" show="#{false}"/>
                <Field name="saleEntityBO.entityName" label="供应商" readonly="#{true}"/>
            </GroupField>
            <GroupField title="标品信息">
                <Field name="spuBO" readonly="#{!this.data.agreementBO}" tips="请先选择协议">
                    <Validations>
                        <Validation required="#{true}" message="标品不能为空"/>
                    </Validations>
                    <RenderType>
                        <ModelSelect targetView="item_SpuBO_SingleSelectAgreementSpu"
                                     env="#{{agreementId:this.data.agreementBO.id}}"/>
                    </RenderType>
                </Field>
                <Field label="关联分类" name="spuBO.category.path" readonly="#{true}" displayOnly="#{true}"/>
                <Field name="spuBO.category" show="#{false}" displayOnly="#{true}"/>
                <Field name="spuBO.category.bulkCategory" show="#{false}" displayOnly="#{true}"/>
                <Field name="spuBO.thing.thingName" label="关联物料" readonly="#{true}" displayOnly="#{true}"/>
                <Field name="spuBO.name" label="名字" show="#{false}" displayOnly="#{true}"/>
                <Field name="spuBO.spuCode" label="编码" show="#{false}" displayOnly="#{true}"/>
                <Field name="spuBO.thingMaterial" label="材质" readonly="#{true}" displayOnly="#{true}"/>
                <Field name="spuBO.thingSize" label="规格型号" readonly="#{true}" displayOnly="#{true}"/>
                <Field name="spuBO.thing.unit.unitName" label="计量单位" readonly="#{true}" displayOnly="#{true}"/>
                <Field name="spuBO.attributes" label="属性" readonly="#{true}" displayOnly="#{true}">
                    <RenderType>
                        <Text format="#{formatAttributes}"/>
                    </RenderType>
                </Field>
            </GroupField>

            <GroupField title="价格信息">
                <Field name="brands" show="#{false}"/>
                <Field label="品牌" name="brandBo">
                    <Validations>
                        <Validation required="#{this.data.spuBO != null &amp;&amp; this.data.spuBO.category.bulkCategory === true}" message="请填写品牌"/>
                    </Validations>
                    <RenderType>
                        <ModelSelect dataCondition="`brandStatus` = 'ENABLED'"/>
                    </RenderType>
                </Field>
                <Field label="税率" name="taxRate">
                    <Validations>
                        <Validation required="true" message="请填写税率"/>
                    </Validations>
                    <RenderType>
                        <ModelSelect dataFunction="md_PagingTaxRateBOBuiltInFunc"
                                     dataParams="{businessType:{type:'Collection',values:['PURCHASE']},taxRateStatus:'ENABLED'}"/>
                    </RenderType>
                </Field>
                <Field label="不含税价" name="supplyInfo.price" readonly="#{true}">
                    <RenderType>
                        <Number format="0.[0000]"/>
                    </RenderType>
                </Field>
                <Field label="含税价" name="supplyInfo.taxPrice">
                    <RenderType>
                        <InputNumber digits="4" fullWidth="true"/>
                    </RenderType>
                    <Validations>
                        <Validation required="true" message="请填写含税价"/>
                    </Validations>
                </Field>
                <Field name="supplyInfo.quotationBasis" label="报价依据"/>
                <Field label="价格方案" name="priceSchemeBO" readonly="#{!this.data.agreementBO}" tips="请先选择协议" show="#{this.record.agreementBO.type!=='match_making'}">
                    <RenderType>
                        <ModelSelect targetView="item_PriceSchemeBO_SingleSelectAgreementPriceScheme"
                                     env="#{{spuId:this.data.spuBO.id,agreementId:this.data.agreementBO.id,agreementType:this.data.agreementBO.type}}"/>
                    </RenderType>
                </Field>
                <Field name="spuBO.rawMaterialContent" label="铜含量（kg/m）" show="#{this.data.spuBO != null &amp;&amp; this.data.spuBO.category.bulkCategory === true}" readonly="true"/>
                <Field name="copperBasicPrice" label="参考铜基价" show="#{this.data.spuBO != null &amp;&amp; this.data.spuBO.category.bulkCategory === true}" readonly="true"/>
                <Field name="yanmiCopperPrice" label="延米铜价" show="#{this.data.spuBO != null &amp;&amp; this.data.spuBO.category.bulkCategory === true}" readonly="true"/>
                <Field name="otherCosts" label="辅材及其他费用" show="#{this.data.spuBO != null &amp;&amp; this.data.spuBO.category.bulkCategory === true}">
                    <Validations>
                        <Validation required="#{true}" message="辅材及其他费用不能为空"/>
                        <Validation validator="#{(rule, otherCosts, callback) => {if(otherCosts &amp;&amp; otherCosts &lt; 0)callback('辅材及其他费用必须大于0')}}"/>
                    </Validations>
                </Field>
                <Field name="purDiscountFactor" label="采购折扣系数" show="#{this.data.spuBO != null &amp;&amp; this.data.spuBO.category.bulkCategory === true}">
                    <Validations>
                        <Validation required="#{true}" message="采购折扣系数不能为空"/>
                        <Validation validator="#{(rule, purDiscountFactor, callback) => {if(purDiscountFactor != null &amp;&amp; (purDiscountFactor &gt; 1 || purDiscountFactor &lt; 0))callback('采购折扣系数大于0且小于等于1')}}" />
                    </Validations>
                </Field>
                <Field label="供货周期(天)" name="supplyInfo.supplyPeriod">
                    <RenderType>
                        <Number min="0" max="999"/>
                    </RenderType>
<!--                    <Validations>-->
<!--                        <Validation required="true" message="请填写供应信息"/>-->
<!--                    </Validations>-->
                </Field>
            </GroupField>
            <GroupField title="备注">
                <Field label="备注" name="agreementRemark"/>
                <Field label="排序索引" name="sortIndex"/>
            </GroupField>
            <Field name="agreementBO.type" show="#{false}"/>
            <Field name="statusDict" show="#{false}"/>
        </Fields>
        <Actions>
            <Action label="设置价格方案" action="#{setPriceScheme}" layout="Header" type="Submit"
                    show="#{(this.record.statusDict === 'START_USING' || this.record.statusDict === 'STOP_USING') &amp;&amp; this.record?.agreementBO?.type==='match_making'}"/>
            <Action label="取消" action="GoBack" layout="Footer"/>
            <Action type="Submit" label="保存" logicFunc="b2b_contract_AgreementDetailEditFunc"
                    after="GoBack" layout="Footer"/>
        </Actions>
    </Form>
</View>
