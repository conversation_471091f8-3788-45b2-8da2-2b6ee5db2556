package io.terminus.gaia.app.b2b.contract.tmodel;

import io.terminus.gaia.md.model.EntityBO;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.FieldType;
import io.terminus.trantorframework.api.annotation.TransientModel;
import io.terminus.trantorframework.api.annotation.typemeta.LookupMeta;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2025/7/7 17:08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TransientModel(name = "AskSupplierPriceReplyTO")
public class AskSupplierPriceReplyTO extends BaseModel<Long> {

    @Field(name = "询价单id")
    private Long askPriceId;

    @Field(name = "报价轮次")
    private Integer round;

    @Field(name = "驳回原因")
    private String rejectReason;

    @Field(name = "供应商")
    private String supplierNames;

    @Field(name = "供应商", type = FieldType.Json)
    @LookupMeta
    private List<Long> supplierIds;

    @Field(name = "供应商", type = FieldType.Json)
    @LookupMeta
    private List<EntityBO> entityBOList;
}
