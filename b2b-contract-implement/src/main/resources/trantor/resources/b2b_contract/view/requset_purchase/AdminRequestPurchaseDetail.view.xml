<?xml version="1.0" encoding="UTF-8" ?>
<View title="需求池详情" forModel="b2b_contract_RequestPurchaseBO" type="Detail" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/xsi/0.17.x/base.xsd">
    <Detail key="RequestPurchase" title="基本信息" model="b2b_contract_RequestPurchaseBO" dataCondition="id = ?" dataParams="[pageContext.record.id]" >
        <Fields>
            <Field name="id" show="false"/>
            <Field name="code"/>
            <Field name="name"/>
            <Field label="项目名称" name="projectBO" />
            <Field label="项目所属组织" name="departmentBO"/>
            <Field label="需求品类" name="categoryBO.categoryName"/>
            <Field label="品牌" name="brandBOList" show="#{false}"/>
            <Field label="铜基价" name="basicPriceBO.priceWithTax" />
            <Field label="备注" name="remark"/>
            <Field label="提报人" name="createdBy"/>
            <Field label="状态" name="handleUser"/>
            <Field name="allInAgreement" show="false"/>
            <Field name="winSupplierBO" show="false"/>
        </Fields>
        <Actions>
            <Action label="返回" action="Close" layout="Footer"/>
            <Action label="协议外商品转询价单" confirm="是否确认转询价单？？" 
                    logicFunc="b2b_contract_RequestPurchaseToAskPriceFunc" 
                    after="Refresh" 
                    show="#{this.data.allInAgreement == null || this.data.allInAgreement === false}" 
                    layout="Footer"/>
            <Action label="设置中标供应商" layout="Footer"
                    after="Refresh"
                    show="#{this.data.allInAgreement === true &amp;&amp; this.data.winSupplierBO == null}"
                    openViewType="Dialog" 
                    openViewSize="s"
                    targetView="b2b_contract_RequestPurchaseBO_SetWinSupplierDialog"/>
        </Actions>
    </Detail>

    <Detail key="test" title="需求明细" model="b2b_contract_RequestPurchaseBO" mute="#{true}">
        <Actions>
            <Action label="导出明细" action="" />
            <Action label="一键匹配协议" action="#{autoMatchingAgreement}" confirm="是否确认一键匹配协议？" />
            <Action label="手动匹配协议" action="#{manualMatchingAgreement}" />
            <Action label="手动匹配标品" action="#{manualMatchingSpu}" />
            <Action label="利润测算" targetView="b2b_contract_RequestPurchaseBO_AdminRequestPurchaseProfit"/>
<!--            <Action label="导出明细" action="#{exportProfit}" />-->
<!--            <Action label="导入销售价" action="" openViewType="Dialog" openViewSize="xs"-->
<!--                    targetView="b2b_contract_RequestPurchaseProfitUploadTO_RequestPurchaseProfitUpload"-->
<!--                    payloadCallback="#{getUploadProfitPayloadCallback('RequestPurchaseLine')}" />-->
        </Actions>
    </Detail>

    <frontend_MultiTabs
            key="multiTabs"
            showSwitch="#{true}"
            enableCheck="#{true}"
            funcKey="b2b_contract_QueryBrandRequestPurchaseLineFunc"
            columns="#{columns}"
            extraParams="#{{requestPurchaseBO: {id: getContainerByKey('RequestPurchase')?.data?.id}, brandBO: {id: '$tabKey'}}}"
            tabs="#{getContainerByKey('RequestPurchase')?.data?.brandBOList?.map(el => ({title: el.brandName, key: el.id})) || []}"
    />

<!--    <Table title="需求明细" model="b2b_contract_RequestPurchaseLineBO" key="RequestPurchaseLine"-->
<!--           dataFunction="b2b_contract_QueryBrandRequestPurchaseLineFunc" dataParams="{requestPurchaseBO:{id:pageContext.record.id}, brandBO:{id:526548}}" >-->
<!--        <Fields>-->
<!--            <Field name="brandBO.brandName" label="品牌"/>-->
<!--            <Field name="needLineName" label="材料名称"/>-->
<!--            <Field name="thingSizeDesc" label="规格型号"/>-->
<!--            <Field name="needNum" label="数量"/>-->
<!--            <Field name="unit.unitName" label="计量单位"/>-->
<!--            <Field name="purTaxPrice" label="采购含税单价">-->
<!--                <RenderType>-->
<!--                    <Currency format="0,0.00" digits="2"/>-->
<!--                </RenderType>-->
<!--            </Field>-->
<!--            <Field name="saleOtherCosts" label="销售辅材及其他价格">-->
<!--                <RenderType>-->
<!--                    <Currency format="0,0.00" digits="2"/>-->
<!--                </RenderType>-->
<!--            </Field>-->
<!--            <Field name="saleDiscountFactor" label="销售折扣系数">-->
<!--                <RenderType>-->
<!--                    <InputNumber precision="4"/>-->
<!--                </RenderType>-->
<!--            </Field>-->
<!--            <Field name="saleTaxPrice" label="销售含税单价">-->
<!--                <RenderType>-->
<!--                    <Currency format="0,0.00" digits="2"/>-->
<!--                </RenderType>-->
<!--            </Field>-->
<!--            <Field name="profitRate" label="销售利润率">-->
<!--                <RenderType>-->
<!--                    <InputNumber precision="2" suffix="%"/>-->
<!--                </RenderType>-->
<!--            </Field>-->
<!--        </Fields>-->

<!--        <Actions>-->
<!--            <Action label="导出清单" action="" />-->
<!--            <Action label="一键匹配协议" action="#{autoMatchingAgreement}" confirm="是否确认一键匹配协议？" />-->
<!--            <Action label="手动匹配协议" multi="#{true}" action="#{manualMatchingAgreement}" />-->
<!--            <Action label="手动匹配标品" multi="#{true}" action="#{manualMatchingSpu}" />-->
<!--            <Action label="导出明细" action="#{exportProfit}" />-->
<!--            <Action label="导入销售价" action="" openViewType="Dialog" openViewSize="xs"-->
<!--                    targetView="b2b_contract_RequestPurchaseProfitUploadTO_RequestPurchaseProfitUpload"-->
<!--                    payloadCallback="#{getUploadProfitPayloadCallback('RequestPurchaseLine')}" />-->
<!--        </Actions>-->
<!--    </Table>-->

    <Table title="询价单" model="b2b_contract_AskPriceBO" key="AskPrice"
           dataFunction="b2b_contract_PagingAskPriceBOBuiltInFunc"
           dataParams="{requestPurchaseBO:{id:{type: 'Collection', values: [pageContext.record.id]}}}">
        <Fields>
            <Field name="code" label="询价单编号"/>
            <Field name="name" label="询价单名称"/>
            <Field name="status" label="状态"/>
            <Field name="updatedAt" label="创建时间"/>
            <Field name="createdBy" label="创建人"/>
        </Fields>
    </Table>
</View>
