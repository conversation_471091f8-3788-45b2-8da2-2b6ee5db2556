package io.terminus.gaia.app.b2b.contract.tmodel;

import io.terminus.gaia.app.b2b.contract.model.AgreementBO;
import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceBO;
import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseLineBO;
import io.terminus.gaia.md.model.BrandBO;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.TransientModel;
import io.terminus.trantorframework.api.annotation.typemeta.LinkMeta;
import io.terminus.trantorframework.api.annotation.typemeta.LookupMeta;
import lombok.*;

import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@TransientModel(name = "AgreementBrandTO")
public class AgreementBrandTO extends BaseModel<Long> {

    private static final long serialVersionUID = -828703226568423550L;

    @Field(name = "协议", desc = "协议")
    @LinkMeta
    private AgreementBO agreementBO;

    @Field(name = "供应商", desc = "供应商")
    @LinkMeta
    private EntityBO yfCompanyBO;

    @Field(name = "品牌", desc = "品牌")
    @LinkMeta
    private BrandBO brandBO;

    /**
     * 要询价的需求池清单行
     * 比如：需求池有3个需求池清单行，其中有2行需要询价，则把这2行需求池清单行传到这
     * */
    @Field(name = "要询价的需求池清单行", desc = "要询价的需求池清单行")
    @LookupMeta(linkField = AskSupplierPriceBO.id_field)
    List<RequestPurchaseLineBO> requestPurchaseLineBOList;

//    /**
//     * 询价单-供应商报价信息（供应商报价合价、供应商信息、协议信息、第n轮次询价报价、供应商询价报价状态）-（供应商询价报价明细清单）
//     * */
//    @Field(name = "供应商报价单", desc = "供应商报价单")
//    @LookupMeta(linkField = AskSupplierPriceBO.id_field)
//    private List<AskSupplierPriceBO> askSupplierPriceBOList;
}
