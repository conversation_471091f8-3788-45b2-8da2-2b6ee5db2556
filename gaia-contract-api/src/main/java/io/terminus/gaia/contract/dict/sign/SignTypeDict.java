package io.terminus.gaia.contract.dict.sign;

import io.terminus.trantorframework.api.annotation.Dictionary;
import io.terminus.trantorframework.api.annotation.DictionaryItem;

/**
 * 签署结果
 */
@Dictionary(name = "electronic sign type dict")
public interface SignTypeDict {
    /**
     * 已签署
     */
    @DictionaryItem(value = "已签署")
    String ALL_SIGNED = "ALL_SIGNED";

    /**
     * 待甲方签署
     */
    @DictionaryItem(value = "待甲方签署")
    String WAIT_PARTY_A = "WAIT_PARTY_A";

    /**
     * 待乙方签署
     */
    @DictionaryItem(value = "待乙方签署")
    String WAIT_PARTY_B = "WAIT_PARTY_B";
}
