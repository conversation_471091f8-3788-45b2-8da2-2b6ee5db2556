package io.terminus.gaia.contract.flow.contract.read;

import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.trantor.module.base.dynamicview.DynamicView;
import io.terminus.trantorframework.api.annotation.Flow;

import java.util.List;

/**
 * 获取合同动态表单
 *
 * <AUTHOR> 韩俊文(柚屿)
 **/
@Flow(name = "Query contract dynamic view flow")
public interface QueryContractDynamicViewFlow {

    /**
     * 获取合同动态表单
     *
     * @param contract 合同
     * @return 动态表单
     */
    List<DynamicView> execute(ContractBO contract);
}
