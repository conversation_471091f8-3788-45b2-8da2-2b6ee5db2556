package io.terminus.gaia.app.b2b.contract.func.agreement.yz;

import io.terminus.gaia.app.b2b.contract.model.AgreementContactBO;
import io.terminus.gaia.app.b2b.contract.model.query.QAgreementContactBO;
import io.terminus.gaia.common.utils.EnhanceDS;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@FunctionImpl
public class YzPagingAgreementContactFuncImpl implements YzPagingAgreementContactFunc {
    @Override
    public Paging<AgreementContactBO> execute(QAgreementContactBO qAgreementContactBO) {
        EnhanceDS.addQueryField(qAgreementContactBO,
                "districts.districtName",
                "agreementBO.code", "agreementBO.name");
        Paging<AgreementContactBO> paging = DS.paging(qAgreementContactBO);

        EnhanceDS.simplifyModel(paging.getData());
        return paging;
    }
}
