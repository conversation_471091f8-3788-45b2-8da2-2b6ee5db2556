package io.terminus.gaia.app.b2b.contract.func.price.read.cal;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import io.terminus.gaia.app.b2b.contract.func.price.service.CalSpcCommonService;
import io.terminus.gaia.app.b2b.contract.func.price.service.CalSpcService;
import io.terminus.gaia.app.b2b.contract.model.price.SalePriceCalLineBO;
import io.terminus.gaia.app.b2b.contract.tmodel.price.CalSPCLineReqTO;
import io.terminus.gaia.app.b2b.contract.tmodel.price.RegionSpcRelateLineTO;
import io.terminus.gaia.app.b2b.contract.tmodel.price.RegionSpcRelateLineWrapTO;
import io.terminus.gaia.app.b2b.contract.tmodel.price.ValidCalSpuSPCTO;
import io.terminus.gaia.contract.utils.ExceptionUtil;
import io.terminus.gaia.item.model.spu.SpuBO;
import io.terminus.gaia.md.model.DistrictBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2024/3/8 17:04
 * 存在标品的销售价格<最高采购价格
 */
@FunctionImpl
@Slf4j
@RequiredArgsConstructor
public class ValidCalSpuSPCFuncImpl implements ValidCalSpuSPCFunc {

    private final CalSpcService calSpcService;
    private final CalSPCLineReqFunc calSPCLineReqFunc;
    private final CalSpcCommonService calSpcCommonService;

    @Override
    public ValidCalSpuSPCTO execute(RegionSpcRelateLineWrapTO req) {
        // 过滤未选中数据
        calSpcCommonService.filterData(req);

        validData(req);

        // 重新计算各种价格
        reCalPrice(req);

        // 校验计算实际销售价格是否高于最高采购平均价
        HashMap<Long, List<SpuBO>> map = new HashMap<>();
        List<String> payableSchemeTemplateCodeList = new ArrayList<>();
        for (RegionSpcRelateLineTO regionSpcRelateLineTO : req.getRegionSpcRelateLineList()) {
            for (CalSPCLineReqTO calSPCLineReqTO : regionSpcRelateLineTO.getCalSPCLineReqList()) {
                for (SalePriceCalLineBO line : calSPCLineReqTO.getSalePriceCalLineList()) {
                    Boolean spcLegal = calSpcService.validSpcLegal(line);
                    if (!spcLegal) {
                        map.computeIfAbsent(regionSpcRelateLineTO.getDistrictId(), k -> new ArrayList<>()).add(line.getSpu());
                        String code = calSPCLineReqTO.getPayableSchemeTemplateBO().getCode();
                        String remark = calSPCLineReqTO.getPayableSchemeTemplateBO().getMark();
                        payableSchemeTemplateCodeList.add(StrUtil.format("{}:{}", code, remark));
                    }
                }
            }
        }

        String errorMsg = null;
        if (CollUtil.isNotEmpty(map)) {
            Set<Long> districtIdSet = map.keySet();
            List<DistrictBO> districtBOList = DS.findAll(DistrictBO.class, "id,districtName", "id in (?)", districtIdSet);
            //Map<Long, DistrictBO> districtBOMap = districtBOList.stream().collect(Collectors.toMap(DistrictBO::getId, Function.identity()));


            String districtJoin = districtBOList.stream().map(DistrictBO::getDistrictName).collect(Collectors.joining(","));
            String spuCodeJoin = map.values().stream().flatMap(Collection::stream).map(SpuBO::getSpuCode).collect(Collectors.joining(","));
            String payableSchemeTemplateCodeJoin = CollUtil.join(payableSchemeTemplateCodeList, ",");

            errorMsg = StrUtil.format("销售区域：【{}】，标品编码：【{}】,账期：【{}】，对应的销售价低于供应商的最高采购价，是否要继续测算当前销售价？", districtJoin, spuCodeJoin, payableSchemeTemplateCodeJoin);
        }

        ValidCalSpuSPCTO result = new ValidCalSpuSPCTO();
        result.setPass(CollUtil.isEmpty(map));
        result.setMsg(errorMsg);

        return result;
    }

    /**
     * 重新计算各种价格
     */
    private void reCalPrice(RegionSpcRelateLineWrapTO req) {
        for (RegionSpcRelateLineTO regionSpcRelateLineTO : req.getRegionSpcRelateLineList()) {
            for (CalSPCLineReqTO template : regionSpcRelateLineTO.getCalSPCLineReqList()) {
                List<SalePriceCalLineBO> resLine = calSPCLineReqFunc.execute(template);
                template.setSalePriceCalLineList(resLine);
            }
        }
    }

    private void validData(RegionSpcRelateLineWrapTO req) {
        for (RegionSpcRelateLineTO regionSpcRelateLineTO : req.getRegionSpcRelateLineList()) {
            Assert.notNull(regionSpcRelateLineTO);

            for (CalSPCLineReqTO template : regionSpcRelateLineTO.getCalSPCLineReqList()) {
                Assert.notNull(template);
                Assert.notNull(template.getManagePrice(), ExceptionUtil.create("管理费用不能为空"));
                Assert.notNull(template.getSalPrice(), ExceptionUtil.create("销售费用不能为空"));
                Assert.notNull(template.getCostProfitRate(), ExceptionUtil.create("成本利润率不能为空"));

                for (SalePriceCalLineBO line : template.getSalePriceCalLineList()) {
                    Assert.notNull(line.getMkRefPriceWithoutTax(), ExceptionUtil.create("市场参考价不能为空"));
                    Assert.notNull(line.getHighPceAvgPriceWithoutTax(), ExceptionUtil.create("最高采购平均价不能为空"));
                }
            }
        }
    }
}

