<?xml version="1.0" encoding="UTF-8" ?>
<View title="询价单详情" forModel="b2b_contract_AskSupplierPriceReplyTO" type="Detail" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/xsi/0.17.x/base.xsd">

    <Form key="paymentScheme" model="b2b_contract_AskSupplierPriceReplyTO" dataSource="#{onDataSource}">
        <Fields>
            <Field name="askPriceId" label="询价单id" show="#{false}"/>
            <Field name="entityBOList">
                <RenderType>
                    <ModelSelect modalTitle="选择供应商" fuzzySearchable="#{false}">
                        <Search>
                            <Fields>
                                <Field name="id" label="编码"/>
                                <Field name="entityName" label="名称"/>
                            </Fields>
                        </Search>
                        <Fields>
                            <Field name="id" label="编码"/>
                            <Field name="entityName" label="名称"/>
                        </Fields>
                    </ModelSelect>
                </RenderType>
            </Field>
            <Field name="rejectReason" label="拒绝原因"/>
        </Fields>
        <Actions>
            <Action layout="Footer" type="Cancel" after="GoBack"/>
            <Action type="Submit" label="提交" layout="Footer" logicFunc="b2b_contract_RejectAskSupplierPriceFunc" after="GoBack"/>
        </Actions>
    </Form>


</View>
