package io.terminus.gaia.app.b2b.contract.flow.material.write;

import io.terminus.gaia.app.b2b.item.func.materialslist.write.UpdateCommercialPaperPrcFunc;
import io.terminus.gaia.app.b2b.item.model.SkuBOExt;
import io.terminus.gaia.contract.flow.contract.write.UpdatePrcWhileUpdateContractFlow;
import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.FlowImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


import java.util.List;

/**
 * 更新材料清单价格
 *
 * <AUTHOR>
 */
@FlowImpl(name = "Update Prc While Update Contract Func")
@RequiredArgsConstructor
@Slf4j
public class UpdatePrcWhileUpdateContractFlowImpl implements UpdatePrcWhileUpdateContractFlow {
    private final UpdateCommercialPaperPrcFunc updateCommercialPaperPrcFunc;
    @Override
    public BooleanResult execute(ContractBO contractBO) {
        try {
            List<SkuBOExt> execute = updateCommercialPaperPrcFunc.execute(contractBO);
        }catch (Exception e){
            log.error("更新价格失败 message:{}");
            e.printStackTrace();
        }
        return BooleanResult.TRUE;
    }
}
