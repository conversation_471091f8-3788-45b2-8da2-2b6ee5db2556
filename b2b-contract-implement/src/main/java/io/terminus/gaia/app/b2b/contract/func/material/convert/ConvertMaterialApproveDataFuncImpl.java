package io.terminus.gaia.app.b2b.contract.func.material.convert;

import io.terminus.gaia.app.b2b.item.dict.item.MaterialsApprovalTypeDict;
import io.terminus.gaia.app.b2b.item.model.SkuBOExt;
import io.terminus.gaia.app.b2b.item.tmodel.item.ItemApprovalTO;
import io.terminus.gaia.contract.dict.common.ApproveStatusDict;
import io.terminus.gaia.contract.dict.contract.ContractStatusDict;
import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.gaia.contract.msg.contract.ContractExMsg;
import io.terminus.gaia.contract.tmodel.bpm.ApprovalResultTO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: fengbo
 * @Date: 2021/8/24
 */
@FunctionImpl(name = "convert material approve data func")
@Slf4j
public class ConvertMaterialApproveDataFuncImpl implements ConvertMaterialApproveDataFunc {
    @Override
    public ItemApprovalTO execute(ApprovalResultTO approvalResultTO) {
        //1.查找业务数据,校验提交协议是否存在
        List<Long> ids = Arrays.asList(approvalResultTO.getBusinessID()
                .split("-")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        List<ContractBO> contractBOS = DS.findByIds(ContractBO.class, ids);
        if (CollectionUtils.isEmpty(contractBOS)) {
            throw new BusinessException(ContractExMsg.CONTRACT_IS_NULL);
        }
        ItemApprovalTO itemApprovalTO=new ItemApprovalTO();
        //根据合同ID集合查询对应的材料清单信息
        List<SkuBOExt> allSkuList = DS.findAll(SkuBOExt.class, "*", SkuBOExt.protocol_field + " in (?)",
                ids);
        itemApprovalTO.setSkuBOExtList(allSkuList);
        //审批中
        if (approvalResultTO.getApprovalStatus().equals(ApproveStatusDict.APPROVING)) {

        }
        //审批通过
        if (approvalResultTO.getApprovalStatus().equals(ApproveStatusDict.APPROVED)) {
            itemApprovalTO.setApprovalType(MaterialsApprovalTypeDict.MATERIALS_APPLY_PASSED);
        }
        //审批退回\作废
        if (approvalResultTO.getApprovalStatus().equals(ApproveStatusDict.APPROVED_BACK)
                || approvalResultTO.getApprovalStatus().equals(ApproveStatusDict.APPROVED_CANCEL)) {
            itemApprovalTO.setApprovalType(MaterialsApprovalTypeDict.MATERIALS_APPLY_REFUSED);
        }
        //审批驳回
        if (approvalResultTO.getApprovalStatus().equals(ApproveStatusDict.APPROVED_REJECT)) {
            itemApprovalTO.setApprovalType(MaterialsApprovalTypeDict.MATERIALS_APPLY_REFUSED);
        }
        return itemApprovalTO;
    }
}
