package io.terminus.gaia.app.b2b.contract.func.askprice.write;

import io.terminus.gaia.app.b2b.contract.model.AskPriceBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 删除询价单
 * 【由于询价单没有草稿态，所以这个接口暂时没有使用场景】
 *
 * <AUTHOR>
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class DeleteAskPriceFuncImpl implements DeleteAskPriceFunc {
    @Override
    public void execute(AskPriceBO askPriceBO) {
        if (askPriceBO == null || askPriceBO.getId() == null) {
            throw new BusinessException("删除失败！原因：入参询价单id必须非空");
        }

        DS.delete(askPriceBO);
    }
}
