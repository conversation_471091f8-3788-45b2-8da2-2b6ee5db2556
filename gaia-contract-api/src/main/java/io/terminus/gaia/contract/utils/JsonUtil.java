package io.terminus.gaia.contract.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;

/**
 * <AUTHOR>
 */
public class JsonUtil {
    /**
     * 转化json串，忽略rootModel中的__trantorExtendFields和_fields
     *
     * @param object
     * @return
     */
    public static String getJsonExcludeRootFields(Object object) {
        if (object == null) {
            return "";
        }

        SimplePropertyPreFilter filter = new SimplePropertyPreFilter();
        filter.getExcludes().add("__trantorExtendFields");
        filter.getExcludes().add("_fields");

        return JSON.toJSONString(object, filter);
    }
}
