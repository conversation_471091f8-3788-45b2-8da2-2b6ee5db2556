package io.terminus.gaia.app.b2b.contract.func.agreement.write;

import io.terminus.gaia.app.b2b.contract.model.RequestPurchaseLineBO;
import io.terminus.gaia.app.b2b.contract.tmodel.RequestPurchaseLineUploadTO;
import io.terminus.trantorframework.api.annotation.Function;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @classname CreateAgreementTemplateUpdateFileTOImportFunc
 * @description TODO
 * @date 2024/3/19 17:58
 */
@Function(name = "文件导入解析")
public interface ImportRequestPurchaseLineFunc {
    List<RequestPurchaseLineBO> execute(RequestPurchaseLineUploadTO requestPurchaseLineUploadTO);
}
