package io.terminus.gaia.app.b2b.contract.model;

import io.terminus.gaia.md.model.EntityBO;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.Model;
import io.terminus.trantorframework.api.annotation.ModelConfig;
import io.terminus.trantorframework.api.annotation.typemeta.LinkMeta;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 供应商-报价单行信息
 *
 * <AUTHOR>
 */
@Model(
        name = "供应商-报价单行信息",
        config = @ModelConfig(
                enableExport = true)
)
@Data
@EqualsAndHashCode(callSuper = true)
public class AskSupplierPriceLineBO extends BaseModel<Long> {

    @Field(name = "需求池", desc = "需求池")
    @LinkMeta
    private RequestPurchaseBO requestPurchaseBO;

    @Field(name = "询价单", desc = "询价单")
    @LinkMeta
    private AskPriceBO askPriceBO;

    @Field(name = "询价单的明细清单行", desc = "询价单的明细清单行")
    @LinkMeta
    private AskPriceLineBO askPriceLineBO;

    @Field(name = "需求池的明细清单行", desc = "需求池的明细清单行")
    @LinkMeta
    private RequestPurchaseLineBO requestPurchaseLineBO;

    @Field(name = "供应商-报价单", desc = "供应商-报价单")
    @LinkMeta
    private AskSupplierPriceBO askSupplierPriceBO;


    @Field(name = "供应方", desc = "供应方id")
    @LinkMeta
    private EntityBO supplierEntity;


    @Field(name = "报价轮次", desc = "报价轮次")
    private Integer round;


    /**
     * 综合单价（含税） = （延米铜价 + 辅材单价） * 折扣系数
     * 特点：小数保留2位，显示2位
     * 来源：供应商手动填入
     */
    @Field(name = "综合单价（含税）（元/米）", desc = "综合单价（含税）（元/米）")
    private BigDecimal purTaxPrice;

    /**
     * 行合价（含税） = （延米铜价 + 辅材单价） * 折扣系数 * 数量
     * 特点：数量限制4位小数，合价小数保留2位，显示2位
     */
    @Field(name = "行合价（含税）（元）", desc = "行合价（含税）（元）")
    private BigDecimal lineAmountWithTax;


    @Field(name = "备注")
    private String remark;


    /**
     * @param id id
     * @return AskSupplierPriceBO
     */
    public static AskSupplierPriceLineBO of(Long id) {
        if (id == null) {
            return null;
        }
        AskSupplierPriceLineBO askSupplierPriceLineBO = new AskSupplierPriceLineBO();
        askSupplierPriceLineBO.setId(id);
        return askSupplierPriceLineBO;
    }

}
