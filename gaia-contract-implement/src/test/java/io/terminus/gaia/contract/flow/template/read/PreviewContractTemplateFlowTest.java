package io.terminus.gaia.contract.flow.template.read;

import com.google.common.collect.Lists;
import io.terminus.gaia.contract.BaseService;
import io.terminus.gaia.contract.dict.contract.ContractTypeDict;
import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.gaia.contract.model.template.ContractTemplateBO;
import io.terminus.gaia.md.dict.CurrencyPrefixPriceDict;
import io.terminus.gaia.md.dict.CurrencyStatusDict;
import io.terminus.gaia.md.dict.EntityStatusDict;
import io.terminus.gaia.md.dict.EntityTypeDict;
import io.terminus.gaia.md.model.CurrencyBO;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.gaia.organization.dict.EmployeeStatusDict;
import io.terminus.gaia.organization.model.EmployeeBO;
import io.terminus.gaia.partner.dict.BusinessTypeDict;
import io.terminus.gaia.partner.dict.form.*;
import io.terminus.gaia.partner.model.form.FormGroupBO;
import io.terminus.gaia.partner.model.form.FormLineBO;
import io.terminus.gaia.partner.model.form.FormLineDynamicDataBO;
import io.terminus.gaia.partner.model.form.FormTemplateBO;
import io.terminus.trantorframework.api.type.Attachment;
import io.terminus.trantorframework.sdk.sql.DS;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import java.util.*;

/**
 * 合同预览测试
 * <AUTHOR>
 * @Date 2021/5/7
 */
public class PreviewContractTemplateFlowTest extends BaseService {

    PreviewContractTemplateFlow previewContractTemplateFlow;

    Map<String,Object> dynamicFields = new LinkedHashMap();

    @Test
    public void execute(){
        // http://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/trantor/attachments/034920e0-6a06-4167-b533-cd234dbda0da.docx

        ContractBO contractBO = new ContractBO();
        contractBO.setContractName("合同名称001");
        contractBO.setContractCode("test001");

        EntityBO entityBOA = new EntityBO();
        entityBOA.setEntityType(EntityTypeDict.COMPANY);
        entityBOA.setEntityName("哇哈哈有限公司");
        entityBOA.setEntityCode("WAHAHA0001");
        entityBOA.setEntityStatus(EntityStatusDict.ENABLED);
        Long entityAId = DS.create(entityBOA).longValue();
        entityBOA.setId(entityAId);

        EntityBO entityBOB = new EntityBO();
        entityBOB.setEntityType(EntityTypeDict.COMPANY);
        entityBOB.setEntityName("蒙牛有限公司");
        entityBOB.setEntityCode("MN0001");
        entityBOB.setEntityStatus(EntityStatusDict.ENABLED);
        Long entityBId = DS.create(entityBOB).longValue();
        entityBOB.setId(entityBId);

        contractBO.setPartyA(entityBOA);
        contractBO.setPartyB(entityBOB);

        contractBO.setBusinessTypeDict(BusinessTypeDict.CONTRACT);

        CurrencyBO currencyBO = new CurrencyBO();
        currencyBO.setPrefixPrice(CurrencyPrefixPriceDict.SHORT_CODE);
        currencyBO.setCurrencyName("人命币");
        currencyBO.setCurrencyStatus(CurrencyStatusDict.ENABLED);
        Long currencyBOId = DS.create(currencyBO).longValue();
        currencyBO.setId(currencyBOId);
        contractBO.setCurrency(currencyBO);

        contractBO.setSignDate(new Date());

        EmployeeBO employeeBO = new EmployeeBO();
        employeeBO.setEmployeeAddress("杭州西湖1202");
        employeeBO.setEmployeeMail("<EMAIL>");
        employeeBO.setEmployeeName("李翠花");
        employeeBO.setEmployeeStatusDict(EmployeeStatusDict.ENABLED);
        Long employeeBOId = DS.create(employeeBO).longValue();
        employeeBO.setId(employeeBOId);
        contractBO.setSignatory(employeeBO);

        contractBO.setEndDate(new Date());

        contractBO.setDynamicFields(dynamicFields);
        contractBO.setContractTypeDict(ContractTypeDict.PURCHASE_AGREEMENT);
        contractBO.setIsUseTemplate(true);

        ContractTemplateBO contractTemplateBO = new ContractTemplateBO();
        Attachment.File file = new Attachment.File();
        file.setName("合同模版0510");
        file.setUrl("http://terminus-trantor.oss-cn-hangzhou.aliyuncs.com/trantor/attachments/034920e0-6a06-4167-b533-cd234dbda0da.docx");
        file.setType("docx");
        Attachment attachment = new Attachment();
        attachment.setFiles(Lists.newArrayList(file));
        contractTemplateBO.setAttachments(attachment);
        contractTemplateBO.setSuitableContractTypeDict(ContractTypeDict.PURCHASE_AGREEMENT);
        contractTemplateBO.setFormTemplate(new FormTemplateBO());
        contractTemplateBO = DS.findById(ContractTemplateBO.class,DS.create(contractTemplateBO).longValue());
        contractBO.setContractTemplate(contractTemplateBO);

        contractBO = DS.findById(ContractBO.class,DS.create(contractBO).longValue());
        contractBO.setDynamicFields(dynamicFields);

        ContractBO res = previewContractTemplateFlow.execute(contractBO);

        Assert.assertNotNull(res);

    }


    public void initFormLineDynamicData(){
        FormLineDynamicDataBO formLineDynamicDataBO1 = new FormLineDynamicDataBO();
        formLineDynamicDataBO1.setId(1L);
        formLineDynamicDataBO1.setApplicableBusinessType(BusinessTypeDict.CONTRACT);
        formLineDynamicDataBO1.setValue("ContractBO.partyA");
        formLineDynamicDataBO1.setName("合同主体");
        formLineDynamicDataBO1.setFieldTypeDict(FormLineTypeDict.TEXT);
        formLineDynamicDataBO1.setDataTypeDict(FormLineDynamicDataTypeDict.MODEL);
        DS.create(formLineDynamicDataBO1);

        FormLineDynamicDataBO formLineDynamicDataBO2 = new FormLineDynamicDataBO();
        formLineDynamicDataBO2.setId(2L);
        formLineDynamicDataBO2.setApplicableBusinessType(BusinessTypeDict.CONTRACT);
        formLineDynamicDataBO2.setValue("ContractBO.signatory");
        formLineDynamicDataBO2.setName("签约人");
        formLineDynamicDataBO2.setFieldTypeDict(FormLineTypeDict.TEXT);
        formLineDynamicDataBO2.setDataTypeDict(FormLineDynamicDataTypeDict.MODEL);
        DS.create(formLineDynamicDataBO2);

        FormLineDynamicDataBO formLineDynamicDataBO3 = new FormLineDynamicDataBO();
        formLineDynamicDataBO3.setId(3L);
        formLineDynamicDataBO3.setApplicableBusinessType(BusinessTypeDict.CONTRACT);
        formLineDynamicDataBO3.setValue("ContractBO.signDate");
        formLineDynamicDataBO3.setName("签约日期");
        formLineDynamicDataBO3.setFieldTypeDict(FormLineTypeDict.TEXT);
        formLineDynamicDataBO3.setDataTypeDict(FormLineDynamicDataTypeDict.MODEL);
        DS.create(formLineDynamicDataBO3);

        FormLineDynamicDataBO formLineDynamicDataBO4 = new FormLineDynamicDataBO();
        formLineDynamicDataBO4.setId(4L);
        formLineDynamicDataBO4.setApplicableBusinessType(BusinessTypeDict.CONTRACT);
        formLineDynamicDataBO4.setValue("ContractBO.currency");
        formLineDynamicDataBO4.setName("货币");
        formLineDynamicDataBO4.setFieldTypeDict(FormLineTypeDict.TEXT);
        formLineDynamicDataBO4.setDataTypeDict(FormLineDynamicDataTypeDict.MODEL);
        DS.create(formLineDynamicDataBO4);

        FormLineDynamicDataBO formLineDynamicDataBO5 = new FormLineDynamicDataBO();
        formLineDynamicDataBO5.setId(5L);
        formLineDynamicDataBO5.setApplicableBusinessType(BusinessTypeDict.CONTRACT);
        formLineDynamicDataBO5.setValue("ContractBO.contractTaxAmt");
        formLineDynamicDataBO5.setName("含税签约金额");
        formLineDynamicDataBO5.setFieldTypeDict(FormLineTypeDict.TEXT);
        formLineDynamicDataBO5.setDataTypeDict(FormLineDynamicDataTypeDict.MODEL);
        DS.create(formLineDynamicDataBO5);

        FormLineDynamicDataBO formLineDynamicDataBO6 = new FormLineDynamicDataBO();
        formLineDynamicDataBO6.setId(6L);
        formLineDynamicDataBO6.setApplicableBusinessType(BusinessTypeDict.CONTRACT);
        formLineDynamicDataBO6.setValue("ContractBO.contractAmt");
        formLineDynamicDataBO6.setName("未含税签约金额");
        formLineDynamicDataBO6.setFieldTypeDict(FormLineTypeDict.TEXT);
        formLineDynamicDataBO6.setDataTypeDict(FormLineDynamicDataTypeDict.MODEL);
        DS.create(formLineDynamicDataBO6);

        FormLineDynamicDataBO formLineDynamicDataBO7 = new FormLineDynamicDataBO();
        formLineDynamicDataBO7.setId(7L);
        formLineDynamicDataBO7.setApplicableBusinessType(BusinessTypeDict.CONTRACT);
        formLineDynamicDataBO7.setValue("ContractBO.startDate");
        formLineDynamicDataBO7.setName("生效日期");
        formLineDynamicDataBO7.setFieldTypeDict(FormLineTypeDict.DATE);
        formLineDynamicDataBO7.setDataTypeDict(FormLineDynamicDataTypeDict.MODEL);
        DS.create(formLineDynamicDataBO7);

        FormLineDynamicDataBO formLineDynamicDataBO8 = new FormLineDynamicDataBO();
        formLineDynamicDataBO8.setId(8L);
        formLineDynamicDataBO8.setApplicableBusinessType(BusinessTypeDict.CONTRACT);
        formLineDynamicDataBO8.setValue("ContractBO.endDate");
        formLineDynamicDataBO8.setName("终止日期");
        formLineDynamicDataBO8.setFieldTypeDict(FormLineTypeDict.DATE);
        formLineDynamicDataBO8.setDataTypeDict(FormLineDynamicDataTypeDict.MODEL);
        DS.create(formLineDynamicDataBO8);

        FormLineDynamicDataBO formLineDynamicDataBO9 = new FormLineDynamicDataBO();
        formLineDynamicDataBO9.setId(9L);
        formLineDynamicDataBO9.setApplicableBusinessType(BusinessTypeDict.CONTRACT);
        formLineDynamicDataBO9.setValue("ContractBO.partyB");
        formLineDynamicDataBO9.setName("合同乙方");
        formLineDynamicDataBO9.setFieldTypeDict(FormLineTypeDict.TEXT);
        formLineDynamicDataBO9.setDataTypeDict(FormLineDynamicDataTypeDict.MODEL);
        DS.create(formLineDynamicDataBO9);

        FormLineDynamicDataBO formLineDynamicDataBO10 = new FormLineDynamicDataBO();
        formLineDynamicDataBO10.setId(10L);
        formLineDynamicDataBO10.setApplicableBusinessType(BusinessTypeDict.CONTRACT);
        formLineDynamicDataBO10.setValue("ContractBO.partyCList");
        formLineDynamicDataBO10.setName("其他合同方");
        formLineDynamicDataBO10.setFieldTypeDict(FormLineTypeDict.TEXT);
        formLineDynamicDataBO10.setDataTypeDict(FormLineDynamicDataTypeDict.MODEL);
        DS.create(formLineDynamicDataBO10);

        FormLineDynamicDataBO formLineDynamicDataBO11 = new FormLineDynamicDataBO();
        formLineDynamicDataBO11.setId(11L);
        formLineDynamicDataBO11.setApplicableBusinessType(BusinessTypeDict.CONTRACT);
        formLineDynamicDataBO11.setValue("ContractBO.businessTypeDict");
        formLineDynamicDataBO11.setName("业务类型");
        formLineDynamicDataBO11.setFieldTypeDict(FormLineTypeDict.RADIO);
        formLineDynamicDataBO11.setDataTypeDict(FormLineDynamicDataTypeDict.DICT);
        DS.create(formLineDynamicDataBO11);

        FormLineDynamicDataBO formLineDynamicDataBO12 = new FormLineDynamicDataBO();
        formLineDynamicDataBO12.setId(12L);
        formLineDynamicDataBO12.setApplicableBusinessType(BusinessTypeDict.CONTRACT);
        formLineDynamicDataBO12.setValue("ContractBO.company");
        formLineDynamicDataBO12.setName("所属公司");
        formLineDynamicDataBO12.setFieldTypeDict(FormLineTypeDict.TEXT);
        formLineDynamicDataBO12.setDataTypeDict(FormLineDynamicDataTypeDict.MODEL);
        DS.create(formLineDynamicDataBO12);

        FormLineDynamicDataBO formLineDynamicDataBO13 = new FormLineDynamicDataBO();
        formLineDynamicDataBO13.setId(13L);
        formLineDynamicDataBO13.setApplicableBusinessType(BusinessTypeDict.CONTRACT);
        formLineDynamicDataBO13.setValue("ContractBO.purchaserOrganization");
        formLineDynamicDataBO13.setName("所属组织");
        formLineDynamicDataBO13.setFieldTypeDict(FormLineTypeDict.TEXT);
        formLineDynamicDataBO13.setDataTypeDict(FormLineDynamicDataTypeDict.MODEL);
        DS.create(formLineDynamicDataBO13);

        FormLineDynamicDataBO formLineDynamicDataBO14 = new FormLineDynamicDataBO();
        formLineDynamicDataBO14.setId(14L);
        formLineDynamicDataBO14.setApplicableBusinessType(BusinessTypeDict.CONTRACT);
        formLineDynamicDataBO14.setValue("ContractBO.site");
        formLineDynamicDataBO14.setName("所属实体");
        formLineDynamicDataBO14.setFieldTypeDict(FormLineTypeDict.TEXT);
        formLineDynamicDataBO14.setDataTypeDict(FormLineDynamicDataTypeDict.MODEL);
        DS.create(formLineDynamicDataBO14);

    }


    @Before
    public void init(){

        Map<String,Object> form = new LinkedHashMap<>();
        String formKey = "";
        List<Object> table = new LinkedList<>();
        String tableKey = "";

        FormGroupBO formGroupBO1 = new FormGroupBO();
        formGroupBO1.setGroupName("基本信息");
        formGroupBO1.setGroupType(FormGroupTypeDict.FORM);
        formGroupBO1 = DS.findById(FormGroupBO.class,DS.create(formGroupBO1).getValue().longValue());

        formKey = "form_" + formGroupBO1.getId();



        FormGroupBO formGroupBO2 = new FormGroupBO();
        formGroupBO2.setGroupName("表格分组");
        formGroupBO2.setGroupType(FormGroupTypeDict.TABLE);
        formGroupBO2 = DS.findById(FormGroupBO.class,DS.create(formGroupBO2).getValue().longValue());

        tableKey = "table_" + formGroupBO2.getId();

        FormLineBO formLineBO0 = new FormLineBO();
        formLineBO0.setFormGroup(formGroupBO1);
        formLineBO0.setTitle("合同名称");
        formLineBO0.setFormLineTypeDict(FormLineTypeDict.TEXT);
        // 获取方式，默认自动
        formLineBO0.setAccessMethod(FormLineAccessMethodDict.AUTOMATIC);
        // 值来源方式，默认自动
        formLineBO0.setDataSource(FormLineDataSourceDict.MANUAL_INPUT);
//        formLineBO0.setFormLineDynamicData();
        Long formLineId0 =  DS.create(formLineBO0).longValue();
        form.put("合同名称_" + formLineId0,"王XX购房合同");

        FormLineBO formLineBO1 = new FormLineBO();
        formLineBO1.setFormGroup(formGroupBO1);
        formLineBO1.setTitle("合同编号");
        formLineBO1.setFormLineTypeDict(FormLineTypeDict.TEXT);
        // 获取方式，默认自动
        formLineBO1.setAccessMethod(FormLineAccessMethodDict.AUTOMATIC);
        // 值来源方式，默认手动输入
        formLineBO1.setDataSource(FormLineDataSourceDict.MANUAL_INPUT);
        Long formLineId1 = DS.create(formLineBO1).longValue();
        form.put("合同编号_" + formLineId1,"GG20210507");

        FormLineBO formLineBO2 = new FormLineBO();
        formLineBO2.setFormGroup(formGroupBO1);
        formLineBO2.setTitle("有效期开始");
        formLineBO2.setFormLineTypeDict(FormLineTypeDict.DATE);
        // 获取方式，默认自动
        formLineBO2.setAccessMethod(FormLineAccessMethodDict.AUTOMATIC);
        // 值来源方式，默认手动输入
        formLineBO2.setDataSource(FormLineDataSourceDict.MANUAL_INPUT);
        Long formLineId2 =DS.create(formLineBO2).longValue();
        form.put("有效期开始_" + formLineId2,"2021-01-01 12:00:00");

        FormLineBO formLineBO3 = new FormLineBO();
        formLineBO3.setFormGroup(formGroupBO1);
        formLineBO3.setTitle("有效期结束");
        formLineBO3.setFormLineTypeDict(FormLineTypeDict.DATE);
        // 获取方式，默认自动
        formLineBO3.setAccessMethod(FormLineAccessMethodDict.AUTOMATIC);
        // 值来源方式，默认手动输入
        formLineBO3.setDataSource(FormLineDataSourceDict.MANUAL_INPUT);
        Long formLineId3 =DS.create(formLineBO3).longValue();
        form.put("有效期结束_" + formLineId3,"2021-05-01 12:00:00");

        FormLineBO formLineBO31 = new FormLineBO();
        formLineBO31.setFormGroup(formGroupBO1);
        formLineBO31.setTitle("合同主体");
        formLineBO31.setFormLineTypeDict(FormLineTypeDict.TEXT);
        // 获取方式，默认自动
        formLineBO31.setAccessMethod(FormLineAccessMethodDict.AUTOMATIC);
        // 值来源方式，自动
        formLineBO31.setDataSource(FormLineDataSourceDict.AUTOMATIC_ACQUISITION);
        FormLineDynamicDataBO formLineDynamicDataBO1 = new FormLineDynamicDataBO();
        formLineDynamicDataBO1.setId(1L);
        formLineBO31.setFormLineDynamicData(formLineDynamicDataBO1);
        Long formLineId31 = DS.create(formLineBO31).longValue();
        form.put("合同主体_" + formLineId31,"合同主体");


        FormLineBO formLineBO32 = new FormLineBO();
        formLineBO32.setFormGroup(formGroupBO1);
        formLineBO32.setTitle("合同乙方");
        formLineBO32.setFormLineTypeDict(FormLineTypeDict.TEXT);
        // 获取方式，默认自动
        formLineBO32.setAccessMethod(FormLineAccessMethodDict.AUTOMATIC);
        // 值来源方式，自动
        formLineBO32.setDataSource(FormLineDataSourceDict.AUTOMATIC_ACQUISITION);
        FormLineDynamicDataBO formLineDynamicDataBO2 = new FormLineDynamicDataBO();
        formLineDynamicDataBO2.setId(9L);
        formLineBO32.setFormLineDynamicData(formLineDynamicDataBO2);
        Long formLineId32 = DS.create(formLineBO32).longValue();
        form.put("合同乙方_" + formLineId32,"合同乙方");

        FormLineBO formLineBO33 = new FormLineBO();
        formLineBO33.setFormGroup(formGroupBO1);
        formLineBO33.setTitle("签约人");
        formLineBO33.setFormLineTypeDict(FormLineTypeDict.TEXT);
        // 获取方式，默认自动
        formLineBO33.setAccessMethod(FormLineAccessMethodDict.AUTOMATIC);
        // 值来源方式，自动
        formLineBO33.setDataSource(FormLineDataSourceDict.AUTOMATIC_ACQUISITION);
        FormLineDynamicDataBO formLineDynamicDataBO3 = new FormLineDynamicDataBO();
        formLineDynamicDataBO3.setId(2L);
        formLineBO33.setFormLineDynamicData(formLineDynamicDataBO3);
        Long formLineId33 = DS.create(formLineBO33).longValue();
        form.put("签约人_" + formLineId33,"签约人");

        FormLineBO formLineBO34 = new FormLineBO();
        formLineBO34.setFormGroup(formGroupBO1);
        formLineBO34.setTitle("图片");
        formLineBO34.setFormLineTypeDict(FormLineTypeDict.IMAGE);
        // 获取方式，默认自动
        formLineBO3.setAccessMethod(FormLineAccessMethodDict.AUTOMATIC);
        // 值来源方式，默认手动输入
        formLineBO3.setDataSource(FormLineDataSourceDict.MANUAL_INPUT);
        Long formLineId34 =DS.create(formLineBO34).longValue();
        form.put("图片_" + formLineId34,"//terminus-trantor.oss-cn-hangzhou.aliyuncs.com/trantor/attachments/ed8ff9ff-4af2-48d7-a464-1d6c5a6fa671.png");



        FormLineBO formLineBO4 = new FormLineBO();
        formLineBO4.setFormGroup(formGroupBO2);
        formLineBO4.setTitle("品名");
        formLineBO4.setSortNum(1);
        formLineBO4.setFormLineTypeDict(FormLineTypeDict.TEXT);
        // 获取方式，默认自动
        formLineBO4.setAccessMethod(FormLineAccessMethodDict.AUTOMATIC);
        // 值来源方式，默认手动输入
        formLineBO4.setDataSource(FormLineDataSourceDict.MANUAL_INPUT);
        long tableId4 =  DS.create(formLineBO4).longValue();

        FormLineBO formLineBO5 = new FormLineBO();
        formLineBO5.setFormGroup(formGroupBO2);
        formLineBO5.setTitle("规格");
        formLineBO5.setSortNum(2);
        formLineBO5.setFormLineTypeDict(FormLineTypeDict.NUMBER);
        // 获取方式，默认自动
        formLineBO5.setAccessMethod(FormLineAccessMethodDict.AUTOMATIC);
        // 值来源方式，默认手动输入
        formLineBO5.setDataSource(FormLineDataSourceDict.MANUAL_INPUT);
        long tableId5 = DS.create(formLineBO5).longValue();

        FormLineBO formLineBO6 = new FormLineBO();
        formLineBO6.setFormGroup(formGroupBO2);
        formLineBO6.setTitle("价格");
        formLineBO6.setSortNum(3);
        formLineBO6.setFormLineTypeDict(FormLineTypeDict.NUMBER);
        // 获取方式，默认自动
        formLineBO6.setAccessMethod(FormLineAccessMethodDict.AUTOMATIC);
        // 值来源方式，默认手动输入
        formLineBO6.setDataSource(FormLineDataSourceDict.MANUAL_INPUT);
        long tableId6 = DS.create(formLineBO6).longValue();

        FormLineBO formLineBO7 = new FormLineBO();
        formLineBO7.setFormGroup(formGroupBO2);
        formLineBO7.setTitle("业务类型");
        formLineBO7.setSortNum(4);
        formLineBO7.setFormLineTypeDict(FormLineTypeDict.TEXT);
        // 获取方式，默认自动
        formLineBO7.setAccessMethod(FormLineAccessMethodDict.AUTOMATIC);
        // 值来源方式，自动
        formLineBO7.setDataSource(FormLineDataSourceDict.AUTOMATIC_ACQUISITION);
        FormLineDynamicDataBO formLineDynamicDataBO = new FormLineDynamicDataBO();
        formLineDynamicDataBO.setId(11L);
        formLineBO7.setFormLineDynamicData(formLineDynamicDataBO);
        long tableId7 = DS.create(formLineBO7).longValue();

        Map<String,Object> table1 = new LinkedHashMap<>();
        table1.put("品名_" + tableId4,"农夫山泉");
        table1.put("规格_" + tableId5,15);
        table1.put("价格_" + tableId6,2.5);
        table1.put("业务类型_" + tableId7,"业务类型");
        Map<String,Object> table2 = new LinkedHashMap<>();
        table2.put("品名_" + tableId4,"卫龙辣条");
        table2.put("规格_" + tableId5,10);
        table2.put("价格_" + tableId6,1);
        table2.put("业务类型_" + tableId7,"业务类型");

        table.add(table1);
        table.add(table2);

        dynamicFields.put(formKey,form);
        dynamicFields.put(tableKey,table);

        this.initFormLineDynamicData();

    }
}
