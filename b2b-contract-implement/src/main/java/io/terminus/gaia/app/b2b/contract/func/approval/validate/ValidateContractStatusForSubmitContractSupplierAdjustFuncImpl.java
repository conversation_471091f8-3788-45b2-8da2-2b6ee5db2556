package io.terminus.gaia.app.b2b.contract.func.approval.validate;

import io.terminus.gaia.contract.dict.bpm.SubmitBPMMethodTypeDict;
import io.terminus.gaia.contract.dict.common.ApproveStatusDict;
import io.terminus.gaia.contract.func.approval.validate.ValidateStatusForSubmitApprovalFunc;
import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.gaia.contract.msg.approval.ApprovalExtMsg;
import io.terminus.gaia.contract.msg.contract.ContractExMsg;
import io.terminus.gaia.contract.tmodel.bpm.SubmitApprovalFromBusinessDataTO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.api.annotation.RouteRuleValue;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 校验业务单据状态是否能够被提交审批
 * <AUTHOR>
 */
@FunctionImpl(name = "validate status for supplier adjust func")
@RouteRuleValue({SubmitBPMMethodTypeDict.CONTRACT_SIGNED_SUPPLIER_ADJUST})
@RequiredArgsConstructor
public class ValidateContractStatusForSubmitContractSupplierAdjustFuncImpl implements ValidateStatusForSubmitApprovalFunc {

    @Override
    public void execute(SubmitApprovalFromBusinessDataTO submitApprovalFromBusinessDataTO) {
        List<ContractBO> contractBOList = DS.findAll(ContractBO.class, "*, parent.*, partyA.*, partyB.*, serviceAreaList.*," +
                "categoryBOList.*,brandBOList.*,contractDistributorList.*,contractDistributorList.performanceEntity.*", "id in (?)", submitApprovalFromBusinessDataTO.getSubmitDatas());
        if (CollectionUtils.isEmpty(contractBOList)) {
            throw new BusinessException(ContractExMsg.CONTRACT_IS_NULL);
        }
        contractBOList.forEach(contractBO -> {
            if (Objects.isNull(contractBO.getApproveStatus())
                    || Objects.isNull(contractBO.getContractStatusDict())) {
                throw new BusinessException(ApprovalExtMsg.SUBMIT_APPROVAL_PARAMS_VALIDATE_FAIL);
            }
            if (contractBO.getApproveStatus().equals(ApproveStatusDict.APPROVING)) {
                throw new BusinessException(ApprovalExtMsg.CONTRACT_APPROVAL_STATUS_CAN_NOT_BE_SUBMIT_TO_BPM);
            }
        });


    }
}
