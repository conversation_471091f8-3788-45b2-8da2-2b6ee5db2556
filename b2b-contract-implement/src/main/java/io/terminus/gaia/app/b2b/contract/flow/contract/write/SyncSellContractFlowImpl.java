package io.terminus.gaia.app.b2b.contract.flow.contract.write;

import io.terminus.gaia.app.b2b.contract.func.contract.convert.ConvertSyncFrameworkContractDataFunc;
import io.terminus.gaia.app.b2b.contract.func.contract.convert.ConvertSyncSellContractDataFunc;
import io.terminus.gaia.app.b2b.contract.func.contract.validate.ValidateSyncSapContractExistFunc;
import io.terminus.gaia.app.b2b.contract.func.contract.validate.ValidateSyncSapFrameworkContractParamsFunc;
import io.terminus.gaia.app.b2b.contract.func.contract.validate.ValidateSyncSapSellContractExistFunc;
import io.terminus.gaia.app.b2b.contract.func.contract.validate.ValidateSyncSapSellContractParamsFunc;
import io.terminus.gaia.app.b2b.contract.model.ContractExtBO;
import io.terminus.gaia.contract.func.contract.fill.FillContractIdWhenCreateFunc;
import io.terminus.gaia.contract.func.contract.write.CreateSapSyncContractFunc;
import io.terminus.gaia.contract.func.contract.write.EditSapSyncContractFunc;
import io.terminus.gaia.contract.func.contract.write.EditSapSyncSellContractFunc;
import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.AnonymousAccess;
import io.terminus.trantorframework.api.annotation.FlowImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: linwb
 * @Date: 2021/10/11
 */
@Slf4j
@RequiredArgsConstructor
@FlowImpl(name = "sync sell contract flow impl")
@AnonymousAccess
public class SyncSellContractFlowImpl implements SyncSellContractFlow {

    private final ValidateSyncSapSellContractParamsFunc validateSyncSapSellContractParamsFunc;

    private final ConvertSyncSellContractDataFunc convertSyncSellContractDataFunc;

    private final ValidateSyncSapSellContractExistFunc validateSyncSapSellContractExistFunc;

    private final FillContractIdWhenCreateFunc fillContractIdWhenCreateFunc;

    private final CreateSapSyncContractFunc createSapSyncContractFunc;

    private final EditSapSyncSellContractFunc editSapSyncSellContractFunc;

    @Override
    public BooleanResult execute(ContractExtBO contractExtBO) {
        //参数校验
        validateSyncSapSellContractParamsFunc.execute(contractExtBO);
        //参数转换处理
        ContractBO contractBO = convertSyncSellContractDataFunc.execute(contractExtBO);
        //校验合同是否已经存在
        ContractBO contractBONow = validateSyncSapSellContractExistFunc.execute(contractBO);
        //调用合同创建flow
        //不存在，创建
        if (contractBONow == null) {
            // 填充初始化ID
            fillContractIdWhenCreateFunc.execute(contractBO);
            // 合同数据入库
            createSapSyncContractFunc.execute(contractBO);
        } else {
            //存在，编辑更新
//            log.info("contractBO的主键id=" + contractBOOld.getId());
            contractBO.setId(contractBONow.getId());
//            log.info("contractBO的主键id=" + contractBO.getId());
            editSapSyncSellContractFunc.execute(contractBO);
        }
        return BooleanResult.TRUE;
    }
}
