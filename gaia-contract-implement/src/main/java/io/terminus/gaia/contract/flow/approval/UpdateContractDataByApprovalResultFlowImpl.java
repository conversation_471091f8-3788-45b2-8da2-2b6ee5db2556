package io.terminus.gaia.contract.flow.approval;

import io.terminus.gaia.contract.dict.bpm.SubmitBPMMethodTypeDict;
import io.terminus.gaia.contract.flow.approval.write.UpdateBusinessDataByApprovalResultFlow;
import io.terminus.gaia.contract.flow.approval.write.UpdateMaterialByApprovalResultFlow;
import io.terminus.gaia.contract.func.approval.write.UpdateBusinessDataByApprovalResultFunc;
import io.terminus.gaia.contract.tmodel.bpm.ApprovalResultTO;
import io.terminus.trantorframework.api.annotation.FlowImpl;
import io.terminus.trantorframework.api.annotation.RouteRuleValue;
import lombok.AllArgsConstructor;

/**
 * 根据审批结果更新业务数据的审批状态：审批驳回/审批通过/审批作废
 */
@FlowImpl(name = "update business data by approval result flow")
@RouteRuleValue({SubmitBPMMethodTypeDict.CONTRACT_CREATE})
@AllArgsConstructor
public class UpdateContractDataByApprovalResultFlowImpl implements UpdateBusinessDataByApprovalResultFlow {

    private final UpdateBusinessDataByApprovalResultFunc updateBusinessDataByApprovalResultFunc;

    private final UpdateMaterialByApprovalResultFlow updateMaterialByApprovalResultFlow;


    @Override
    public void execute(ApprovalResultTO approvalResultTO) {
        //1.更新状态相关信息
        updateBusinessDataByApprovalResultFunc.execute(approvalResultTO);
        //2.更新材料清单
        updateMaterialByApprovalResultFlow.execute(approvalResultTO);
    }
}
