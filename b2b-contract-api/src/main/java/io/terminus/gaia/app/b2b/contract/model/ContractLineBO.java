package io.terminus.gaia.app.b2b.contract.model;

import cn.hutool.core.util.ObjectUtil;
import io.terminus.gaia.app.b2b.contract.dict.RentMaterialTypeDict;
import io.terminus.gaia.app.b2b.contract.dict.RentPeriodDict;
import io.terminus.gaia.app.b2b.contract.dict.YzContractDetailStatusDict;
import io.terminus.gaia.app.b2b.contract.dict.price.SelfSpcCalTypeDict;
import io.terminus.gaia.app.b2b.item.model.SkuBOExt;
import io.terminus.gaia.item.dict.item.ItemCombinationNumTypeDict;
import io.terminus.gaia.item.model.price.PriceSchemeBO;
import io.terminus.gaia.item.model.spu.SpuBO;
import io.terminus.gaia.md.model.BrandBO;
import io.terminus.gaia.md.model.superiors.PmCmWbsBO;
import io.terminus.gaia.md.model.superiors.PmMmAccountSubjectBO;
import io.terminus.gaia.md.model.superiors.PmMmCostSubjectBO;
import io.terminus.gaia.md.model.superiors.PmMmSubSegmentBO;
import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.FieldType;
import io.terminus.trantorframework.api.annotation.Model;
import io.terminus.trantorframework.api.annotation.Transient;
import io.terminus.trantorframework.api.annotation.typemeta.*;
import io.terminus.trantorframework.api.type.Currency;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/6
 */
@EqualsAndHashCode(callSuper = true)
@Model(
        name = "合同明细"
)
@Data
public class ContractLineBO extends BaseModel<Long> {

    @Field(name = "编号")
    private String code;

    @Field(name = "外部编号")
    private String externalCode;

    @LinkMeta
    @Field(name = "关联合同")
    private YzContractBO contract;

    /**
     * 行状态
     */
    @Field(name = "状态")
    @DictionaryMeta(value = YzContractDetailStatusDict.class)
    private String status;

    @Field(name = "意向明细编码")
    private String intentionDetailNo;

    @Field(name = "成本系统明细编码")
    private String thirdContractIntentionDetailNo;

    @Field(name = "意向编码")
    private String intentionNo;

    @LinkMeta
    @Field(name = "关联sku")
    private SkuBOExt sku;

    @LinkMeta
    @Field(name = "关联spu")
    private SpuBO spu;

    @Field(name = "关联sku json", type = FieldType.Json)
    private SkuBOExt skuJson;

    @Field(name = "含税单价(元)")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency prcWithTax;

    @Field(name = "不含税单价(元)")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency prcWithoutTax;

    @Field(name = "税率(%)")
    private BigDecimal taxRate;

//    @Transient
//    @Field(name = "库存")
//    private Boolean stock;

    @Transient
    @Field(name = "子合同行列表", type = FieldType.Json)
    private List<SubContractLineTO> subContractLineList;

    @Field(name = "签约数量")
    @FloatMeta(intDigits = 20, decimalDigits = 4, displayDigits = 4)
    private BigDecimal signedQty;

    @Field(name = "已下单数量")
    @FloatMeta(intDigits = 20, decimalDigits = 4, displayDigits = 4)
    private BigDecimal placedQty;

    @Transient
    @Field(name = "本次下单数量")
    @FloatMeta(intDigits = 20, decimalDigits = 4, displayDigits = 4)
    private BigDecimal placingQty;

//    @Field(name = "是否主材")
//    private Boolean isMain;

    @Field(name = "价格方案快照", type = FieldType.Json)
    private PriceSchemeBO priceScheme;

    @Field(name = "已发货数量", defaultValue = "0")
    @FloatMeta(intDigits = 20, decimalDigits = 4, displayDigits = 4)
    private BigDecimal sentQty;

    @Field(name = "验收数量")
    @FloatMeta(intDigits = 20, decimalDigits = 4, displayDigits = 4)
    private BigDecimal receivedQty;

    @Field(name = "竞价品牌", type = FieldType.Json)
    private List<BrandBO> bidBrands;

    @Field(name = "是否调差合同行", desc = "是否调差合同行", defaultValue = "false")
    private Boolean adjustItem;

    @Field(name = "租赁形式")
    @DictionaryMeta(RentPeriodDict.class)
    private String leaseForm;

    @Field(name = "租赁时长")
    private BigDecimal leaseTime;


    @Field(name = "已收货含税单价(元)")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency receivedPrcWithTax;

    @Field(name = "已收货不含税单价(元)")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency receivedPrcWithoutTax;

    @Transient
    @Field(name = "已收货含税单价(元)")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency linePrcWithTax;

    @Transient
    @Field(name = "已收货不含税单价(元)")
    @CurrencyMeta(intDigits = 20, decimalDigits = 4)
    private Currency linePrcWithoutTax;

    @Field(name = "优惠系数")
    @FloatMeta(intDigits = 9, decimalDigits = 4)
    private BigDecimal discountFactor;

    // 售后字段
    @Field(name = "累计退货数量")
    @FloatMeta(intDigits = 9, decimalDigits = 4)
    private BigDecimal totalReverseQty;
    
    @Field(name = "累计退货金额")
    @FloatMeta(intDigits = 9, decimalDigits = 4)
    private Currency totalReverseAmt;
    
    @Field(name = "累计退货金额（不含税）")
    @FloatMeta(intDigits = 9, decimalDigits = 4)
    private Currency totalReverseWithoutTaxAmt;
    // 售后字段 end

    @Field(name = "命中的测算id")
    private Long salePriceCalLineId;

    @Field(name = "应收账款方案", type = FieldType.Json)
    private List<PayableSchemeTemplateBO> payableSchemeTemplates;

    @Field(name = "价格类型")
    @DictionaryMeta(SelfSpcCalTypeDict.class)
    private String priceType;

    /**
     * 辅材数量
     */
    @Field(name = "辅材数量")
    @DictionaryMeta(ItemCombinationNumTypeDict.class)
    @Transient
    private String combinationNumType;

    /**
     * 辅材数量比例
     */
    @Field(name = "辅材数量比例")
    @Transient
    private BigDecimal combinationNumRate;

    @Field(name = "租赁材料类型")
    @DictionaryMeta(RentMaterialTypeDict.class)
    private String rentMaterialType;

    /**
     * 关联会计科目
     */
    @Field(name = "关联会计科目")
    @LinkMeta
    private PmMmAccountSubjectBO pmMmAccountSubjectBO;

    /**
     * 关联WBS
     */
    @Field(name = "关联WBS")
    @LinkMeta
    private PmCmWbsBO pmCmWbsBO;

    /**
     * 关联成本科目
     */
    @Field(name = "关联成本科目")
    @LinkMeta
    private PmMmCostSubjectBO pmMmCostSubjectBO;

    /**
     * 关联施工部位
     */
    @Field(name = "关联施工部位")
    @LinkMeta
    private PmMmSubSegmentBO pmMmSubSegmentBO;

    /**
     * 单位
     */
    @Field(
            name = "单位"
    )
    private String unitName;

    /**
     * 分类路径
     */
    @Field(
            name = "分类路径"
    )
    private String materialCategoryFullPathName;

    /**
     * 内存字段 不可下单
     */
    @Field(name = "是否可下单")
    @Transient
    private Boolean cannotOrder;

    @Field(name = "历史税率(%)")
    @Transient
    private BigDecimal historyTaxRate;

    @Field(name = "合同清单行备注")
    @TextMeta(length = 1024)
    private String remark;

    public Long getSkuId() {
        if (ObjectUtil.isNotNull(this.sku)) {
            return sku.getId();
        }
        else {
            return null;
        }
    }

    /**
     * 含铜量
     */
    @Field(name = "含铜量")
    @FloatMeta(intDigits = 8, decimalDigits = 12)
    private BigDecimal copperContent;

    /**
     * 铜基价
     */
    @Field(name = "铜基价")
    @FloatMeta(intDigits = 8, decimalDigits = 4)
    private BigDecimal copperBasicPrice;

    /**
     * 延米铜价
     */
    @Field(name = "延米铜价")
    @FloatMeta(intDigits = 8, decimalDigits = 4)
    private BigDecimal yanmiCopperPrice;

    /**
     * 销售折扣系数
     */
    @Field(name = "销售折扣系数")
    @FloatMeta(intDigits = 8, decimalDigits = 4)
    private BigDecimal saleDiscountFactor;

    /**
     * 辅材价格
     */
    @Field(name = "辅材价格")
    @FloatMeta(intDigits = 8, decimalDigits = 4)
    private BigDecimal otherCosts;

}
