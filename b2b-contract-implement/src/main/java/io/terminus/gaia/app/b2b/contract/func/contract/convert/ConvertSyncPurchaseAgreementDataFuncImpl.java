package io.terminus.gaia.app.b2b.contract.func.contract.convert;

import com.google.common.collect.Lists;
import io.terminus.gaia.app.b2b.contract.dict.SapBidWayTypeDict;
import io.terminus.gaia.app.b2b.contract.model.ContractExtBO;
import io.terminus.gaia.app.b2b.item.model.CategoryBOExt;
import io.terminus.gaia.app.b2b.item.model.OutBrandBO;
import io.terminus.gaia.contract.dict.contract.ContractSourceDict;
import io.terminus.gaia.contract.dict.contract.ContractStatusDict;
import io.terminus.gaia.contract.dict.contract.ContractTypeDict;
import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.gaia.contract.model.contract.ContractDistributorBO;
import io.terminus.gaia.md.model.BrandBO;
import io.terminus.gaia.md.model.CategoryBO;
import io.terminus.gaia.md.model.CurrencyBO;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.gaia.organization.model.EmployeeBO;
import io.terminus.gaia.organization.setting.SunacSetting;
import io.terminus.trantor.module.base.model.User;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.api.annotation.typemeta.LinkMeta;
import io.terminus.trantorframework.api.type.Attachment;
import io.terminus.trantorframework.sdk.sql.DS;
import io.terminus.trantorframework.sdk.util.ModelUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 同步数据转换
 *
 * @Author: fengbo
 * @Date: 2021/9/7
 */
@Slf4j
@RequiredArgsConstructor
@FunctionImpl
public class ConvertSyncPurchaseAgreementDataFuncImpl implements ConvertSyncPurchaseAgreementDataFunc {

    private final SunacSetting sunacSetting;

    @Override
    public ContractBO execute(ContractExtBO contractExtBO) {
        ContractBO contractBO = ModelUtils.convert(contractExtBO, ContractBO.class);
        //状态默认为已创建
        contractBO.setContractStatusDict(ContractStatusDict.CREATED);
        //合同类型
        contractBO.setContractTypeDict(ContractTypeDict.PURCHASE_AGREEMENT);
        //合同主体默认为材料公司总部
        EntityBO entityBO = DS.findOne(EntityBO.class, "*", "entityCode=?", sunacSetting.getMaterialHeadquartersCode());
        contractBO.setPartyA(entityBO);
        //经办人，经办部门
        User oaUser = DS.findOne(User.class, "*", "username=?", contractExtBO.getOaAccCode());
        if (oaUser == null) {
            log.error("经办人信息缺失，sap同步的经办人编码:{}", contractExtBO.getOaAccCode());
        }
        contractBO.setOperator(oaUser);
        contractBO.setCreatedBy(oaUser);
        if(oaUser!=null){
            List<EmployeeBO> operatorDepartmentList = DS.findAll(EmployeeBO.class, "*", "user=?", oaUser.getId());
            if (CollectionUtils.isEmpty(operatorDepartmentList)) {
                log.error("经办部门信息缺失，sap同步的经办部门编码:{}", contractExtBO.getDepCode());
            }
            if (!CollectionUtils.isEmpty(operatorDepartmentList)) {
                contractBO.setOperatorDepartment(operatorDepartmentList.get(0));
            }
        }
        //货币不传，默认为人民币
        if (contractExtBO.getCurrency() == null) {
            CurrencyBO currencyBO = DS.findOne(CurrencyBO.class, "*", "currencyShortName=?", "CNY");
            contractBO.setCurrency(currencyBO);
        }
        //非清单类材料不传，默认为否
        if (contractExtBO.getIsNotBOM() == null) {
            contractBO.setIsNotBOM(Boolean.FALSE);
        }
        //限制总金额为否
        contractBO.setIsLimitTotalAmount(Boolean.FALSE);
        //合同乙方信息
        EntityBO partyB = DS.findOne(EntityBO.class, "*", "entityCode=?", contractExtBO.getPartyBCode());
        contractBO.setPartyB(partyB);
        //是否多方合同
        contractBO.setIsMultiParty(Boolean.FALSE);
        if (!StringUtils.isBlank(contractExtBO.getPartyCCode())) {
            contractBO.setIsMultiParty(Boolean.TRUE);
        }
        //招标方式
        contractBO.setBidWay(SapBidWayTypeDict.exchange(contractExtBO.getBidWay()));
        //是否补充协议,为空是主协议
        contractBO.setIsSupplementContract(Boolean.FALSE);
        if (contractExtBO.getIsSupplementContract() != null && contractExtBO.getIsSupplementContract()) {
            contractBO.setIsSupplementContract(Boolean.TRUE);
        }
        //文件信息
        //是否使用模板=否
        contractBO.setIsUseTemplate(Boolean.FALSE);
        //合同模板为空，模板信息为空
        contractBO.setContractTemplate(null);
        contractBO.setContractTemplateInfo(null);
        //合同文件
        if (!CollectionUtils.isEmpty(contractExtBO.getAttachmentTOList())) {
            Attachment contractAttachment = new Attachment();
            List<Attachment.File> files = Lists.newArrayList();
            contractExtBO.getAttachmentTOList().forEach(contractAttachmentTO -> {
                Attachment.File file = new Attachment.File();
                file.setName(contractAttachmentTO.getFileName());
                file.setUrl(contractAttachmentTO.getFileUrl());
                file.setType("pdf");
                files.add(file);
            });
            contractAttachment.setFiles(files);
            contractBO.setContractAttachment(contractAttachment);
        }
        //创建方式=同步
        contractBO.setSourceDict(ContractSourceDict.SYNC);
        //来源单据=sap单据ID
        contractBO.setSourceDocument(Long.valueOf(contractExtBO.getContractCode()));
        //来源单据编码=sap单据编码
        contractBO.setSourceCode(contractExtBO.getContractCode());
        //签约供应商信息
        if (!CollectionUtils.isEmpty(contractExtBO.getSupplierCodeList())) {
            List<EntityBO> supplierList = DS.findAll(EntityBO.class, "*", "entityCode in (?)", contractExtBO.getSupplierCodeList());
            if (CollectionUtils.isEmpty(supplierList)) {
                log.error("缺失供应商数据信息，sap同步的供应商编码:{}", contractExtBO.getSupplierCodeList());
                return contractBO;
            }
            List<ContractDistributorBO> contractDistributorList = Lists.newArrayList();
            supplierList.forEach(supplier -> {
                ContractDistributorBO contractDistributorBO = new ContractDistributorBO();
                contractDistributorBO.setContract(contractBO);
                contractDistributorBO.setPerformanceEntity(supplier);
                contractDistributorList.add(contractDistributorBO);
            });
            contractBO.setContractDistributorList(contractDistributorList);
        }
        //类目
        List<CategoryBOExt> categoryBOList =
                DS.findAll(CategoryBOExt.class, "*", "outCategoryCode=?", contractExtBO.getCategoryCode());
        if (CollectionUtils.isEmpty(categoryBOList)) {
            log.error("缺失分类数据信息，sap同步的类目编码:{}", contractExtBO.getCategoryCode());
        }
        if (!CollectionUtils.isEmpty(categoryBOList)) {
            contractBO.setCategoryBOList(ModelUtils.convert(categoryBOList, CategoryBO.class));
        }
        //品牌
        List<BrandBO> brandBOList = DS.findAll(BrandBO.class, "*", "brandName=?", contractExtBO.getBrandName());
        if (CollectionUtils.isEmpty(brandBOList)) {
            log.error("缺失品牌数据信息，sap同步的品牌名称:{}", contractExtBO.getBrandName());
        }
        if (!CollectionUtils.isEmpty(brandBOList)) {
            contractBO.setBrandBOList(brandBOList);
        }

        //sap品牌
        if (!StringUtils.isEmpty(contractExtBO.getBrandCode())) {
            OutBrandBO outBrandBO = DS.findOne(OutBrandBO.class, "*", OutBrandBO.brandCode_field + " = ?", contractExtBO.getBrandCode());
            if (outBrandBO == null) {
                log.error("缺失sap品牌数据信息，sap同步的品牌code:{}", contractExtBO.getBrandCode());
            } else {
                contractBO.set("sapBrand", outBrandBO);
            }
        }


        //处理关联协议信息
        if (StringUtils.isNotEmpty(contractExtBO.getParentContractCode()) && contractBO.getIsSupplementContract()) {
            ContractBO parentContract = DS.findOne(ContractBO.class, "*", "contractCode=?", contractExtBO.getParentContractCode());
            contractBO.setParent(parentContract);
        }
        //归档编码不存储
        contractBO.setFilingCode(null);
        return contractBO;
    }
}
