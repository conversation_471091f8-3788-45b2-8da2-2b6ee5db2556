<View title="销售价格测算" forModel="b2b_contract_SalePriceCalBO" type="Form" version="2"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="https://trantor-docs-dev.app.terminus.io/static/v0.17.x/schema/base.xsd">

    <Steps current="4">
        <Step title="选择协议" key="1" />
        <Step title="选择标品" key="2" />
        <Step title="选择区域" key="2-5" />
        <Step title="选择账期" key="3" />
        <Step title="测算价格" key="4" />
        <Step title="确认结果" key="5" />
    </Steps>

    <Record key="main">
        <Actions>
            <Action layout="Footer" type="Cancel" targetView="b2b_contract_SalePriceCalBO_SalePriceCalList"/>
            <Action layout="Footer" label="上一步"  action="GoBack"/>
            <Action type="Submit" action="#{calSel}" label="测算所选标品" layout="Footer" />
            <Action type="Submit" action="#{calAll}" label="测算全部标品" layout="Footer" />
        </Actions>
    </Record>

    <frontend_SalePriceCal key="salePriceCal" />
</View>