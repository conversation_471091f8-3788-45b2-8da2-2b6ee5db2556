package io.terminus.gaia.app.b2b.contract.func.scheme;

import io.terminus.gaia.app.b2b.contract.model.PayableSchemeTemplateBO;
import io.terminus.trantor.module.base.model.result.BooleanResult;
import io.terminus.trantorframework.api.annotation.Function;

/**
 * <AUTHOR>
 * @time 2023/11/6 11:38
 */
@Function
public interface PayableSchemeTLDeleteFunc {

    BooleanResult execute(PayableSchemeTemplateBO request);

}
