package io.terminus.gaia.app.b2b.contract.func.customermanager.read;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Throwables;
import io.terminus.gaia.app.b2b.contract.model.CustomerManageBO;
import io.terminus.gaia.app.b2b.contract.model.CustomerManageCategoryBO;
import io.terminus.gaia.app.b2b.contract.tmodel.QueryCustomerDiscountFactorTO;
import io.terminus.gaia.app.b2b.item.model.CategoryBOExt;
import io.terminus.gaia.app.b2b.trade.tmodel.BigDecimalResult;
import io.terminus.gaia.md.model.CompanyBO;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.gaia.organization.model.DepartmentBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.exception.BusinessException;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class QueryCustomerDiscountFactorFuncImpl implements QueryCustomerDiscountFactorFunc {


    private final RedisTemplate<String, String> redisTemplate;

    @Override
    public BigDecimalResult execute(QueryCustomerDiscountFactorTO request) {
        return new BigDecimalResult(null);
        // 优惠系数暂时不用
        /*if (Objects.isNull(request.getCompanyId())) {
            throw new BusinessException("公司ID不可以为空");
        }

        if (Objects.isNull(request.getCategoryId())) {
            throw new BusinessException("分类ID不可以为空");
        }

        String cacheKey = getCacheKey(request);
        BigDecimal cache = getFromCache(cacheKey);
        if (Objects.nonNull(cache)) {
            return new BigDecimalResult(cache);
        }

        CategoryBOExt category = DS.findById(CategoryBOExt.class, request.getCategoryId());
        List<Long> categoryIdList = getAncestorCategoryIds(category);
        BigDecimal discountFactor = null;

        Queue<Long> queue = new LinkedList<>();
        queue.offer(request.getCompanyId());

        while (Objects.isNull(discountFactor) && !queue.isEmpty()) {
            // 查询当前分类优惠系数
            discountFactor = getCustomerManage(queue.peek(), request.getProjectId(), categoryIdList);
            if (Objects.isNull(discountFactor)) {
                // 查询上级组织对应实体
                Optional.ofNullable(queue.poll())
                        .map(entityId -> DS.findById(EntityBO.class, entityId, "*,company.*"))
                        .map(EntityBO::getCompany).map(CompanyBO::getOrgId)
                        .map(orgId -> DS.findById(DepartmentBO.class, orgId, "*,department.*"))
                        .map(DepartmentBO::getDepartment).map(DepartmentBO::getEntity).map(EntityBO::getId)
                        .ifPresent(queue::offer);
            }
        }

        // 未配置优惠系数，查询分类上的优惠系数
        if (Objects.isNull(discountFactor)) {
            log.info("命中分类优惠系数,categoryId={}", request.getCategoryId());
            discountFactor = category.getDiscountFactor();
        }

        setCache(cacheKey, discountFactor);

        return new BigDecimalResult(discountFactor);*/
    }

    private void setCache(String cacheKey, BigDecimal discountFactor) {
        try {
            redisTemplate.opsForValue().set(cacheKey, discountFactor.toPlainString(), 10, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("设置优惠系数失败,cacheKey={},cause={}", cacheKey, Throwables.getStackTraceAsString(e));
        }
    }

    private BigDecimal getFromCache(String cacheKey) {
        BigDecimal res = null;
        try {
            String s = redisTemplate.opsForValue().get(cacheKey);
            if (StrUtil.isBlank(s)) {
                return null;
            }

            res = new BigDecimal(s);
        } catch (Exception e) {
            log.error("获取优惠系数失败,cacheKey={},cause={}", cacheKey, Throwables.getStackTraceAsString(e));
        }
        return res;
    }

    private String getCacheKey(QueryCustomerDiscountFactorTO request) {
        String cacheKey = StrUtil.format("QueryCustomerDiscountFactorFunc:{}:{}:{}", request.getCompanyId(), request.getProjectId(), request.getCategoryId());
        return cacheKey;
    }

    private BigDecimal getCustomerManage(Long companyId, Long projectId, List<Long> categoryIdList) {
        List<CustomerManageBO> customerManageList = DS.findAll(CustomerManageBO.class, "*,projectList.*,categoryList.*", "company = ?", companyId);

        CustomerManageBO customerManage = null;
        if (CollUtil.isNotEmpty(customerManageList)) {
            // 优先寻找项目适配的记录
            if (Objects.nonNull(projectId)) {
                customerManage = CollUtil.findOne(customerManageList, it -> CollUtil.contains(it.getProjectList(), prj -> projectId.equals(prj.getId())));
            }
            // 没传项目 或者 项目不适配，再寻找适用所有项目的记录
            if (Objects.isNull(customerManage)) {
                customerManage = CollUtil.findOne(customerManageList, it -> CollUtil.isEmpty(it.getProjectList()));
            }
        }

        // 查询分类上设置的优惠系数
        if (Objects.nonNull(customerManage)) {
            for (Long categoryId : categoryIdList) {
                CustomerManageCategoryBO category = CollUtil.findOne(customerManage.getCategoryList(), it -> categoryId.equals(it.getCategory().getId()));
                if (Objects.nonNull(category)) {
                    log.info("命中优惠,customerManage={}",customerManage.getId());
                    return category.getDiscountFactor();
                }
            }
        }

        return null;
    }

    private List<Long> getAncestorCategoryIds(CategoryBOExt cur) {
        List<Long> categoryIdList = new ArrayList<>();
        while (cur.getCategoryLevel() > 1) {
            categoryIdList.add(cur.getId());
            cur = DS.findById(CategoryBOExt.class, cur.getParentCategory().getId());
        }
        return categoryIdList;
    }
}
