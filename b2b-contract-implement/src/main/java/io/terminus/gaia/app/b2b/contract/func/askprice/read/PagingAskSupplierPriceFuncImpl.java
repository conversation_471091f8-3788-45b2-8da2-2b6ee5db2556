package io.terminus.gaia.app.b2b.contract.func.askprice.read;

import io.terminus.gaia.app.b2b.contract.model.AskSupplierPriceBO;
import io.terminus.gaia.app.b2b.contract.model.query.QAskSupplierPriceBO;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.gaia.md.model.query.QEntityBO;
import io.terminus.gaia.organization.context.UserCompanyContext;
import io.terminus.gaia.organization.context.UserInfoContext;
import io.terminus.gaia.organization.tmodel.CompanyInfoTO;
import io.terminus.gaia.organization.tmodel.UserInfoTO;
import io.terminus.trantorframework.Paging;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.querymodel.type.support.QLongId;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * @class_name: PagingAskSupplierPriceFuncImpl
 * @desc:
 * @author: huangjunwei
 **/
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class PagingAskSupplierPriceFuncImpl implements PagingAskSupplierPriceFunc {

    private final UserCompanyContext userCompanyContext;
    private final UserInfoContext userInfoContext;


    @Override
    public Paging<AskSupplierPriceBO> execute(QAskSupplierPriceBO qAskSupplierPriceBO) {

        UserInfoTO userInfo = userInfoContext.getUserInfo();
        EntityBO entity = userInfo.getEntity();



        qAskSupplierPriceBO.getQueryParams().getSelect().addField("projectBO.*");
        qAskSupplierPriceBO.getQueryParams().getSelect().addField("requestPurchaseBO.*");
        qAskSupplierPriceBO.getQueryParams().getSelect().addField("askPriceBO.*");

        // admin看所有的
        if (!Objects.equals(userInfo.getIsSysAdmin(),true)){
            QEntityBO qEntityBO = new QEntityBO();
            qEntityBO.setId(new QLongId(entity.getId()));
            qAskSupplierPriceBO.setSupplierEntity(qEntityBO);
        }

        return DS.paging(qAskSupplierPriceBO);
    }
}
