package io.terminus.gaia.contract;

import io.terminus.gaia.md.model.SiteBO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;

/**
 * DATE: 2021/2/26 16:23 <br>
 * MAIL: <EMAIL> <br>
 * <AUTHOR>
 */
@Slf4j
public class PartnerBaseService extends BaseService {
    protected static SiteBO store1;
    protected static SiteBO store2;

    @Before
    public void setup1() {
        if (!hasInitData) {
            log.info("start init data");
            // todo  数据初始化操作
            super.finishInit();
        }
    }

    @Test
    public void test() {

    }
}