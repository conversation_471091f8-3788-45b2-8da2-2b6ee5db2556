package io.terminus.gaia.contract.func.contract.write;

import io.terminus.gaia.contract.dict.contract.ContractStatusDict;
import io.terminus.gaia.contract.model.contract.ContractBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;

/**
 * <AUTHOR>
 */
@FunctionImpl
public class ModifyHandledContractFuncImpl implements ModifyHandledContractFunc {
    @Override
    public void execute(ContractBO handledContract) {
        ContractBO updateApply = new ContractBO();
        // 根据是否对接签署判断目标状态
        Boolean isEnableElectronicSeal = handledContract.getIsEnableElectronicSeal();
        String targetStatus = isEnableElectronicSeal ?
                ContractStatusDict.SIGNED : ContractStatusDict.SUBMITTED;

        updateApply.setId(handledContract.getId());
        updateApply.setContractStatusDict(targetStatus);

        DS.update(updateApply);
    }
}
