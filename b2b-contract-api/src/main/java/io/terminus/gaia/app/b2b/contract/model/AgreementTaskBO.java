package io.terminus.gaia.app.b2b.contract.model;

import io.terminus.trantorframework.api.BaseModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.Model;
import io.terminus.trantorframework.api.annotation.typemeta.LinkMeta;
import lombok.Data;

import java.util.Date;

/**
 * 协议任务
 * 暂时无用，协议结束时间都是23：59:59，0点的定时任务自动失效即可
 * <AUTHOR>
 */
@Data
@Model
public class AgreementTaskBO extends BaseModel<Long> {

    @Field(name = "协议")
    @LinkMeta
    private AgreementBO agreementBO;

    @Field(name = "协议名称")
    private String agreementName;

    /**
     * eg：expire_agreement 协议过期
     */
    @Field(name = "任务类型")
    private String taskType;

    @Field(name = "执行时间")
    private Date executeAt;

    @Field(name = "执行结果")
    private String executeResult;

    @Field(name = "备注")
    private String remark;
}
