package io.terminus.gaia.contract.func.approval.write;

import io.terminus.gaia.contract.tmodel.bpm.SubmitApprovalFromBusinessDataTO;
import io.terminus.gaia.contract.tmodel.bpm.SubmitApprovalResultTO;
import io.terminus.gaia.contract.tmodel.bpm.SubmitDalaranBaseTO;
import io.terminus.trantorframework.api.annotation.Function;
import io.terminus.trantorframework.api.annotation.RouteRuleMatch;

/**
 * 提交待审批数据至集成平台
 */
@Function(name = "submit BPM approval to dalaran function")
@RouteRuleMatch("submitApprovalFromBusinessDataTO.submitMethod")
public interface SubmitBPMApprovalToDalaranFunc {

    SubmitApprovalResultTO execute (SubmitApprovalFromBusinessDataTO submitApprovalFromBusinessDataTO);
}
