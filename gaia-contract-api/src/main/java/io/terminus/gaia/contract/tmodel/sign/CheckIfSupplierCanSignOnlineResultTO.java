package io.terminus.gaia.contract.tmodel.sign;

import io.terminus.trantorframework.api.RootModel;
import io.terminus.trantorframework.api.annotation.Field;
import io.terminus.trantorframework.api.annotation.TransientModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2021/10/30
 * Desc:
 */
@EqualsAndHashCode(callSuper = true)
@TransientModel(
        name = "CheckIfSupplierCanSignOnlineResultTO"
)
@Data
public class CheckIfSupplierCanSignOnlineResultTO extends RootModel<Long> {

    @Field(name = "statusMessage")
    private String statusMessage;

    @Field(name = "statusCode")
    private String statusCode;

    @Field(name = "isSupportSignOnline", desc = "是否支持线上签署")
    private Boolean isSupportSignOnline;
}
