package io.terminus.gaia.app.b2b.contract.func.askprice.write;

import io.terminus.gaia.app.b2b.contract.dict.AskPriceStatusDict;
import io.terminus.gaia.app.b2b.contract.dict.AskSupplierPriceStatusDict;
import io.terminus.gaia.app.b2b.contract.model.*;
import io.terminus.gaia.app.b2b.contract.tmodel.AgreementBrandTO;
import io.terminus.gaia.md.model.BrandBO;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.gaia.organization.context.UserInfoContext;
import io.terminus.platform.sdk.transaction.DSTransaction;
import io.terminus.trantorframework.api.RootModel;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 创建询价单
 *
 * <AUTHOR>
 */
@Slf4j
@FunctionImpl
@RequiredArgsConstructor
public class CreateAskPriceFuncImpl implements CreateAskPriceFunc {

    private final UserInfoContext userInfoContext;

    /**
     * 第1轮询价、报价
     * */
    private static Integer ROUND_TIME_1 = 1;

    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");

    @Override
    @DSTransaction
    public AskPriceBO execute(AskPriceTO askPriceTO) {
        log.info("生成询价单CreateAskPriceFuncImpl，入参:{}", askPriceTO);
        //入参TO转BO
        /**  步骤1.创建运营端询价单  ***/
        AskPriceBO askPriceBO = new AskPriceBO();
        askPriceBO.setId(DS.nextId(AskPriceBO.class));
        askPriceBO.setRequestPurchaseBO(askPriceTO.getRequestPurchaseBO());
        //询价单名称：20250707ABC项目
        askPriceBO.setName(dateFormat.format(new Date()) + askPriceTO.getProjectBO().getSiteName());
        askPriceBO.setStatus(AskPriceStatusDict.PRICE_WRITING);//默认：报价中
        askPriceBO.setCategoryBO(askPriceTO.getCategoryBO());
        askPriceBO.setCategoryName(askPriceTO.getCategoryBO().getCategoryName());
        askPriceBO.setProjectBO(askPriceTO.getProjectBO());
        askPriceBO.setProjectName(askPriceTO.getProjectBO().getSiteName());
        //项目方的部门信息、子企业名称
        askPriceBO.setDepartmentBO(askPriceTO.getDepartmentBO());
        askPriceBO.setEnterpriseName(askPriceTO.getEnterpriseName());
        //铜基价
        askPriceBO.setBasePriceWithTax(askPriceTO.getBasePriceWithTax());
        //品牌列表、供应商列表
        List<AgreementBrandTO> agreementBrandTOList = askPriceTO.getAgreementBrandTOList();
        if (CollectionUtils.isNotEmpty(agreementBrandTOList)) {
            List<BrandBO> brandBOList = new ArrayList<>();
            List<EntityBO> entityBOList = new ArrayList<>();
            agreementBrandTOList.stream().map(item -> {
                brandBOList.add(item.getBrandBO());
                entityBOList.add(item.getYfCompanyBO());
                return null;
            }).collect(Collectors.toList());
            askPriceBO.setBrandBOList(brandBOList);
            askPriceBO.setEntityBOList(entityBOList);
        }
        askPriceBO.setCurrentRound(ROUND_TIME_1);
        DS.create(askPriceBO);

        /** 创建询价单明细清单 **/
        //根据需求池id，查需求池清单的明细，按spuId去重
        List<RequestPurchaseLineBO> requestPurchaseLineBOList = DS.findAll(RequestPurchaseLineBO.class, "*, spu.*", "requestPurchaseBO = ?", askPriceTO.getRequestPurchaseBO().getId());
        requestPurchaseLineBOList = requestPurchaseLineBOList.stream().
                collect(Collectors.collectingAndThen(
                        Collectors.toCollection(
                                () -> new TreeSet<>(Comparator.comparing(item -> item.getSpu().getId()))), ArrayList::new
        ));
        List<AskPriceLineBO> askPriceLineBOList = new ArrayList<>();
        for (int i = 0; i < requestPurchaseLineBOList.size(); i++) {
            RequestPurchaseLineBO requestPurchaseLineBO = requestPurchaseLineBOList.get(i);
            if (requestPurchaseLineBO == null) {
                continue;
            }
            AskPriceLineBO askPriceLineBO = new AskPriceLineBO();
            askPriceLineBO.setId(DS.nextId(AskPriceLineBO.class));
            askPriceLineBO.setRequestPurchaseBO(askPriceTO.getRequestPurchaseBO());
            askPriceLineBO.setAskPriceBO(askPriceBO);
            askPriceLineBO.setRound(ROUND_TIME_1);
            //标品和物料
            askPriceLineBO.setMaterialName(requestPurchaseLineBO.getNeedLineName());
            askPriceLineBO.setThingSizeDesc(requestPurchaseLineBO.getThingSizeDesc());
            askPriceLineBO.setNeedNum(requestPurchaseLineBO.getNeedNum());
            askPriceLineBO.setUnit(requestPurchaseLineBO.getUnit());
            askPriceLineBO.setSpu(requestPurchaseLineBO.getSpu());
            askPriceLineBO.setSpuCode(requestPurchaseLineBO.getSpu().getSpuCode());
            askPriceLineBO.setSpuName(requestPurchaseLineBO.getSpu().getName());
            //含铜量
            askPriceLineBO.setRawMaterialContent(requestPurchaseLineBO.getPurRawMaterialContent());
            //铜基价
            askPriceLineBO.setPurCopperBasicPrice(requestPurchaseLineBO.getPurCopperBasicPrice());
            //延米铜价
            askPriceLineBO.setPurCopperPrice(requestPurchaseLineBO.getPurCopperPrice());
            askPriceLineBOList.add(askPriceLineBO);
        }
        DS.create(askPriceLineBOList);

        /**  步骤3.创建供应商报价单、供应商报价清单明细  **/
        for (int i = 0; i < agreementBrandTOList.size(); i++) {
            //2.1 创建供应商报价单
            AgreementBrandTO agreementBrandTO = agreementBrandTOList.get(i);
            AskSupplierPriceBO askSupplierPriceBO = new AskSupplierPriceBO();
            askSupplierPriceBO.setId(DS.nextId(AskSupplierPriceBO.class));
            askSupplierPriceBO.setRequestPurchaseBO(askPriceTO.getRequestPurchaseBO());
            askSupplierPriceBO.setAskPriceBO(askPriceBO);
            askSupplierPriceBO.setSupplierEntity(agreementBrandTO.getYfCompanyBO());
            askSupplierPriceBO.setStatus(AskSupplierPriceStatusDict.PRICE_DOING);//默认状态为报价中
            askSupplierPriceBO.setCurrentRound(ROUND_TIME_1);
            if (userInfoContext != null && userInfoContext.getUserInfo() != null) {
                askSupplierPriceBO.setCreatedBy(userInfoContext.getUserInfo().getUser());
            }
            DS.create(askSupplierPriceBO);
            //2.2 创建供应商报价单明细（每个品牌/一个协议/一个供应商/报价单下，有报价明细清单）
            //根据入参，查需要供应商报价的需求明细明细清单
            List<RequestPurchaseLineBO> rpLineBOForSuppliers = Optional.ofNullable(agreementBrandTO)
                    .map(AgreementBrandTO::getRequestPurchaseLineBOList)
                    .orElse(Collections.emptyList());
            if (CollectionUtils.isEmpty(rpLineBOForSuppliers)) {
                continue;
            }
            List<Long> rpLineBOIds = rpLineBOForSuppliers.stream().filter(Objects::nonNull)
                    .map(RootModel::getId)
                    .collect(Collectors.toList());
            List<RequestPurchaseLineBO> requestPurchaseLineBOs = DS.findAll(RequestPurchaseLineBO.class , "*, unit.*", "id in (?)", rpLineBOIds);
            //根据需要供应商报价的需求明细明细清单，设置供应商报价清单
            List<AskSupplierPriceLineBO> askSupplierPriceLineBOs = new ArrayList<>();
            for (int j = 0; j < requestPurchaseLineBOs.size(); j++) {
                RequestPurchaseLineBO requestPurchaseLineBO = requestPurchaseLineBOs.get(j);
                AskSupplierPriceLineBO askSupplierPriceLineBO = new AskSupplierPriceLineBO();
                askSupplierPriceLineBO.setId(DS.nextId(AskSupplierPriceLineBO.class));
                askSupplierPriceLineBO.setRequestPurchaseBO(askPriceTO.getRequestPurchaseBO());
                askSupplierPriceLineBO.setAskPriceBO(askPriceBO);
                //根据需求明细单的标品，匹配运营端的询价清单行
                Optional<AskPriceLineBO> askPriceLineBOOptional = Optional.ofNullable(askPriceLineBOList).orElse(Collections.emptyList())
                        .stream()
                        .filter(item -> item.getSpu().getId().equals(requestPurchaseLineBO.getSpu().getId()))
                        .findFirst();
                askSupplierPriceLineBO.setAskPriceLineBO(askPriceLineBOOptional.get());
                //供应商报价单
                askSupplierPriceLineBO.setAskSupplierPriceBO(askSupplierPriceBO);
                askSupplierPriceLineBO.setSupplierEntity(agreementBrandTO.getYfCompanyBO());
//                askSupplierPriceLineBO.setBrandBO(agreementBrandTO.getBrandBO());
//                askSupplierPriceLineBO.setCategoryBO(askPriceTO.getCategoryBO());
//                askSupplierPriceLineBO.setAgreementBO(agreementBrandTO.getAgreementBO());
                askSupplierPriceLineBO.setRound(ROUND_TIME_1);
                //材料名称
//                askSupplierPriceLineBO.setThingSizeDesc(requestPurchaseLineBO.getThingSizeDesc());
//                askSupplierPriceLineBO.setSpu(requestPurchaseLineBO.getSpu());
//                askSupplierPriceLineBO.setNeedNum(requestPurchaseLineBO.getNeedNum());
//                askSupplierPriceLineBO.setUnit(requestPurchaseLineBO.getUnit());
//                askSupplierPriceLineBO.setUnitName("米");//通过单位查找到计量单位名称
                /** 价格相关  **/
//                //含铜量
//                askSupplierPriceLineBO.setRawMaterialContent(requestPurchaseLineBO.getPurRawMaterialContent() != null ? requestPurchaseLineBO.getPurRawMaterialContent() : new BigDecimal("0"));
//                //延米铜价
//                askSupplierPriceLineBO.setPurCopperPrice(requestPurchaseLineBO.getPurCopperPrice() != null ? requestPurchaseLineBO.getPurCopperPrice() : new BigDecimal("0"));
//                //折扣系数为1，不允许供应商修改
//                askSupplierPriceLineBO.setPurDiscountFactor(new BigDecimal("1"));
//                //辅材单价（需要反向计算出辅材单价）
//                askSupplierPriceLineBO.setPurOtherCosts(requestPurchaseLineBO.getPurOtherCosts() != null ? requestPurchaseLineBO.getPurOtherCosts() : new BigDecimal("0"));
                //单价
                if (requestPurchaseLineBO.getPurTaxPrice() != null) {
                    askSupplierPriceLineBO.setPurTaxPrice(requestPurchaseLineBO.getPurTaxPrice());
                }
                BigDecimal lineAmountWithTax = new BigDecimal("0");//一行的合价默认为0 todo
                askSupplierPriceLineBO.setLineAmountWithTax(lineAmountWithTax);
                askSupplierPriceLineBOs.add(askSupplierPriceLineBO);
            }
            DS.create(askSupplierPriceLineBOs);
        }

        return askPriceBO;
    }
}
