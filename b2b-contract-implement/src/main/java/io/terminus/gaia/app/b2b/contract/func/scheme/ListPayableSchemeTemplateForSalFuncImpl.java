package io.terminus.gaia.app.b2b.contract.func.scheme;

import cn.hutool.core.collection.CollUtil;
import io.terminus.gaia.app.b2b.contract.model.PayableSchemeTemplateBO;
import io.terminus.gaia.app.b2b.contract.model.price.SalePriceCalLineBO;
import io.terminus.gaia.app.b2b.contract.model.price.query.QSalePriceCalBO;
import io.terminus.trantorframework.api.annotation.FunctionImpl;
import io.terminus.trantorframework.sdk.sql.DS;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@FunctionImpl
public class ListPayableSchemeTemplateForSalFuncImpl implements ListPayableSchemeTemplateForSalFunc{
    @Override
    public List<PayableSchemeTemplateBO> execute(QSalePriceCalBO qSalePriceCalBO) {
        List<SalePriceCalLineBO> salePriceCalLineBOList = DS.findAll(SalePriceCalLineBO.class, "*, payableSchemeTemplateBO.*", "salePriceCalBO = ?", qSalePriceCalBO.getId().getValue());
        if (CollUtil.isNotEmpty(salePriceCalLineBOList)) {
            List<PayableSchemeTemplateBO> payableSchemeTemplateBOList = salePriceCalLineBOList.stream().map(SalePriceCalLineBO::getPayableSchemeTemplateBO).collect(Collectors.toList());
            List<PayableSchemeTemplateBO> result = payableSchemeTemplateBOList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(PayableSchemeTemplateBO::getId, obj -> obj, (existing, replacement) -> existing))
                    .values()
                    .stream()
                    .collect(Collectors.toList());
            return result;
        }
        return Collections.emptyList();
    }
}
