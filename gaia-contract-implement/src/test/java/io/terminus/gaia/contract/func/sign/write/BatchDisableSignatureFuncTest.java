package io.terminus.gaia.contract.func.sign.write;

import io.terminus.gaia.contract.BaseService;
import io.terminus.gaia.contract.dict.sign.SignatureStatusDict;
import io.terminus.gaia.contract.dict.sign.SignatureTypeDict;
import io.terminus.gaia.contract.model.sign.SignatureBO;
import io.terminus.gaia.md.dict.EntityRoleStatusDict;
import io.terminus.gaia.md.dict.EntityRoleTypeDict;
import io.terminus.gaia.md.model.EntityBO;
import io.terminus.gaia.md.model.EntityRoleBO;
import io.terminus.trantorframework.sdk.sql.DS;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 批量停用电子签名 单测
 *
 * <AUTHOR>
 */
public class BatchDisableSignatureFuncTest extends BaseService {
    private BatchDisableSignatureFunc batchDisableSignatureFunc;
    private List<SignatureBO> signatureList;

    @Before
    public void init(){
        signatureList = new ArrayList<>();
        EntityBO entityBO = new EntityBO();
        entityBO.setId(1234L);
        EntityRoleBO entityRoleBO = new EntityRoleBO();
        entityRoleBO.setEntity(entityBO);
        entityRoleBO.setRoleCode("TEST_CODE");
        entityRoleBO.setRoleType(EntityRoleTypeDict.MEMBERS);
        entityRoleBO.setRoleStatus(EntityRoleStatusDict.ENABLED);
        Long entityRoleId = DS.create(entityRoleBO).getValue().longValue();
        entityRoleBO = DS.findById(EntityRoleBO.class,entityRoleId);

        int time = 3;
        while(time-->0){
            SignatureBO signatureBO = new SignatureBO();
            signatureBO.setName("测试名称");
            signatureBO.setEntityBO(entityBO);
            signatureBO.setSignatureTypeDict(SignatureTypeDict.SIGNATURE);
            signatureBO.setRemarks("测试备注");
            signatureBO.setApplyDate(new Date());
            signatureBO.setSignatureStatusDict(SignatureStatusDict.ENABLED);
            long id = DS.create(signatureBO).getValue().longValue();
            signatureBO = DS.findById(SignatureBO.class,id);
            signatureList.add(signatureBO);
        }

    }

    @Test
    public void testBatchDisableSignatureFunc() {
        signatureList.forEach(node->{
            node.setSignatureStatusDict(SignatureStatusDict.ENABLED);
            DS.update(node);
        });
        batchDisableSignatureFunc.execute(signatureList);
        signatureList.forEach(node->{
            node = DS.findById(SignatureBO.class,node.getId());
            Assert.assertEquals(node.getSignatureStatusDict(), SignatureStatusDict.STOPPED);
        });
    }
}
